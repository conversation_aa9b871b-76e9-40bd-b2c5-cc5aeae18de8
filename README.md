# **EASELEARNING GAMES**
- ## Project setup
  ```
  yarn install
  ```
  ----
- ## Offline server
  ```
  yarn serve:offline
  ```
  **IMPORTANT:** URL must have random identificator to load the game
  > *Ejemplo:* localhost:8080`/?id=1`

  **1. Games names:** <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>zle, <PERSON><PERSON>ords, DoubleOrNothing.

  **2. Choose game:** Go to you *offlineData.json* and change `normalizedName` value.

----
- ## Version de NodeJS
  ```
    Versión utilizada v14.0.0
  ```

- ## Compiles and hot-reloads for development
  ```
  yarn serve
  ```

- ## Compiles and minifies for production
  ```
  yarn build
  ```

- ## Run your unit tests
  ```
  yarn test:unit
  ```

- ## Lints and fixes files
  ```
  yarn lint
  ```

- ## Customize configuration
  See [Configuration Reference](https://cli.vuejs.org/config/).

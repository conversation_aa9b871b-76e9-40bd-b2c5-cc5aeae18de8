import FillGapsModule from '@/store/FillGapsModule';
import { createLocalVue } from '@vue/test-utils';
import Vuex from 'vuex';

describe('FillGapsModule', () => {
  let localVue;
  let store;

  beforeAll(() => {
    localVue = createLocalVue();
    localVue.use(Vuex);

    store = new Vuex.Store({ ...FillGapsModule });
    const questions = [
      {
        id: 1,
        text: ' Ipsum nisi perspiciatis. [id1-drag] Aut voluptates aperiam. [id2-drag] Quam aliquam corrupti voluptatem dolores sunt minus in.',
        answers: [
          { id: 11, answer: 'Respuesta 1.1' },
          { id: 12, answer: 'Respuesta 1.2' },
          { id: 13, answer: 'Respuesta 1.3' },
        ],
      },
      {
        id: 2,
        text: ' Ipsum nisi perspiciatis. [id1-drag] Aut voluptates aperiam. [id2-drag] Quam aliquam corrupti voluptatem dolores sunt minus in.',
        answers: [
          { id: 21, answer: 'Respuesta 2.1' },
          { id: 22, answer: 'Respuesta 2.2' },
          { id: 23, answer: 'Respuesta 2.3' },
        ],
      },
    ];
    store.commit('SET_QUESTIONS', questions);
  });

  describe('Número de fallos y aciertos', () => {
    test('Cuando hay una respuesta con todo correcto', () => {
      const answers = [
        {
          questionId: 1,
          time: 5,
          attempts: [
            { id: 11, word: 'Respuesta 1.1', correct: true },
            { id: 12, word: 'Respuesta 1.2', correct: true },
          ],
        },
      ];

      const expectedResults = [
        { type: 'correct', value: 2 },
        { type: 'incorrect', value: 0 },
      ];

      store.commit('SET_ANSWERS', answers);
      const results = store.getters.getAnswersResults();

      expect(results.length).toBe(2);
      expect(results).toEqual(expectedResults);
    });

    test('Cuando hay una respuesta con todo fallado', () => {
      const answers = [
        {
          questionId: 1,
          time: 5,
          attempts: [
            { id: 11, word: 'Respuesta 1.1', correct: false },
            { id: 12, word: 'Respuesta 1.2', correct: false },
          ],
        },
      ];

      const expectedResults = [
        { type: 'correct', value: 0 },
        { type: 'incorrect', value: 2 },
      ];

      store.commit('SET_ANSWERS', answers);
      const results = store.getters.getAnswersResults();

      expect(results.length).toBe(2);
      expect(results).toEqual(expectedResults);
    });

    test('Cuando hay una respuesta con fallos y aciertos', () => {
      const answers = [
        {
          questionId: 1,
          time: 5,
          attempts: [
            { id: 11, word: 'Respuesta 1.1', correct: false },
            { id: 12, word: 'Respuesta 1.2', correct: true },
          ],
        },
      ];

      const expectedResults = [
        { type: 'correct', value: 1 },
        { type: 'incorrect', value: 1 },
      ];

      store.commit('SET_ANSWERS', answers);
      const results = store.getters.getAnswersResults();

      expect(results.length).toBe(2);
      expect(results).toEqual(expectedResults);
    });

    test('Cuando hay varios intentos con solo fallos', () => {
      const answers = [
        {
          questionId: 1,
          time: 5,
          attempts: [
            { id: 11, word: 'Respuesta 1.1', correct: false },
            { id: 12, word: 'Respuesta 1.2', correct: false },
            { id: 11, word: 'Respuesta 1.1', correct: false },
            { id: 12, word: 'Respuesta 1.2', correct: false },
          ],
        },
      ];

      const expectedResults = [
        { type: 'correct', value: 0 },
        { type: 'incorrect', value: 4 },
      ];

      store.commit('SET_ANSWERS', answers);
      const results = store.getters.getAnswersResults();

      expect(results.length).toBe(2);
      expect(results).toEqual(expectedResults);
    });

    test('Cuando hay varios intentos con fallos y aciertos', () => {
      const answers = [
        {
          questionId: 1,
          time: 5,
          attempts: [
            { id: 11, word: 'Respuesta 1.1', correct: true },
            { id: 12, word: 'Respuesta 1.2', correct: false },
            { id: 11, word: 'Respuesta 1.1', correct: true },
            { id: 12, word: 'Respuesta 1.2', correct: true },
          ],
        },
      ];

      const expectedResults = [
        { type: 'correct', value: 2 },
        { type: 'incorrect', value: 1 },
      ];

      store.commit('SET_ANSWERS', answers);
      const results = store.getters.getAnswersResults();

      expect(results.length).toBe(2);
      expect(results).toEqual(expectedResults);
    });

    test('Cuando hay varias respuestas con varios intentos con fallos y aciertos', () => {
      const answers = [
        {
          questionId: 1,
          time: 5,
          attempts: [
            { id: 11, word: 'Respuesta 1.1', correct: true },
            { id: 12, word: 'Respuesta 1.2', correct: false },
            { id: 11, word: 'Respuesta 1.1', correct: true },
            { id: 12, word: 'Respuesta 1.2', correct: true },
          ],
        },
        {
          questionId: 1,
          time: 5,
          attempts: [
            { id: 21, word: 'Respuesta 2.1', correct: true },
            { id: 22, word: 'Respuesta 2.2', correct: false },
            { id: 21, word: 'Respuesta 2.1', correct: true },
            { id: 22, word: 'Respuesta 2.2', correct: true },
          ],
        },
      ];

      const expectedResults = [
        { type: 'correct', value: 4 },
        { type: 'incorrect', value: 2 },
      ];

      store.commit('SET_ANSWERS', answers);
      const results = store.getters.getAnswersResults();

      expect(results.length).toBe(2);
      expect(results).toEqual(expectedResults);
    });
  });
});

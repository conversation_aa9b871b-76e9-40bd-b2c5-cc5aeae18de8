import WordleModule from '@/store/WordleModule';
import { createLocalVue } from '@vue/test-utils';
import Vuex from 'vuex';

describe('WordleModule', () => {
  let localVue;
  let store;

  beforeAll(() => {
    localVue = createLocalVue();
    localVue.use(Vuex);

    store = new Vuex.Store({ ...WordleModule });
    const questions = [
      {
        id: 1,
        question: '¿Pregunta 1?',
        letters: 4,
        time: 300
      },
      {
        id: 2,
        question: '¿Pregunta 2?',
        letters: 4,
        time: 300
      },
    ];
    store.commit('SET_QUESTIONS', questions);
  });

  describe('Número de fallos y aciertos', () => {
    test('Cuando hay una respuesta con todo correcto', () => {
      const answers = [
        {
          questionId: 1,
          attempts: [
            {
              letters: [
                { letter: 'h', correct: true, present: true },
                { letter: 'o', correct: true, present: true },
                { letter: 'l', correct: true, present: true },
                { letter: 'a', correct: true, present: true }
              ],
              time: 5
            }
          ]
        },
      ];

      const expectedResults = [
        { type: 'correct', value: 1 },
        { type: 'incorrect', value: 0 },
      ];

      store.commit('SET_ANSWERS', answers);
      const results = store.getters.getAnswersResults();

      expect(results.length).toBe(2);
      expect(results).toEqual(expectedResults);
    });

    test('Cuando hay una respuesta con todo fallado', () => {
      const answers = [
        {
          questionId: 1,
          attempts: [
            {
              letters: [
                { letter: 'h', correct: false, present: false },
                { letter: 'o', correct: false, present: false },
                { letter: 'l', correct: false, present: false },
                { letter: 'a', correct: false, present: false }
              ],
              time: 5
            }
          ]
        },
      ];

      const expectedResults = [
        { type: 'correct', value: 0 },
        { type: 'incorrect', value: 1 },
      ];

      store.commit('SET_ANSWERS', answers);
      const results = store.getters.getAnswersResults();

      expect(results.length).toBe(2);
      expect(results).toEqual(expectedResults);
    });

    test('Cuando hay una respuesta con fallos y aciertos', () => {
      const answers = [
        {
          questionId: 1,
          attempts: [
            {
              letters: [
                { letter: 'h', correct: true, present: true },
                { letter: 'o', correct: false, present: false },
                { letter: 'l', correct: true, present: true },
                { letter: 'a', correct: false, present: true }
              ],
              time: 5
            }
          ]
        },
      ];

      const expectedResults = [
        { type: 'correct', value: 0 },
        { type: 'incorrect', value: 1 },
      ];

      store.commit('SET_ANSWERS', answers);
      const results = store.getters.getAnswersResults();

      expect(results.length).toBe(2);
      expect(results).toEqual(expectedResults);
    });

    test('Cuando hay varios intentos con solo fallos', () => {
      const answers = [
        {
          questionId: 1,
          attempts: [
            {
              letters: [
                { letter: 'h', correct: true, present: true },
                { letter: 'o', correct: false, present: false },
                { letter: 'l', correct: true, present: true },
                { letter: 'a', correct: false, present: true }
              ],
              time: 5
            },
            {
              letters: [
                { letter: 'h', correct: true, present: true },
                { letter: 'o', correct: false, present: false },
                { letter: 'l', correct: true, present: true },
                { letter: 'a', correct: false, present: true }
              ],
              time: 10
            }
          ]
        },
      ];

      const expectedResults = [
        { type: 'correct', value: 0 },
        { type: 'incorrect', value: 2 },
      ];

      store.commit('SET_ANSWERS', answers);
      const results = store.getters.getAnswersResults();

      expect(results.length).toBe(2);
      expect(results).toEqual(expectedResults);
    });

    test('Cuando hay varios intentos con fallos y aciertos', () => {
      const answers = [
        {
          questionId: 1,
          attempts: [
            {
              letters: [
                { letter: 'h', correct: true, present: true },
                { letter: 'o', correct: false, present: false },
                { letter: 'l', correct: true, present: true },
                { letter: 'a', correct: false, present: true }
              ],
              time: 5
            },
            {
              letters: [
                { letter: 'h', correct: true, present: true },
                { letter: 'o', correct: true, present: true },
                { letter: 'l', correct: true, present: true },
                { letter: 'a', correct: true, present: true }
              ],
              time: 10
            }
          ]
        },
      ];

      const expectedResults = [
        { type: 'correct', value: 1 },
        { type: 'incorrect', value: 1 },
      ];

      store.commit('SET_ANSWERS', answers);
      const results = store.getters.getAnswersResults();

      expect(results.length).toBe(2);
      expect(results).toEqual(expectedResults);
    });

    test('Cuando hay varias respuestas con varios intentos con fallos y aciertos', () => {
      const answers = [
        {
          questionId: 1,
          attempts: [
            {
              letters: [
                { letter: 'h', correct: true, present: true },
                { letter: 'o', correct: false, present: false },
                { letter: 'l', correct: true, present: true },
                { letter: 'a', correct: false, present: true }
              ],
              time: 5
            },
            {
              letters: [
                { letter: 'h', correct: true, present: true },
                { letter: 'o', correct: true, present: true },
                { letter: 'l', correct: true, present: true },
                { letter: 'a', correct: true, present: true }
              ],
              time: 10
            }
          ]
        },
        {
          questionId: 2,
          attempts: [
            {
              letters: [
                { letter: 'h', correct: true, present: true },
                { letter: 'o', correct: false, present: false },
                { letter: 'l', correct: true, present: true },
                { letter: 'a', correct: false, present: true }
              ],
              time: 5
            },
            {
              letters: [
                { letter: 'h', correct: true, present: true },
                { letter: 'o', correct: true, present: true },
                { letter: 'l', correct: true, present: true },
                { letter: 'a', correct: true, present: true }
              ],
              time: 10
            }
          ]
        },
      ];

      const expectedResults = [
        { type: 'correct', value: 2 },
        { type: 'incorrect', value: 2 },
      ];

      store.commit('SET_ANSWERS', answers);
      const results = store.getters.getAnswersResults();

      expect(results.length).toBe(2);
      expect(results).toEqual(expectedResults);
    });
  });
});

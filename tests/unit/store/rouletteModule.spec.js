import RouletteModule from '@/store/RouletteModule';
import { createLocalVue } from '@vue/test-utils';
import Vuex from 'vuex';

describe('RouletteModule', () => {
  let localVue;
  let store;

  beforeAll(() => {
    localVue = createLocalVue();
    localVue.use(Vuex);

    store = new Vuex.Store({ ...RouletteModule });
    const questions = [
      { id: 1, question: 'question 1', answers: [{ id: 1, answer: 'answer 1' }, { id: 2, answer: 'answer 2' }] },
      { id: 2, question: 'question 2', answers: [{ id: 3, answer: 'answer 3' }, { id: 4, answer: 'answer 4' }] },
      { id: 3, question: 'question 3', answers: [{ id: 5, answer: 'answer 5' }, { id: 6, answer: 'answer 6' }] },
    ];
    store.commit('SET_QUESTIONS', questions);
  });

  describe('Preguntas sin responder', () => {
    test('Cuando todas las preguntas estan sin responder', () => {
      const questions = store.getters.getUnansweredQuestions();
      expect(questions.length).toBe(3);
    });
  });

  describe('Preguntas con respuestas vacías', () => {
    beforeAll(() => {
      const newAnswer = { id: null, questionId: 1, correct: false };
      store.commit('ADD_ANSWER', newAnswer);
    });

    test('Cuando se añade una respuesta', () => {
      expect(store.state.answers.length).toBe(1);
    });

    test('Cuando una pregunta tiene una respuesta vacía', () => {
      const questions = store.getters.getEmptyAnsweredQuestions();
      expect(questions.length).toBe(1);
    });
  });

  describe('Preguntas con respuestas incorrectas', () => {
    const wrongAnswer = { id: 1, questionId: 1, correct: false };
    beforeEach(() => {
      store.commit('ADD_ANSWER', wrongAnswer);
    });

    test('Cuando se falla una respuesta', () => {
      const questions = store.getters.getFailedAnsweredQuestions();
      expect(questions.length).toBe(1);
      expect(questions[0].id).toBe(wrongAnswer.questionId);
    });

    test('Cuando se contesta correctamente una pregunta fallada', () => {
      const correctAnswer = { id: 1, questionId: 1, correct: true };
      store.commit('ADD_ANSWER', correctAnswer);

      const questions = store.getters.getFailedAnsweredQuestions();
      expect(questions.length).toBe(0);
    });

    test('Cuando se vuelve a fallar una respuesta correcta', () => {
      store.commit('ADD_ANSWER', wrongAnswer);

      const questions = store.getters.getFailedAnsweredQuestions();
      expect(questions.length).toBe(1);
      expect(questions[0].id).toBe(wrongAnswer.questionId);
    });
  });

  describe('Pregunta aleatoria', () => {
    beforeEach(() => {
      store.state.questions = [
        { id: 1, question: 'question 1', answers: [{ id: 1, answer: 'answer 1' }, { id: 2, answer: 'answer 2' }] },
        { id: 2, question: 'question 2', answers: [{ id: 3, answer: 'answer 3' }, { id: 4, answer: 'answer 4' }] },
      ];

      const answers = [
        { id: 1, questionId: 1, correct: true },
        { id: 2, questionId: 2, correct: true },
      ];
      answers.forEach((answer) => store.commit('ADD_ANSWER', answer));
    });

    test('Cuando no quedan ni preguntas sin contestar ni mal contestadas', () => {
      const randomQuestions = [];
      for (let i = 0; i < 10; i += 1) {
        const question = store.getters.getRandomQuestion();
        randomQuestions.push(question);
      }

      store.state.questions.forEach((question) => {
        expect(randomQuestions).toContain(question);
      });
    });
  });

  describe('Caso especial con una pregunta', () => {
    beforeEach(() => {
      store.state.questions = [
        {
          id: 3783, question: '¿Para qué sirven las dinámicas de rompe hielo?', image: '61f8349a7c62e523013890.jpg', imageUrl: 'uploads/images/question//61f8349a7c62e523013890.jpg', answers: [{ id: 12845, answer: 'Reducen la tensión y vergüenza inicial.' }, { id: 12846, answer: 'Desarrollan un sentimiento de diversión' }], random: true, time: 20,
        },
      ];
    });

    test('Cuando cargan la misma pregunta respondida justo antes', () => {
      const question1 = store.getters.getRandomQuestion();
      expect(question1.answers.length).toBe(2);

      const correctAnswer = { id: 1, questionId: 3783, correct: true };
      store.commit('ADD_ANSWER', correctAnswer);

      const question2 = store.getters.getRandomQuestion();
      expect(question2.answers.length).toBe(2);
    });
  });
});

// eslint-disable-next-line import/no-extraneous-dependencies
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');
const { DEFAULT_CLIENT } = require('./scripts/compile-utils');

const packageJson = fs.readFileSync('./package.json');
const packageVersion = JSON.parse(packageJson).version || 0;

const loadClientEnvironmentValues = () => {
  if (!('VUE_APP_CLIENT' in process.env)) {
    dotenv.populate(process.env, { VUE_APP_CLIENT: DEFAULT_CLIENT });
  }

  dotenv.populate(process.env, { VUE_APP_VERSION: packageVersion });
};

loadClientEnvironmentValues();

module.exports = {
  publicPath: process.env.BASE_URL,
  productionSourceMap: process.env.NODE_ENV !== 'production',
  outputDir: `builds_games/${process.env.VUE_APP_CLIENT}/${process.env.OUTPUT_DIR}`,
  lintOnSave: true,

  configureWebpack: {
    devtool: process.env.NODE_ENV !== 'production' ? 'eval-source-map' : false,
    output: {
      filename: '[name].[hash].js',
      chunkFilename: 'js/[name].[hash].js',
    },
  },
  chainWebpack: (config) => {
    if (!['testing', 'offline'].includes(process.env.NODE_ENV)) {
      config.module
        .rule('exclude-mocks')
        .test(path.resolve(__dirname, 'src/mocks'))
        .use('null-loader')
        .loader('null-loader');

      config.module
        .rule('exclude-mirage')
        .test(/node_modules\/miragejs\//)
        .use('null-loader')
        .loader('null-loader');
    }

    const clientName = process.env.VUE_APP_CLIENT;
    const clientsDir = path.resolve(__dirname, 'src/clients');
    const clientFolders = fs.readdirSync(clientsDir);
    const selectedClientFolders = clientFolders.filter((folder) => folder !== clientName);
    selectedClientFolders.forEach((folder) => {
      config.module
        .rule('exclude-client')
        .test(path.resolve(__dirname, `src/clients/${folder}`))
        .use('null-loader')
        .loader('null-loader');
    });
  },

  css: {
    loaderOptions: {
      sass: {
        prependData: `
          @import "@/assets/styles/_global.scss";
          @import "@/clients/${process.env.VUE_APP_CLIENT}/assets/styles/_global.scss";
        `,
      },
    },
  },

  devServer: {
    disableHostCheck: true,
    proxy: {
      '/api': {
        target: 'https://127.0.0.1:8000/',
      },
    },
  },

  pluginOptions: {
    i18n: {
      locale: 'es',
      fallbackLocale: 'es',
      localeDir: 'locales',
      enableInSFC: false,
    },
  },
};

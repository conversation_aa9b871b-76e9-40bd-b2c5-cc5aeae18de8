const { spawn } = require('child_process');

const FORMATTED_ENVS = ['staging', 'production', 'dev', 'offline'];
const CLIENT_TITLES = Object.freeze({
  easylearning: 'Easylearning',
  iberostar_new: 'Iberostar Campus',
  iberostar_old: 'Iberostar Campus',
  bedisruptive: 'BeDisruptive',
  dacsa: 'Dacsa Campus',
  earlycare: 'eEarlyCare-T',
  ecovalores: 'Ecovalores',
  emprende: 'E-mprende',
  'green-academy': 'Green Academy',
  greeneco: 'GreenEco',
  holcim: 'Holcim',
  imq: 'IMQ Campus',
  mydigicoop: 'MyDigicoop',
  sanidad: 'Sanidad',
  jasangarritasunariekin: 'Jasangarritasunariekin',
  apnabi: 'Apnabi',
  apm: 'APM',
  vicioschool: 'Vicio School',
  berdin: 'Berdin',
  md: 'MD',
  smartgrid: 'Smartgrid',
  noisetech: 'NoiseTech',
  sim: 'sim',
  dhl: 'dhl',
  chef: 'chef',
  gameup: 'gameup',
  incident_command_academy: 'Incident Command Academy',
  hre: 'HRE Automation'
});

const DEFAULT_CLIENT = Object.keys(CLIENT_TITLES)[0];

const printLoader = () => {
  const shapes = ['\\', '|', '/', '-'];
  let index = 0;
  const interval = setInterval(() => {
    process.stdout.write(`\r${shapes[index]}`);
    index += 1;
    // eslint-disable-next-line no-bitwise
    index &= 3;
  }, 250);

  return interval;
};

const runNodeCommand = (command, { silentMode, loadingMode } = { silentMode: true, loadingMode: false }) => {
  const interval = loadingMode ? printLoader() : null;

  return new Promise((resolve, reject) => {
    const task = spawn(command, { shell: true });

    if (silentMode) {
      task.stdout.on('data', (data) => process.stdout.write(data.toString()));
      task.stderr.on('data', (data) => process.stdout.write(data.toString()));
    }

    task.on('close', (code) => {
      if (interval) {
        clearInterval(interval);
      }

      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`El comando falló con el código de salida ${code}`));
      }
    });
  });
};

module.exports = {
  printLoader,
  runNodeCommand,
  FORMATTED_ENVS,
  CLIENT_TITLES,
  DEFAULT_CLIENT,
};

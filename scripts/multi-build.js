/* eslint-disable import/no-extraneous-dependencies */
const inquirer = require('inquirer');
const fs = require('fs').promises;
const { FORMATTED_ENVS, CLIENT_TITLES, runNodeCommand } = require('./compile-utils');

function askForClients() {
  const clients = Object.keys(CLIENT_TITLES);

  return inquirer.prompt([
    {
      type: 'checkbox',
      name: 'selectedClients',
      choices: clients,
      message: 'Selecciona los clientes:',
    },
  ]);
}

async function askForEnvironment() {
  const prefix = '.env.';

  const { selectedEnvs } = await inquirer.prompt([
    {
      type: 'checkbox',
      name: 'selectedEnvs',
      message: 'Selecciona el entorno:',
      choices: FORMATTED_ENVS,
    },
  ]);

  return selectedEnvs.map((env) => ({ envFile: `${prefix}${env}`, env }));
}

function validateOptions(clients, env) {
  const clientNames = clients.map((client) => CLIENT_TITLES[client]).join(', ');
  const envsNames = env.join(', ');

  return inquirer.prompt([
    {
      type: 'confirm',
      name: 'validate',
      message: `Quieres hacer la build de ${clientNames} para ${envsNames}?`,
    },
  ]);
}

async function readOutputDir(env) {
  const envFile = await fs.readFile(`./${env}`, 'utf-8');
  const outputDir = envFile.split('\n').find((line) => line.includes('OUTPUT_DIR'));

  return outputDir.replace('OUTPUT_DIR=', '');
}

async function runBuildScripts(client, env, outputDir) {
  try {
    process.stdout.write('  Building...');
    await runNodeCommand(`yarn cross-env VUE_APP_CLIENT=${client} vue-cli-service build --mode=${env}`, {
      loadingMode: true,
      silentMode: true,
    });
    process.stdout.write(`\r✅ Build ${client} in ${outputDir}\n`);
  } catch (error) {
    console.log('❌ Build error');
    console.log('---------------------------------');
    console.log(error);
  }
}

async function buildClientsRecursive(selectedClients, envFile, env, outputDir) {
  if (selectedClients.length === 0) {
    return;
  }

  const client = selectedClients.pop();
  await runBuildScripts(client, env, outputDir);

  await buildClientsRecursive(selectedClients, envFile, env, outputDir);
}

async function buildEnvRecursive(selectedEnvs, selectedClients) {
  if (selectedEnvs.length === 0) {
    return;
  }

  const { env, envFile } = selectedEnvs.pop();
  const outputDir = await readOutputDir(envFile);
  await buildClientsRecursive([...selectedClients], envFile, env, outputDir);

  await buildEnvRecursive(selectedEnvs, selectedClients);
}

async function buildClients() {
  const { selectedClients } = await askForClients();

  const selectedEnvsData = await askForEnvironment();
  const selectedEnvs = selectedEnvsData.map(({ env }) => env);

  const { validate } = await validateOptions(selectedClients, selectedEnvs);
  if (!validate) {
    console.log('❌ Build cancelada');
    return;
  }

  await runNodeCommand('yarn locales');
  console.log('✅ Load locales');

  await buildEnvRecursive(selectedEnvsData, selectedClients);
  console.log('✅ Builds finalizadas');
}

buildClients();

const fs = require('fs');
const axios = require('axios');

const LOCALES_PATH = './src/locales';
const LOCALE_API_KEY = 'qRzIgScPO809HzGukP7tqZqUkmJaSDJY';
const LOCALE_STATUS = 'translated';

if (!fs.existsSync(LOCALES_PATH)) {
  fs.mkdirSync(LOCALES_PATH);
}

function onSuccess(locale) {
  console.log(`✅ Locales ${locale} correctly!`);
}

function onError(e, locale) {
  if (locale) {
    console.log(`❌ Error downloading ${locale}`);
  } else {
    console.log('❌ Error!');
  }

  console.log(e);
}

(async () => {
  const response = await axios.get(
    `https://localise.biz/api/export/all.json?status=${LOCALE_STATUS}&key=${LOCALE_API_KEY}`
  );

  Object.entries(response.data).forEach(([key, values]) => {
    const locale = key?.replace(/-\w+$/, '');

    try {
      fs.writeFile(`${LOCALES_PATH}/${locale}.json`, JSON.stringify(values), () => onSuccess(locale));
    } catch (e) {
      onError(e, locale);
    }
  });
})();

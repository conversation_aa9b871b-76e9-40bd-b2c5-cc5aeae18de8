/* eslint-disable import/no-extraneous-dependencies */
const inquirer = require('inquirer');
const inquirerPrompt = require('inquirer-autocomplete-prompt');
const fs = require('fs').promises;
const { FORMATTED_ENVS, CLIENT_TITLES, runNodeCommand } = require('./compile-utils');
const { generateVersionFile } = require('./version');

inquirer.registerPrompt('autocomplete', inquirerPrompt);

function askForClient() {
  const clients = Object.keys(CLIENT_TITLES);

  return inquirer.prompt([
    {
      type: 'autocomplete',
      name: 'client',
      choices: clients,
      message: 'Selecciona el cliente:',
      source: (answersSoFar, input) => {
        if (!input) return clients;

        return clients.filter((client) => client.includes(input));
      },
    },
  ]);
}

async function askForEnvironment() {
  const prefix = '.env.';

  const { env } = await inquirer.prompt([
    {
      type: 'autocomplete',
      name: 'env',
      message: '<PERSON><PERSON>cciona el entorno:',
      choices: FORMATTED_ENVS,
      source: (answersSoFar, input) => {
        if (!input) return FORMATTED_ENVS;

        return FORMATTED_ENVS.filter((e) => e.includes(input));
      },
    },
  ]);

  return { envFile: `${prefix}${env}`, env };
}

function validateOptions(client, env, outputDir) {
  const clientName = CLIENT_TITLES[client];

  return inquirer.prompt([
    {
      type: 'confirm',
      name: 'validate',
      message: `Quieres hacer la build de ${clientName} para el entorno ${env} en la carpeta ${outputDir}?`,
    },
  ]);
}

async function readOutputDir(env) {
  const envFile = await fs.readFile(`./${env}`, 'utf-8');
  const outputDir = envFile.split('\n').find((line) => line.includes('OUTPUT_DIR'));

  return outputDir.replace('OUTPUT_DIR=', '');
}

async function runBuildScripts(client, env, outputDir) {
  try {
    await runNodeCommand('yarn locales');
    console.log('✅ Load locales');

    process.stdout.write('  Building...');
    await runNodeCommand(`yarn cross-env VUE_APP_CLIENT=${client} vue-cli-service build --mode=${env}`, {
      loadingMode: true,
      silentMode: true,
    });
    process.stdout.write('  Generating version file...\r');
    generateVersionFile(client, outputDir);
    process.stdout.write(`\r✅ Build ${client} in ${outputDir}\n`);
  } catch (error) {
    console.log('❌ Build error');
    console.log('---------------------------------');
    console.log(error);
  }
}

async function buildClient() {
  try {
    const { client } = await askForClient();
    const { envFile, env } = await askForEnvironment();
    const outputDir = await readOutputDir(envFile);

    const { validate } = await validateOptions(client, env, outputDir);

    if (!validate) {
      console.log('❌ Build cancelada');
      return;
    }

    runBuildScripts(client, env, outputDir);
  } catch (error) {
    console.log(error);
  }
}

buildClient();

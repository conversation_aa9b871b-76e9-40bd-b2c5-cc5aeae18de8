const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function getCurrentGitBranch() {
  try {
    const branch = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
    return branch;
  } catch (error) {
    console.error('Error fetching Git branch:', error.message);
    return null;
  }
}

function getCurrentCommitHash() {
  try {
    const commit = execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
    return commit;
  } catch (error) {
    console.error('Error fetching Git commit:', error.message);
    return null;
  }
}

function formatDate() {
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const now = new Date();
  const month = months[now.getMonth()];
  const day = now.getDate();
  const year = now.getFullYear();
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${month} - ${day} - ${year} | [${hours}:${minutes}:${seconds}]`;
}

function createVersionFile(client, outputDir, branch, commit) {
  const buildDir = path.join(__dirname, '../builds_games', client, outputDir.trim());

  fs.mkdirSync(buildDir, { recursive: true });

  const versionFilePath = path.join(buildDir, 'version.json');
  const formattedDate = formatDate();

  const versionData = {
    build: {
      date: formattedDate,
      branch: branch || 'unknown',
      commit: commit || 'unknown',
    }
  };

  try {
    fs.writeFileSync(versionFilePath, JSON.stringify(versionData, null, 2));
    console.log(`version.json file created/updated successfully at ${versionFilePath}.`);
  } catch (error) {
    console.error('Error writing version.json file:', error.message);
  }
}

function generateVersionFile(client, outputDir) {
  const branch = getCurrentGitBranch();
  const commit = getCurrentCommitHash();
  createVersionFile(client, outputDir, branch, commit);
}

module.exports = {
  generateVersionFile
};

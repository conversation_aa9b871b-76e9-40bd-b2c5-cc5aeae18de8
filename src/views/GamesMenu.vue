<template>
  <div class="GamesMenu">
    <ul class="games">
      <li
        v-for="(game, i) in games"
        :key="i"
      >
        <router-link :to="{ name: 'Home', query: { id: i + 1 } }">
          {{ $t(`${game.toUpperCase()}.TITLE`) }}
        </router-link>
      </li>
    </ul>
  </div>
</template>

<script>
const GAMES = [
  'Quiz',
  'Roulette',
  'Puzzle',
  'HiddenWords',
  'X2',
  'LettersWheel',
  'TrueOrFalse',
  'FILLTHEGAPS',
  'HiddenPic',
  'MemoryMatch',
  'HigherLower',
  'GuessWord',
  'Wordle',
  'SearchWord',
  'Categorized',
  'VideoQuiz',
];

export default {
  data() {
    return {
      games: GAMES,
    };
  },
};
</script>

<style lang="scss" scoped>
.GamesMenu {
  flex: 1;
  padding: $spacing-xs;
  display: grid;
  grid-template-columns: 100%;
  box-sizing: border-box;
  background: url('~@/assets/images/background/fondo-juegos.png');
  .games {
    max-width: 800px;
    width: 100%;
    margin: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    align-content: flex-start;
    gap: $spacing-m;

    li {
      display: grid;
      a {
        all: unset;
        text-align: center;
        background-color: $color-neutral-light;
        box-shadow: $shadow-elevation-1;
        transition: all 0.3s ease;
        border-radius: 5px;
        padding: $spacing-s;

        &:hover {
          cursor: pointer;
          background-color: $color-neutral-lighter;
        }
      }
    }
  }
}
</style>

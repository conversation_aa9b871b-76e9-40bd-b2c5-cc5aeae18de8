<template>
  <div class="Finnish cover-screen">
    <BaseLoader v-if="isLoading" />

    <ScorePanel
      v-else
      class="panel"
      :score="currentScore"
      :description="getDescription()"
      :check="getCheck()"
      @close-game="closeGame"
    />
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import ScorePanel from '@/components/common/ScorePanel';

export default {
  name: 'Finnish',
  components: {
    ScorePanel,
  },

  computed: {
    ...get('', ['isLoading', 'getDescription', 'getScore', 'getCheck']),

    currentScore() {
      return this.getScore();
    },
  },

  async created() {
    await this.$store.dispatch('finishChapter');
  },

  methods: {
    closeGame() {
      window.parent.postMessage('close-modal', '*');
    },
  },
};
</script>

<style lang="scss" scoped></style>

<template>
  <div class="Game">
    <component
      :is="currentGameContent"
      @change-page="changePage"
    />
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import Quiz from '@/games/Quiz';
import Roulette from '@/games/Roulette';
import Puzzle from '@/games/Puzzle';
import HiddenWords from '@/games/HiddenWords';
import DoubleOrNothing from '@/games/DoubleOrNothing';
import LettersWheel from '@/games/LettersWheel';
import TrueOrFalse from '@/games/TrueOrFalse';
import FillGaps from '@/games/FillGaps';
import HiddenPic from '@/games/HiddenPic';
import MemoryMatch from '@/games/MemoryMatch';
import HigherLower from '@/games/HigherLower';
import GuessWord from '@/games/GuessWord';
import Wordle from '@/games/Wordle';
import SearchWord from '@/games/SearchWord';
import VideoQuiz from '@/games/VideoQuiz';

const MOBILE_BREAKPOINT = 768;

export default {
  components: {
    Quiz,
    Roulette,
    Puzzle,
    HiddenW<PERSON>s,
    DoubleOrNothing,
    LettersWheel,
    TrueOrFalse,
    FillGaps,
    HiddenPic,
    MemoryMatch,
    HigherLower,
    GuessWord,
    Wordle,
    SearchWord,
    VideoQuiz,
  },

  data() {
    return {
      games: [
        'Quiz',
        'Roulette',
        'Puzzle',
        'HiddenWords',
        'DoubleOrNothing',
        'LettersWheel',
        'TrueOrFalse',
        'FillGaps',
        'HiddenPic',
        'MemoryMatch',
        'HigherLower',
        'GuessWord',
        'Wordle',
        'SearchWord',
        'Categorized',
        'VideoQuiz',
      ],
    };
  },

  computed: {
    ...get('', ['getChapterGame']),

    currentGameContent() {
      let normalizedName = this.getChapterGame();
      if (normalizedName === 'Categorized') {
        normalizedName = 'TrueOrFalse';
      }
      const currentGame = this.games.findIndex((game) => game === normalizedName);

      return this.games[currentGame];
    },
  },

  mounted() {
    if (window.innerWidth > MOBILE_BREAKPOINT) {
      this.removeBodyScroll();
    }
  },

  beforeDestroy() {
    this.resetBodyScroll();
  },

  methods: {
    changePage(page) {
      this.$emit('change-page', page);
    },

    resetBodyScroll() {
      document.querySelector('body').classList.remove('no-scroll');
    },

    removeBodyScroll() {
      document.querySelector('body').classList.add('no-scroll');
    },
  },
};
</script>

<style lang="scss" scoped>
.Game {
  flex: 1;
  width: 100%;
  background: url('~@/assets/images/background/fondo-juegos.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
</style>

<template>
  <div class="Home cover-screen">
    <BaseLoader v-if="isLoading" />

    <div
      v-else
      class="panel"
    >
      <img
        src="~@/assets/images/home.svg"
        alt="wellcome image"
      />

      <DeepContent
        v-if="getDescription()"
        class="description"
        v-html="getDescription()"
      />

      <BaseButton
        v-if="buttonStartVisible"
        class="btn-start"
        @click="$emit('change-page', 1)"
      >
        {{ $t('START.BUTTON') }}
        <font-awesome-icon icon="fa-solid fa-angle-right" />
      </BaseButton>
    </div>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';

import DeepContent from '@/components/common/DeepContent';

export default {
  name: 'Home',
  components: {
    DeepContent,
  },

  data() {
    return {
      buttonStartVisible: false,
    };
  },

  computed: {
    ...get('', ['isLoading', 'getDescription']),

    chapterId() {
      return this.$route?.query?.id;
    },
  },

  watch: {
    chapterId: {
      immediate: true,
      handler(newValue) {
        if (newValue !== undefined) {
          this.loadChapter();
        }
      },
    },
  },

  methods: {
    async loadChapter() {
      if (this.chapterId) {
        await this.$store.dispatch('initChapter', this.chapterId);
        this.buttonStartVisible = true;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.Home {
  .panel {
    .description {
      margin-bottom: $spacing-l;
    }

    .btn-start {
      margin: 0 auto;
    }
  }
}
</style>

<template>
  <div
    class="card-container"
    :class="{ correct: card.isMatched }"
  >
    <div
      class="card"
      :class="{ flipped: card.isFlipped, matched: card.isMatched }"
    >
      <div class="card__face card__face--front">
        <p class="text_front">?</p>
      </div>

      <div
        class="card__face card__face--back"
        :style="{ 'background-image': `url(${card.image})` }"
      >
        <p class="text_back">
          {{ card.text }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    card: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.card-container {
  width: clamp(100px, 16vmin, 150px);
  height: clamp(100px, 16vmin, 150px);
  perspective: 1000px;
  display: grid;

  &.correct {
    animation: correct 0.5s;
  }

  .card {
    // border: 1px solid red;
    // width: 100%;
    // height: 100%;
    position: relative;
    transition: transform 0.6s;
    transform-style: preserve-3d;
    cursor: pointer;

    &.flipped {
      transform: rotateY(180deg);
    }

    &.matched .card__face--back {
      opacity: 1;
      background-color: $color-neutral-mid-darker;
    }

    .card__face {
      border-radius: $border-radius-l;
      position: absolute;
      height: 100%;
      width: 100%;
      backface-visibility: hidden;
      box-shadow: $shadow-elevation-2;
      overflow: hidden;
    }

    .card__face--front {
      display: grid;
      $darkColor: adjust-color($color: $color-primary,
        $lightness: -6%,
      );
      $lightColor: adjust-color($color: $color-primary,
        $lightness: 6%,
      );
      background: $color-primary radial-gradient(closest-side at 50% 50%, $lightColor 0%, $darkColor 100%);

      .text_front {
        display: grid;
        place-items: center;
        color: $color-neutral-lightest;
        text-align: center;
        font-weight: bold;
        font-size: $font-size-5xl;
        font-family: $font-family-secondary;
        overflow-wrap: anywhere;
      }
    }

    .card__face--back {
      transform: rotateY(180deg);
      background-color: $color-neutral-lightest;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      display: grid;

      .text_back {
        position: relative;
        display: grid;
        place-items: center;
        color: #fff;
        text-align: center;
        font-weight: bold;
        font-size: $font-size-m;
        font-family: $font-family-secondary;
        z-index: 1;
        overflow-wrap: anywhere;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          z-index: -1;
          background: rgba(0, 0, 0, 0.4);
        }
      }
    }
  }
}

@media #{$breakpoint-md-max} {
  .card-container {
    .card {
      .card__face--front {
        .text_front {
          margin: 0 auto;
        }
      }

      .card__face--back {
        .text_back {
          // margin-top: 20%;
          font-size: $font-size-s;
        }
      }
    }
  }
}

@media #{$breakpoint-sm-max} {
  .card-container {
    width: clamp(85px, 16vmin, 150px);
    height: clamp(85px, 16vmin, 150px);
    .card {
      .card__face--front {
        .text_front {
          font-size: $font-size-4xl;
        }
      }
    }
  }
}
</style>

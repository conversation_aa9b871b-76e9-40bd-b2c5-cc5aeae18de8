<template>
  <div class="Board">
    <Timer
      v-if="time"
      :time="time"
      :state="timer.state"
      :bonus="timer.bonus"
      @current-time="saveTime"
      @timer-controller="timerController"
    />

    <ul class="list">
      <Panel
        v-for="(card, i) in shuffledCards"
        :key="i"
        :card="card"
        @click.native="selectedCard(card)"
      />
    </ul>
  </div>
</template>

<script>
import Timer from '@/components/common/Timer';
import Panel from '@/components/memoryMatch/Panel';

const STORE = 'MemoryMatchModule';

export default {
  components: {
    Timer,
    Panel,
  },

  props: {
    startTime: {
      type: Number,
      default: undefined,
    },

    cards: {
      type: Array,
      default: undefined,
    },

    initBonus: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      timer: {
        bonus: this.initBonus,
        state: 'stopped',
      },
      elapsedTime: 0,
      flippedCards: [],
      // attempts: [],
      shuffledCards: [],
    };
  },

  computed: {
    time() {
      return this.startTime;
    },
  },

  watch: {
    elapsedTime(newTime) {
      if (newTime) {
        this.saveAnswer();
      }
    },
  },

  mounted() {
    const delay = (this.cards.length / 2) * 1000;
    this.showAllCards(delay);
  },

  methods: {
    showAllCards(delay) {
      const shuffledCards = this.shuffle([...this.cards]);

      this.shuffledCards = shuffledCards.map((c) => ({ ...c, isFlipped: true }));
      setTimeout(() => {
        this.timer.state = 'discounting';
        this.shuffledCards = shuffledCards;
      }, delay);
    },

    timerController(data) {
      if (data.state) {
        this.timer.state = data.state;
        if (this.timer.state === 'stopped') {
          this.saveTime(0);
        }
      }

      if (data.bonus !== undefined) {
        this.timer.bonus = data.bonus;
      }
    },

    saveTime(currentTime) {
      this.elapsedTime = this.time + this.initBonus - currentTime;
    },

    shuffle(items) {
      const randomItem = Math.floor(Math.random() * items.length);
      const item = items[randomItem];

      if (items.length === 0) return [];

      items.splice(randomItem, 1);
      return [item, ...this.shuffle(items)];
    },

    selectedCard(card) {
      if (this.timer.state === 'paused' || card.isMatched || card.isFlipped) return;

      const currentCard = this.shuffledCards.find(({ id }) => id === card.id);
      currentCard.isFlipped = true;

      const isFirstCardFlipped = this.flippedCards.length < 2;
      if (isFirstCardFlipped) {
        this.flippedCards.push(card);
      }

      if (this.flippedCards.length === 2) {
        this.timer.state = 'paused';
      }
    },

    areAllCardsMatched() {
      return this.shuffledCards.every((card) => card.isMatched);
    },

    async saveAnswer() {
      let attempt = { time: this.elapsedTime };

      const isUnanwered = this.flippedCards.length !== 2;
      if (!isUnanwered) {
        const [card1, card2] = this.flippedCards;
        const cards = [card1?.id, card2?.id];
        attempt = { ...attempt, cards };
      }

      const cardsSort = attempt.cards?.sort();
      let cardsMatched = cardsSort?.length ? await this.$store.dispatch(`${STORE}/saveAnswer`, attempt) : false;

      if (!cardsMatched) {
        this.timer.bonus = -5;
      } 

      setTimeout(async () => {
        this.setFlippedCards(cardsMatched);
        const willCloseRound = isUnanwered || this.areAllCardsMatched();
        if (willCloseRound) {
          if (!this.areAllCardsMatched()) await this.$store.dispatch(`${STORE}/saveAnswer`, attempt);
          this.$emit('finish-game');
        } else {
          this.timer.state = 'discounting';
        }
      }, 1000);
    },

    setFlippedCards(cardsMatched) {
      for (let i = 0; i < this.flippedCards.length; i += 1) {
        const element = this.flippedCards[i];
        element.isFlipped = cardsMatched;
        element.isMatched = cardsMatched;
      }

      this.flippedCards = [];
    },
  },
};
</script>

<style lang="scss" scoped>
.Board {
  .list {
    max-width: 800px;
    margin: 0 auto;
    margin-top: $spacing-xl;
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
    gap: $spacing-s;
  }

  @media #{$breakpoint-sm-max} {
    .list {
      margin-top: $spacing-m;
      gap: $spacing-xs;
    }
  }
}
</style>

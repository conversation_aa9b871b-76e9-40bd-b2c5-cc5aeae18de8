<template>
  <div
    id="text"
    class="PanelOptions"
    :draggable="true"
    @dragstart="dragStart"
    @drag="dragging"
    @dragend="dragEnd"
  >
    <div class="container">
      <p>
        {{ optionWord }}
      </p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    option: {
      type: Object,
      required: true,
    },
  },

  computed: {
    optionWord() {
      return this.option?.answer;
    },
  },

  methods: {
    dragStart(event) {
      const { target } = event;
      target.style.opacity = '1';
      target.classList.add('drag');
      event.dataTransfer.setData('text', this.optionWord);
    },

    dragging(event) {
      const { target } = event;
      target.style.visibility = 'hidden';
      target.style.opacity = '1';
    },

    dragEnd(event) {
      const { target } = event;
      target.style.visibility = 'visible';
    },
  },
};
</script>

<style lang="scss" scoped>
.PanelOptions {
  cursor: move; /* fallback if grab cursor is unsupported */
  cursor: grab;
  display: flex;
  margin: auto;

  .container {
    display: grid;
    place-content: start center;

    p {
      font-family: $font-family-secondary;
      font-size: clamp($font-size-m, 2vw, $font-size-xl);
      border: 1px solid $color-primary-light;
      border-radius: $border-radius-xl; //$border-radius-m;
      box-shadow: $shadow-elevation-1;
      color: $color-primary-dark;
      text-align: center;
      padding: $spacing-xs $spacing-m;
      background: $color-neutral-lighter;
      background: $color-primary-lightest;
    }
  }
}
</style>

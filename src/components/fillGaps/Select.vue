<template>
  <select
    v-model="selection"
    class="Select"
    name="options"
    @input="$emit('input', $event.target.value)"
  >
    <option
      v-for="(option, index) in options"
      :key="index"
      :value="option.answer"
      class="option"
    >
      {{ option.answer }}
    </option>
  </select>
</template>

<script>
export default {
  props: {
    options: {
      type: null,
      required: true,
    },
  },

  data() {
    return {
      selection: '',
    };
  },

  watch: {
    selection() {
      this.clearPreviousStates();
    },
  },

  methods: {
    clearPreviousStates() {
      this.$el.classList.remove('incorrect');
      this.$el.classList.remove('correct');
    },
  },
};
</script>

<style lang="scss" scoped>
.Select {
  background: $color-neutral-lightest;
  display: inline;
  max-width: 200px;
  width: 100%;
  border: 1px solid $color-neutral-mid;
  padding: $spacing-xs;
  border-radius: 5px;
  font-size: $font-size-xl;
  color: $color-neutral-dark;
  outline: none;

  option {
    background-color: $color-neutral-lightest;
  }
}
</style>

<template>
  <div
    class="Draggable"
    @dragover="handleOnDragOver"
    @dragenter="dragEnterEvent"
    @dragleave="dragLeaveEvent"
    @drop="dropEvent"
  >
    <div class="container">
      <p class="word">
        <template v-if="word">
          {{ word }}
        </template>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    options: {
      type: Array,
      required: true,
    },
  },

  data() {
    return {
      word: null,
    };
  },

  methods: {
    handleOnDragOver(event) {
      event.preventDefault();
    },

    dragEnterEvent(event) {
      const { target } = event;
      target.style.transition = 'all .2s ease-in-out';
      target.style.transform = 'scale(1.1)';
    },

    dragLeaveEvent(event) {
      const { target } = event;
      target.style.transition = 'all .2s ease-in-out';
      target.style.transform = 'scale(1)';
    },

    dropEvent(event) {
      event.preventDefault();
      event.stopPropagation();
      const { target } = event;
      target.style.transform = 'scale(1)';
      const word = event.dataTransfer.getData('text');

      if (!this.options.find((o) => o.answer === word)) {
        return;
      }

      this.word = word;
      this.clearPreviousStates(event.target);
      this.$emit('input', this.word);
    },

    clearPreviousStates(target) {
      const draggableDOM = target.closest('.Draggable');
      if (!draggableDOM && !draggableDOM?.classList) return;

      draggableDOM.classList.remove('incorrect');
      draggableDOM.classList.remove('correct');
    },
  },
};
</script>

<style lang="scss" scoped>
.Draggable{
  display: inline;
  max-width: 200px;
  width: 100%;

  .container{
    .word{
      border-radius: $border-radius-xl;
      background: $color-neutral-lightest;
      font-family: $font-family-secondary;
      font-size: $font-size-xl;
      color: $color-neutral-dark;
      text-align: center;
      box-shadow: $shadow-elevation-0;
      display: inline-block;
      min-width: 200px;
      min-height: $font-size-3xl;
      vertical-align: middle;
    }
  }
}

</style>

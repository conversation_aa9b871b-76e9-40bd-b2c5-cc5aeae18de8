<template>
  <div class="Video">
    <vimeo-player
      v-if="url"
      ref="player"
      class="video"
      :video-url="url"
      :options="options"
      @ready="onReady"
    />
  </div>
</template>

<script>
export default {
  name: 'Video',
  props: {
    url: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      playerReady: false,
      video: undefined,
      options: {
        autoplay: 1,
        title: false,
        responsive: true,
        controls: false,
        pip: false
      },
    };
  },

  methods: {
    onReady() {
      this.playerReady = true;
      this.video = this.$refs.player;
      this.$emit('loaded', this.video);
    },
  },
};
</script>

<style lang="scss" scoped>
.Video {
  width: 100%;
  background-color: #000;
  display: grid;
  align-items: center;

  // min-height: 70vh;
  :deep([id^="vimeo-player"]) {
    >div {
      padding: 0 !important;
      aspect-ratio: 16/9;
      max-height: calc(100vh - 230px);
      margin: 0 auto;
    }
  }
}
</style>

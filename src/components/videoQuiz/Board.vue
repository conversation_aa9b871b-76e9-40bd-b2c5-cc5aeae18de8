<template>
  <div class="Board">
    <div class="main-container">
      <Video
        :url="url"
        @loaded="videoLoaded"
      />

      <VideoControls
        :questions="questions"
        :progress="progress"
        :video-time="videoTime"
      />
    </div>
  </div>
</template>

<script>

import Video from '@/components/videoQuiz/Video';
import VideoControls from '@/components/videoQuiz/VideoControls';

export default {
  components: {
    Video,
    VideoControls,
  },

  props: {
    questions: {
      type: Array,
      required: true,
    },

    progress: {
      type: Number,
      default: 0,
    },

    videoTime: {
      type: Number,
      required: true,
    },

    url: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      video: undefined,
    };
  },

  methods: {
    currentTime(newValue, timer) {
      this.$emit('current-time', newValue, timer);
    },

    videoLoaded(video) {
      this.video = video;
      this.$emit('loaded', this.video);
    },
  },

};
</script>

<style lang="scss" scoped>
.Board {
  max-width: 1200px;
  width: 95%;
  margin: 0 auto;

  .main-container {
    display: flex;
    margin-top: 5vh;
    position: relative;
    box-shadow: $shadow-elevation-2;
  }
}
</style>

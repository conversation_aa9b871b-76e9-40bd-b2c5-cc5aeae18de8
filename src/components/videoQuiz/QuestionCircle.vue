<template>
  <div
    class="QuestionCircle"
    :class="question.state"
  >
    <span class="icon">
      <TransitionSlide side="bottom">
        <font-awesome-icon
          v-if="question.state === 'correct'"
          :icon="['fas', 'check']"
        />

        <span v-else>?</span>
      </TransitionSlide>
    </span>
  </div>
</template>

<script>
import TransitionSlide from '@/transitions/TransitionSlide';

export default {
  components: {
    TransitionSlide,
  },
  props: {
    question: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.QuestionCircle {
  width: 36px;
  height: 36px;
  padding: 2px;
  border-radius: 50%;
  font-size: 25px;
  font-weight: 500;
  font-family: $font-family-secondary;
  text-align: center;
  aspect-ratio: 1;
  display: grid;
  place-items: center;
  flex-shrink: 0;
  .icon {
    overflow: hidden;
  }

  &.active {
    background: $color-neutral-lightest;
    box-shadow: inset 0 0 2px 1px $color-primary-dark, inset 0 0 2px 2px $color-primary;
    color: $color-primary-dark;
  }

  &.unanswered {
    background: $color-neutral-darkest;
    color: $color-neutral-mid-darker;
  }

  &.correct {
    // background: $color-success;
    background-color: $color-primary;
    color: #fff;

    // color: $color-neutral-lightest;
  }

  @media #{$breakpoint-sm-max} {
    width: 5px;
    height: 5px;
    font-size: 0;

    &.active {
      box-shadow: none;
    }
  }
}
</style>

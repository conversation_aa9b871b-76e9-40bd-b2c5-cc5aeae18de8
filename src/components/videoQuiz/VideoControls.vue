<template>
  <div class="VideoControls">
    <div class="progress-allcontainer">
      <div
        v-for="({ progressBarTime, active, correct, question }, i) in progressParts"
        :key="i"
        class="progress-container"
        :style="{ flexBasis: `${(progressBarTime * 100) / videoTime}% ` }"
      >
        <div
          v-if="progressBarTime"
          class="progress-bar"
          :class="{ active, correct }"
        >
          <div
            class="filled-bar"
            :style="{ width: `calc(${(getProgressBarCurrentTime(i) * 100) / progressBarTime}%)` }"
          />
        </div>

        <QuestionCircle
          v-if="question"
          :question="question"
        />
      </div>
    </div>
  </div>
</template>

<script>
import QuestionCircle from '@/components/videoQuiz/QuestionCircle';

export default {
  components: {
    QuestionCircle,
  },

  props: {
    questions: {
      type: Array,
      required: true,
    },

    progress: {
      type: Number,
      required: true,
    },

    videoTime: {
      type: Number,
      required: true,
    },
  },

  computed: {
    currentProgress() {
      return this.progress - this.getCompletedQuestionsTime();
    },

    progressParts() {
      let previousBarTime = 0;

      const parts = this.questions.map((question) => {
        const progressBarTime = question?.triggerTime - previousBarTime;
        previousBarTime += progressBarTime;
        const active = question.state === 'active';
        const complete = question.state === 'correct';
        return {
          progressBarTime,
          active,
          complete,
          question,
        };
      });

      if (previousBarTime < this.videoTime) {
        parts.push({ progressBarTime: this.videoTime - previousBarTime, active: false, complete: false });
      }

      return parts;
    },
  },

  methods: {
    getProgressBarCurrentTime(index) {
      const part = this.progressParts[index];
      const correct = this.questions.filter((question) => question.state === 'correct');
      if (correct.length === this.questions.length && index === correct.length) {
        part.active = true;
      }
      if (part.active) return this.currentProgress;

      if (part.complete) return part.progressBarTime;

      return 0;
    },

    getCompletedQuestionsTime() {
      const correct = this.questions.filter((question) => question.state === 'correct');
      const ques = correct[correct.length - 1];
      return ques?.triggerTime ?? 0;
    },
  },
};
</script>

<style lang="scss" scoped>
.VideoControls {
  position: absolute;
  top: 100%;
  padding: $spacing-m 0;
  width: 100%;
  background-color: $color-neutral-dark;
  border-radius: 0 0 10px 10px;
  display: flex;
  align-items: center;
  box-shadow: $shadow-elevation-2;

  .progress-allcontainer {
    width: 100%;
    display: flex;
    align-items: center;

    .progress-container {
      display: flex;

      align-items: center;
      gap: $spacing-xs;
      padding: 0 $spacing-2xs;

      &:first-child {
        padding-left: $spacing-m;
      }

      &:last-child {
        padding-right: $spacing-m;
      }

      .progress-bar {
        height: 8px;
        background-color: $color-neutral-darkest;
        border-radius: 50px;
        width: 100%;

        .filled-bar {
          transition: all 0.1s linear;
          height: 100%;
          border-radius: 50px;
          width: 0%;
          background: $color-primary;
        }

        @media #{$breakpoint-sm-max} {
          height: 4px;
        }
      }
    }
  }
}
</style>

<template>
  <div class="Board max-content-width">
    <canvas
      id="canvas"
      :width="width"
      :height="height"
    />
  </div>
</template>

<script>
import Settings from '@/classes/puzzle/Settings';
import Game from '@/classes/puzzle/Game';

const SIZE_TO_BE_RESPONSIVE = 500;
const CANVAS_WIDTH = 1900;
const CANVAS_HEIGHT = 1300;
const CANVAS_RESPONSIVE_WIDTH = 1000;
const CANVAS_RESPONSIVE_HEIGHT = 1490;

export default {
  props: {
    image: {
      type: String,
      required: true,
    },

    difficulty: {
      type: Number,
      default: 0,
    },
  },

  data() {
    const isResponsive = window.innerWidth < SIZE_TO_BE_RESPONSIVE;

    return {
      settings: undefined,
      game: undefined,
      isResponsive,
      width: isResponsive ? CANVAS_RESPONSIVE_WIDTH : CANVAS_WIDTH,
      height: isResponsive ? CANVAS_RESPONSIVE_HEIGHT : CANVAS_HEIGHT,
    };
  },

  computed: {
    finishPuzzle() {
      return this.game?.isFinish ?? false;
    },

    loadPuzzle() {
      return this.game?.isloaded ?? false;
    },
  },

  watch: {
    finishPuzzle(newValue, oldValue) {
      if (newValue && !oldValue) {
        this.finish();
      }
    },

    loadPuzzle(newValue, oldValue) {
      if (newValue && !oldValue) {
        this.loaded();
      }
    },
  },

  created() {
    this.settings = new Settings(this.difficulty);
  },

  mounted() {
    setTimeout(() => {
      this.init();
    }, 500);
  },

  methods: {
    init() {
      this.canvas = document.getElementById('canvas');

      const canvasWidth = this.canvas.width;
      const canvasHeight = this.canvas.height;
      const questionImage = this.image;
      const delayToStart = 1000;
      this.game = new Game(
        this.settings,
        this.difficulty,
        canvasWidth,
        canvasHeight,
        questionImage,
        delayToStart,
        this.isResponsive
      );

      setTimeout(() => {
        this.$emit('start');
      }, delayToStart + 3000);
    },

    finish() {
      this.$emit('finish');
    },

    loaded() {
      this.$emit('loaded');
    },
  },
};
</script>

<style lang="scss" scoped>
.Board {
  height: 100vh;
  text-align: center;

  canvas {
    max-width: 100%;
    max-height: 100%;
  }
}
</style>

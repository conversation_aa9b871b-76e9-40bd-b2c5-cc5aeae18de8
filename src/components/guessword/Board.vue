<template>
  <div class="Board">
    <Timer
      v-if="time"
      :time="time"
      :state="timer.state"
      :bonus="timer.bonus"
      @current-time="saveTime"
      @timer-controller="timerController"
    />

    <div class="main-container max-content-width">
      <div class="title-container">
        {{ text }}
      </div>

      <Letters
        :word="wordLetters"
        @answer="attempt = $event"
      />

      <BaseButton
        :disabled="disabledButtonWhileCheckOrStopped"
        @click="pauseTimerToSendAttempt"
      >
        {{ $t('CONFIRM.BUTTON') }}
        <font-awesome-icon icon="fa-solid fa-check" />
      </BaseButton>
    </div>
  </div>
</template>

<script>
import Timer from '@/components/common/Timer';
import Letters from '@/components/guessword/Letters';

export default {
  components: {
    Timer,
    Letters,
  },

  props: {
    question: {
      type: Object,
      required: true,
    },

    initBonus: {
      type: Number,
      default: 0,
    },

    fail: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      timer: {
        bonus: this.initBonus,
        state: 'stopped',
      },
      elapsedTime: 0,
      attempt: undefined,
      currentAnswer: undefined,
      timeUsed: Math.abs(this.initBonus),
    };
  },

  computed: {
    text() {
      return this.question?.question;
    },

    word() {
      return this.question?.word;
    },

    wordLetters() {
      return this.word?.split('') ?? [];
    },

    time() {
      return this.question?.time ?? 0;
    },
    disabledButtonWhileCheckOrStopped() {
      return ['stopped', 'paused'].includes(this.timer.state);
    }
  },

  watch: {
    elapsedTime(time) {
      if (time) {
        this.currentAnswer = {
          word: this.attempt ?? this.word,
          time,
        };
        this.saveAnswer();
      }
    },

    fail(value) {
      this.timer.bonus = 0;
      if (value) {
        this.timer.state = 'discounting';
        this.timer.bonus = -5;
      }
    },
  },

  mounted() {
    this.timer.state = 'discounting';
  },

  methods: {
    timerController(data) {
      if (data.state) {
        this.timer.state = data.state;
        if (this.timer.state === 'stopped') {
          this.unanswered();
        }
      }

      if (data.bonus !== undefined) {
        this.timer.bonus = data.bonus;
      }
    },

    saveTime(currentTime) {
      this.elapsedTime = (this.time - this.timeUsed) - currentTime;
    },

    pauseTimerToSendAttempt() {
      this.timer.state = 'paused';
    },

    async unanswered() {
      this.currentAnswer = {
        word: undefined,
        time: (this.time - this.timeUsed),
      };
      this.saveAnswer();
    },

    async saveAnswer() {
      this.$emit('save-answer', this.currentAnswer);
    },
  },
};
</script>

<style lang="scss" scoped>
.Board {
  .main-container {
    display: grid;
    gap: $spacing-xl;
    padding: $spacing-xl;

    .title-container {
      text-align: center;
      font-family: $font-family-primary;
      font-weight: bold;
      font-size: $font-size-3xl;
      color: $color-primary-dark;
    }

    .BaseButton {
      justify-self: center;
    }
  }
}
</style>

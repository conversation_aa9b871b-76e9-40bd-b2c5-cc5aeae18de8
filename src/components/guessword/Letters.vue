<template>
  <div class="Letters">
    <Container
      drag-handle-selector=".row-drag-handle"
      orientation="horizontal"
      class="container-board"
      lock-axis="x"
      :animation-duration="500"
      drag-class="card-ghost"
      drop-class="card-ghost-drop"
      @drop="onDrop"
    >
      <Draggable
        v-for="(letter, i) in letters"
        :id="i"
        :key="i"
        class="row-drag-handle"
      >
        <span class="letter">
          {{ letter.toUpperCase() }}
        </span>
      </Draggable>
    </Container>
  </div>
</template>

<script>
// eslint-disable-next-line import/no-extraneous-dependencies
import { Container, Draggable } from 'vue-smooth-dnd';

export default {
  components: {
    Container,
    Draggable,
  },

  props: {
    word: {
      type: Array,
      required: true,
    },
  },

  data() {
    return {
      letters: undefined,
    };
  },

  async created() {
    this.letters = this.word;
  },

  methods: {
    getItemsSorted({ removedIndex, addedIndex, payload }) {
      const elementRemoved = removedIndex !== null;
      const elementAdded = addedIndex !== null;
      if (!elementRemoved && !elementAdded) {
        return this.attempt;
      }

      const items = [...this.letters];
      let itemToAdd = payload;

      if (elementRemoved) {
        [itemToAdd] = items.splice(removedIndex, 1);
      }

      if (elementAdded) {
        items.splice(addedIndex, 0, itemToAdd);
      }

      return items;
    },

    onDrop(dropResult) {
      this.letters = this.getItemsSorted(dropResult);

      const answerWord = this.letters.join('');
      this.$emit('answer', answerWord);
    },
  },
};
</script>

<style lang="scss" scoped>
.Letters {
  text-align: center;
  font-family: $font-family-secondary;
  font-weight: bold;
  font-size: clamp(1.6rem, 7vw, 5rem);
  color: $color-neutral-dark;
  $shadow-color: adjust-color($color: $color-primary-dark, $alpha: -0.6);

  .card-ghost {
    transition: all 0.5s !important;
    color: $color-primary;
  }

  .smooth-dnd-ghost {
    transform: scale(1.25);
    animation: increase 0.3s ease-in-out;
  }

  .card-ghost-drop {
    transition: all 0.5s !important;
    color: $color-neutral-dark;
  }

  .container-board {
    margin: auto;

    .letter {
      text-shadow: 0px 0.1em 0px #{$shadow-color};

      @media #{$breakpoint-sm-max} {
        display: inline-block;
        width: 1.5ch;
        margin: 0 0.1rem;
        background: #809eab30;
      }

      &:hover,
      &:focus {
        cursor: grab;
      }

      &.correct {
        animation: shake 0.7s;
        color: adjust-color($color: $color-success, $lightness: -20%);
      }

      &.shake {
        animation: shake 0.7s;
        color: adjust-color($color: $color-error, $lightness: -20%);
      }
    }
  }
}
</style>

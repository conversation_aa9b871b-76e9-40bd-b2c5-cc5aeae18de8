<template>
  <div
    class="Panel"
    :class="question.state"
  >
    <span> {{ question.text }}</span>
  </div>
</template>

<script>

export default {
  props: {
    question: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      originalText: undefined,
    };
  },

  watch: {
    question(value) {
      const questions = value;
      if (value.state === 'incorrect') {
        setTimeout(() => {
          questions.state = undefined;
        }, 2000);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.Panel {
  text-align: center;
  font-family: $font-family-secondary;
  border-radius: $border-radius-m;
  color: $color-neutral-mid-darker;
  background-color: white;
  cursor: pointer;
  padding: 0.4em $spacing-xs;
  overflow: hidden;

  &.incorrect {
    box-shadow: inset 0 0 50px 2px $color-error;
    color: $color-error-dark;
    color: adjust-color($color: $color-error, $lightness: -30%);
    transition: all 0.2s ease-in-out;
    animation: bounce 0.5s;
  }

  &.correct {
    box-shadow: inset 0 0 50px 2px $color-success;
    color: adjust-color($color: $color-success, $lightness: -30%);
    background: adjust-color($color: $color-success, $lightness: 35%);
    pointer-events: none;
    animation: correct 0.5s;
  }
}
</style>

<template>
  <div class="Bar">
    <p class="plus">
      +
    </p>
  </div>
</template>

<script>
export default {

};
</script>

<style lang="scss" scoped>
.Bar{
  $successLighter: adjust-color($color: $color-success,$lightness: 35%);
  background: linear-gradient(180deg, $color-success, $successLighter);
  clip-path: polygon(100% 0, 0% 120%, 0 0%);
  border-radius: $border-radius-m;
  margin-top: $spacing-2xs;

  .plus {
    text-align: center;
    font-size: $font-size-3xl;
    font-weight: bold;
    color: #FFFFFF;
  }
}
</style>

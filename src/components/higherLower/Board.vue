<template>
  <div class="Board">
    <Timer
      :time="time"
      :state="timer.state"
      :bonus="timer.bonus"
      @current-time="saveTime"
      @timer-controller="timerController"
    />

    <div class="main-container max-content-width">
      <div class="title-container">
        {{ title }}
      </div>

      <div
        v-if="attempt"
        class="container-board"
      >
        <div
          class="items"
          :class="{blocked: selectedIndexes.length === 2, animated: animatedSelectedItems}"
        >
          <Panel
            v-for="(panel, i) in attempt"
            :key="panel.id"
            :question="panel"
            :class="[panel.state, { selected: selectedIndexes.includes(i) }]"
            @click.native="addItem(i)"
          />
        </div>

        <Bar />
      </div>

      <BaseButton
        class="send-attempt"
        :disabled="disabledButtonWhileCheckOrStopped || selectedIndexes.length === 2"
        @click="pauseTimerToSendAttempt"
      >
        {{ $t('SOLVE.BUTTON') }}
        <font-awesome-icon icon="fa-solid fa-check" />
      </BaseButton>
    </div>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import Timer from '@/components/common/Timer';
import Bar from '@/components/higherLower/Bar';
import Panel from '@/components/higherLower/Panel';
import BaseButton from '@/components/base/BaseButton';

const STORE = 'HigherLowerModule';
const PANEL_STATES = { INCORRECT: 'incorrect', CORRECT: 'correct' };

export default {
  components: {
    Timer,
    Bar,
    Panel,
    BaseButton,
  },

  props: {
    question: {
      type: Object,
      default: undefined,
    },

    initBonus: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      timer: {
        bonus: this.initBonus,
        state: 'stopped',
      },
      elapsedTime: 0,
      attempt: undefined,
      currentAnswer: undefined,
      timeUsed: Math.abs(this.initBonus),
      selectedIndexes: [],
      animatedSelectedItems: false,
      timeOut: false
    };
  },

  computed: {
    ...get(STORE, ['getNextQuestion']),
    questionId() {
      return this.question?.id;
    },

    title() {
      return this.question?.title;
    },

    time() {
      return this.question?.time;
    },

    options() {
      return this.question?.questions;
    },
    disabledButtonWhileCheckOrStopped() {
      return ['stopped', 'paused'].includes(this.timer.state);
    }
  },

  watch: {
    elapsedTime(time) {
      if (time) {
        this.currentAnswer = {
          questionId: this.questionId,
          ids: this.attempt.map(({ id }) => id),
          time,
        };
        this.saveAnswer();
      }
    },

    selectedIndexes: {
      deep: true,
      async handler(indexes) {
        if (indexes.length >= 2) {
          await this.animateSelectedItems();

          this.swapPositions(indexes);

          this.selectedIndexes = [];
        }
      },
    }
  },

  created() {
    this.attempt = this.shuffle([...this.options]);
  },

  mounted() {
    this.timer.state = 'discounting';
  },

  methods: {
    shuffle(items) {
      const randomItem = Math.floor(Math.random() * items.length);
      const item = items[randomItem];

      if (items.length === 0) return [];

      items.splice(randomItem, 1);
      return [item, ...this.shuffle(items)];
    },

    timerController(data) {
      if (data.state) {
        this.timer.state = data.state;
        if (this.timer.state === 'stopped') {
          this.unanswered();
        }
      }

      if (data.bonus !== undefined) {
        this.timer.bonus = data.bonus;
      }
    },

    saveTime(currentTime) {
      this.elapsedTime = this.time - currentTime;
    },

    pauseTimerToSendAttempt() {
      this.timer.state = 'paused';
    },

    addItem(index) {
      if (this.selectedIndexes.length < 2) {
        this.selectedIndexes.push(index);
      }
    },

    swapPositions(indexes) {
      const [index1, index2] = indexes;

      const item1 = this.attempt[index1];
      const item2 = this.attempt[index2];

      this.attempt[index2] = item1;
      this.attempt[index1] = item2;
    },

    async animateSelectedItems() {
      await this.waitingForFinishAnimation(800);
      this.animatedSelectedItems = true;

      await this.waitingForFinishAnimation(500);
      this.animatedSelectedItems = false;
    },

    async unanswered() {
      this.currentAnswer = {
        questionId: this.questionId,
        ids: this.attempt.map(({ id }) => id),
        time: this.time,
      };
      this.timeOut = true;
      this.saveAnswer();
    },

    async saveAnswer() {
      const result = await this.$store.dispatch(`${STORE}/checkAnswer`, this.currentAnswer);
      this.attempt = this.loadStateOnAttempt(result);

      const isFinished = result.every((r) => r?.correct);
      const timeFinished = this.timeOut;
      const nextQuestion = this.getNextQuestion();

      await this.waitingForFinishAnimation(1500);

      if (isFinished || !nextQuestion || (timeFinished && nextQuestion)) {
        this.$emit('next-question', this.timeOut);
        this.timeOut = false;
      } else {
        this.timeUsed += this.currentAnswer.time;
        this.timer.state = 'discounting';
        this.timer.bonus = -5;
      }
    },

    loadStateOnAttempt(attemptChecked) {
      return this.attempt.map((attempt, i) => {
        const state = attemptChecked[i]?.correct ? PANEL_STATES.CORRECT : PANEL_STATES.INCORRECT;
        return { ...attempt, state };
      });
    },

    waitingForFinishAnimation(delay) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve();
        }, delay);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.Board {
  user-select: none;
  .main-container {
    padding: 0 1rem;
  }

  .title-container {
    padding: $spacing-m;
    text-align: center;
    font-family: $font-family-secondary;
    font-size: $font-size-l;
  }

  .container-board {
    display: grid;
    gap: $spacing-xs;
    margin: 0 auto;
    margin-bottom: $spacing-m;
    max-width: 500px;
    width: 100%;
    grid-template-columns: 1fr auto;

    .items {
      &.blocked{
        pointer-events: none;
      }

      &.animated .Panel.selected {
        background-color: $color-neutral-lighter;
        transform: rotateX(90deg);
      }

      .Panel {
        word-wrap: break-word;
        margin-bottom: $spacing-2xs;
        transition: all 0.4s ease-in-out;

        &.selected{
          font-weight: bold;
          color: $color-primary;
          pointer-events: none;
        }
      }

      @for $i from 1 through 12 {
        .Panel:nth-child(#{$i}) {
          font-size: 160 - $i * 7%;

          @media #{$breakpoint-sm-max} {
            font-size: 120 - $i * 4%;
          }
        }
      }
    }

    .Bar {
      width: 50px;
    }
  }

  .send-attempt {
    margin: auto;
  }
}
</style>

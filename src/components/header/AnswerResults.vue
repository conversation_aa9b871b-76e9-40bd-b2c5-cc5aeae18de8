<template>
  <div
    v-if="answerResults"
    class="AnswerResuts"
  >
    <AnswerResult
      v-for="(answerResult, i) in answerResults"
      :key="i"
      :type="answerResult"
    />
  </div>
</template>

<script>
import AnswerResult from '@/components/header/AnswerResult';

export default {
  components: {
    AnswerResult,
  },

  props: {
    answerResults: {
      type: Array,
      default: undefined,
    },
  },
};
</script>

<style lang="scss" scoped>
.AnswerResuts {
  display: flex;
  gap: $spacing-2xs;
}
</style>

<template>
  <div
    class="AnswerResultResponsive"
    :class="[`type--${classType}`]"
  >
    {{ resultNumber }}
  </div>
</template>

<script>
const types = ['unanswered', 'correct', 'incorrect'];
export default {
  props: {
    type: {
      type: String,
      default: 'unanswered',
      validator: (type) => types.includes(type),
    },
    resultNumber: {
      type: String,
      default: '',
    },
  },

  computed: {
    classType() {
      return (this.type) ? this.type : 'unanswered';
    },
  },

};
</script>

<style lang="scss" scoped>
.AnswerResultResponsive {
  height: 50px;
  width: 50px;
  background: var(--local-background);
  font-family: $font-family-secondary;
  font-size: $font-size-2xl;
  font-weight: 600;
  color: #fff;
  display: grid;
  place-content: center;
  // border: 2px solid var(--local-border-color);
  border-radius: $border-radius-xl;
  box-shadow: $shadow-elevation-1;

  &.type {
    &--correct {
      --local-background: #{$color-success};
    }
    &--incorrect {
      --local-background: #{$color-error};
    }
  }
  @media #{$breakpoint-sm-max} {
    height: 32px;
    width: 32px;
    font-size: $font-size-s;
  }
}
</style>

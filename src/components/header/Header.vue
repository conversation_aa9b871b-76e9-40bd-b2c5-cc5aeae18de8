<template>
  <div :class="['Header', { 'Header--visible': visibleInLandscape }]">
    <div class="header-content">
      <h1 class="title">
        {{ title }}
      </h1>

      <slot />
    </div>
    <div
      v-if="!visibleInLandscape"
      class="header-slide"
      @click="handleHeader"
    >
      <div>{{ $t('SHOW.HEADER.MOBILE.BUTTON') }}</div> <font-awesome-icon icon="fa-solid fa-angle-down" />
    </div>
  </div>
</template>

<script>

export default {
  props: {
    title: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      visibleInLandscape: false,
      headerElement: null,
      maxMobileWidth: 932,
      headerDisplayMS: 5000
    };
  },
  mounted() {
    this.headerElement = document.querySelector('.header');
    if (this.headerElement) {
      this.headerElement.style.transition = 'all .3s ease-in-out';
      window.addEventListener('resize', this.handleHeader);
      window.addEventListener('orientationchange', this.handleHeader);
      this.handleHeader();
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleHeader);
    window.removeEventListener('orientationchange', this.handleHeader);
  },
  methods: {
    handleHeader() {
      this.visibleInLandscape = true;
      this.headerElement.style.maxHeight = '100px';
      setTimeout(() => {
        if (this.isMobileInLandscape()) {
          this.visibleInLandscape = false;
          this.headerElement.style.maxHeight = '0px';
        }
      }, this.headerDisplayMS);
    },
    isMobileInLandscape() {
      const isLandscape = window.matchMedia('(orientation: landscape)').matches;
      const isMobile = window.innerWidth <= this.maxMobileWidth;
      return isMobile && isLandscape;
    },
  },
};
</script>

<style lang="scss" scoped>
.Header {
  background: rgba(255, 255, 255, 0.67);
  display: flex;
  transition: all .3s ease-in-out;

  .header-slide {
    cursor: pointer;
    color: white;
    background-color: $color-primary;
    transform: translateY(5rem);
    width: auto;
    height: max-content;
    display: none;
    padding: #{$spacing-3xs} #{$spacing-xs};
    border-radius: 5px;
    margin-right: 1rem;

    @media (max-width: 932px) and (orientation: landscape) {
      display: flex;
      white-space: nowrap;
      gap: .5rem;
      align-items: center;
    }
  }

  .header-content {
    max-width: 1200px;
    width: 100%;
    margin: auto;
    display: flex;
    flex-flow: row wrap;
    gap: $spacing-s;
    align-items: center;
    padding: #{$spacing-s} #{$spacing-m};

    @media #{$breakpoint-md-max} {
      padding: 0px #{$spacing-s};
    }

    .title {
      flex: 1;
      font-size: $font-size-3xl;
      color: $color-primary;
      text-transform: uppercase;
      font-weight: 500;
    }

    @media #{$breakpoint-sm-max} {
      .title {
        font-size: $font-size-xl;
      }
    }
  }

  &.Header--visible {
    @media (max-width: 932px) and (orientation: landscape) {
      transform: translateY(0);
    }
  }

  @media (max-width: 932px) and (orientation: landscape) {
    background: transparent !important;
    transform: translateY(-100%);
  }
}
</style>

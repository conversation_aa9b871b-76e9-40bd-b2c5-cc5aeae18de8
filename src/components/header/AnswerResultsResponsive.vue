<template>
  <div
    v-if="answerResults && isVisible"
    class="AnswerResuts"
  >
    <AnswerResultResponsive
      v-for="(answerResult, i) in answerResults"
      :key="i"
      :type="answerResult.type"
      :result-number="answerResult.value.toString()"
    />

    <PieChart
      v-if="progress"
      :current="progress.current"
      :total="progress.total"
    />
  </div>
</template>

<script>
import AnswerResultResponsive from '@/components/header/AnswerResultResponsive';
import Pie<PERSON>hart from '@/components/common/PieChart';

export default {
  components: {
    AnswerResultResponsive,
    Pie<PERSON><PERSON>,
  },

  props: {
    answerResults: {
      type: Array,
      default: undefined,
    },

    progress: {
      type: Object,
      default: undefined,
      validator: (progress) => ('current' in progress) && ('total' in progress),
    },
    
    isVisible: {
      type: Boolean,
      default: true
    }
  },
};
</script>

<style lang="scss" scoped>
.AnswerResuts {
  display: flex;
  gap: $spacing-s;
  align-items: center;

  .progress{
    display: grid;
    grid-template-columns: repeat(2, auto);
    align-items: center;
    padding: 0 $spacing-l;
    gap: $spacing-xs;
    font-family: $font-family-secondary;
    font-size: $font-size-2xl;
    font-weight: 500;
    text-transform: uppercase;
    // background: $color-neutral-mid-darker;
    background: #fff;
    border-radius: $border-radius-xl;
    color: $color-primary-dark;
    box-shadow: $shadow-elevation-1;

    b{
      font-weight: 500;
      color: $color-neutral-darker;
    }
  }
}
</style>

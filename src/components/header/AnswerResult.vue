<template>
  <div
    class="AnswerResult"
    :class="[`type--${classType}`]"
  />
</template>

<script>
const types = ['unanswered', 'correct', 'incorrect'];
export default {
  props: {
    type: {
      type: String,
      default: 'unanswered',
      validator: (type) => types.includes(type),
    },
  },

  computed: {
    classType() {
      return (this.type) ? this.type : 'unanswered';
    },
  },

};
</script>

<style lang="scss" scoped>
.AnswerResult {
  height: 50px;
  width: 50px;
  background-image: var(--local-background-image);
  background-size: cover;
  background-position: center;
  background-color: var(--background-color);
  border: 2px solid var(--local-border-color);
  border-radius: $border-radius-xl;

  &.type {
    &--correct{
      --local-background-image: url("~@/assets/images/check.svg");
      --background-color: #fff;
      --local-border-color: #fff;
    }
    &--incorrect{
      --local-background-image: url("~@/assets/images/cross.svg");
      --background-color: #fff;
      --local-border-color: transparent;
    }
    &--unanswered{
      --local-background-image: url("~@/assets/images/question-mark.svg");
      --background-color: #{$color-neutral-darker};
      --local-border-color: #{$color-neutral-lighter};
    }
  }
}

@media #{$breakpoint-sm-max} {
  .AnswerResult{
    height: 35px;
    width: 35px;
  }
}
</style>

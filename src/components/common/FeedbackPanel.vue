<template>
  <div :class="['FeedbackPanel', `state--${state}`]">
    <div :class="['icon']" />

    <h2 class="title">
      {{ title }}
    </h2>

    <div class="content">
      <slot name="content" />
    </div>

    <slot name="footer" />
  </div>
</template>

<script>
export default {
  props: {
    state: {
      type: String,
      default: undefined,
      validator: (value) => ['correct', 'incorrect'].includes(value),
    },
  },

  computed: {
    title() {
      const messageKey = this.state === 'correct' ? 'ANSWERPANEL.SUCCESS.TITLE' : 'ANSWERPANEL.FAILURE.TITLE';
      return this.$t(messageKey);
    },
  },
};
</script>

<style lang="scss" scoped>
.FeedbackPanel {
  display: grid;
  padding: $spacing-m;
  // gap: $spacing-l;
  text-align: center;
  justify-items: center;

  &.state--correct {
    --title-color: #{$color-success-dark};
    --image: url('~@/assets/images/correct.png');
    --image-background: #{$color-success-light};
    --animation: correct 0.5s;
  }

  &.state--incorrect {
    --title-color: #{$color-error-dark};
    --image: url('~@/assets/images/incorrect.png');
    --image-background: #{$color-error-light};
    --animation: bounce 0.5s;
  }

  // .icon{
  //   margin: auto;
  //   font-size: $font-size-5xl;
  // }

  .icon {
    width: 100px;
    height: 100px;
    margin: auto;
    border-radius: 50px;
    background-size: cover;
    background-image: var(--image);
    background-color: var(--image-background);
    margin-bottom: $spacing-l;
    animation: var(--animation);
  }

  .title {
    color: var(--title-color);
    font-weight: 800;
    font-family: $font-family-secondary;
    font-size: $font-size-2xl;
    margin-bottom: $spacing-s;
  }

  .content {
    font-size: $font-size-m;
    margin-bottom: $spacing-m;
  }
}
</style>

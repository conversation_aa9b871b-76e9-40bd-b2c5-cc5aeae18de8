<template>
  <div
    v-if="timers"
    class="MultiTimer"
  >
    <div
      v-for="(timer, i) in timers"
      :key="i"
      class="timer-capsule"
    >
      <Timer
        :time="timer.time"
        :bonus="timer.bonus"
        :state="timer.state"
        @current-time="saveTime"
        @timer-controller="timerController"
      />
    </div>
  </div>
</template>

<script>

import Timer from '@/components/common/Timer';

export default {
  components: {
    Timer,
  },

  props: {
    timers: {
      type: Array,
      required: true,
    },
  },

  methods: {
    timerController(data) {
      this.$emit('timer-controller', data);
    },

    saveTime(currentTime) {
      this.$emit('current-time', currentTime);
    },
  },
};
</script>

<style lang="scss" scoped>
.MultiTimer{
  display: flex;
  gap: $spacing-2xs;

  .timer-capsule{
    flex: 1;
  }
}
</style>

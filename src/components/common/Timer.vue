<template>
  <div
    class="Timer"
    :class="[themeClass]"
  >
    <div
      class="time"
      :style="progress"
    />
  </div>
</template>

<script>
const states = ['stopped', 'paused', 'discounting', 'counting'];

export default {
  props: {
    time: {
      type: Number,
      default: undefined,
    },

    state: {
      type: String,
      default: 'stopped',
      validator: (state) => states.includes(state),
    },

    bonus: {
      type: Number,
      default: 0,
    },
  },

  data() {
    const currentTime = this.time + this.bonus;
    return {
      currentTime: (currentTime < this.time) ? currentTime : this.time,
      tickInterval: undefined,
    };
  },

  computed: {
    progress() {
      const currentWidth = (this.currentTime / this.time) * 100;
      return `width: ${currentWidth}%`;
    },

    direction() {
      return (this.state === 'discounting') ? -1 : 1;
    },

    isRunning() {
      return (this.state === 'discounting' || this.state === 'counting');
    },

    hasFinnish() {
      if ((this.direction === -1) && (this.currentTime <= 0)) return true;
      if ((this.direction === 1) && (this.currentTime >= this.time)) return true;

      return false;
    },
  },

  watch: {
    state(newState, oldState) {
      if ((oldState === 'stopped') && (newState === 'discounting')) {
        this.currentTime = this.time + this.bonus;
      }

      if ((oldState === 'stopped') && (newState === 'counting')) {
        this.currentTime = 0;
      }

      if (newState === 'paused') {
        this.$emit('current-time', this.currentTime);
      }
    },

    bonus(newBonus, oldBonus) {
      if (newBonus !== oldBonus) {
        this.currentTime += newBonus;
      }
    },
  },

  mounted() {
    let lastUpdate = Date.now();
    this.tickInterval = setInterval(() => {
      const now = Date.now();
      const deltaTime = (now - lastUpdate) / 1000;
      lastUpdate = now;

      if (this.isRunning) {
        this.tick(deltaTime);
      }
    }, 100);
  },

  beforeDestroy() {
    clearInterval(this.tickInterval);
  },

  methods: {
    tick(deltaTime) {
      if (this.hasFinnish) {
        this.finnish();
      } else {
        this.currentTime += (this.direction * deltaTime);

        if (this.bonus !== 0) {
          this.resetBonus();
        }
      }
    },

    resetBonus() {
      this.$emit('timer-controller', { bonus: 0 });
    },

    finnish() {
      this.currentTime = (this.direction === 1) ? this.time : 0;
      this.$emit('timer-controller', { state: 'stopped' });
    },
  },

};
</script>

<style lang="scss" scoped>
.Timer{
  background-color: var(--local-background-color);
  height: 10px;
  box-shadow: $shadow-elevation-0;
  border-radius: $border-radius-s;
  overflow: hidden;

  &.theme{
    &--light{
      --local-background-color: #{adjust-color($color: $color-neutral-lighter, $alpha: -.7)};
    }

    &--dark{
      --local-background-color: #{adjust-color($color: $color-neutral-darker, $alpha: -.7)};
    }
  }

  .time{
    background-color: $color-primary;
    height: 100%;
    transition: all .1s ease;
    border-radius: $border-radius-s;
  }
}

</style>

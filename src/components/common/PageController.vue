<template>
  <div class="PageController">
    <component
      :is="currentPageContent"
      :questions="questions"
      @change-page="changePage"
    />
  </div>
</template>

<script>
import Home from '@/views/Home';
import Game from '@/views/Game';
import Finnish from '@/views/Finnish';

export default {
  name: 'PageController',
  components: {
    Home,
    Game,
    Finnish,
  },

  data() {
    return {
      questions: [],
      pages: ['Home', 'Game', 'Finnish'],
      currentPage: 0,
    };
  },

  computed: {
    currentPageContent() {
      return this.pages[this.currentPage];
    },
  },

  methods: {
    changePage(page) {
      this.currentPage = page && this.pages.length > page && page >= 0 ? page : 0;
    },
  },
};
</script>

<style lang="scss" scoped>
.PageController {
  flex: 1;
  display: flex;
}
</style>

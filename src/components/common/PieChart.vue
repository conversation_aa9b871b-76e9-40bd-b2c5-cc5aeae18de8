<template>
  <div class="PieChart">
    <canvas
      :width="canvasSize.width"
      :height="canvasSize.height"
    />

    <div class="circle">
      <span>
        {{ current }}
        <b>{{ `/ ${total}` }}</b>
      </span>
    </div>

    <span class="scss" />
  </div>
</template>

<script>
const STARTING_POINT = 4.72;
const ARC_SIZE = 5;

export default {
  props: {
    current: {
      type: Number,
      default: 0,
    },

    total: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      counter: undefined,
      currentValue: 0,
      diff: undefined,
      fill: undefined,
      colorSuccess: undefined,
      colorBackground: undefined,
      canvasSize: {
        width: 80,
        height: 70,
      },
    };
  },

  computed: {
    value() {
      return (this.current / this.total) * 100;
    },
  },

  watch: {
    value(newValue, oldValue) {
      const step = newValue > oldValue ? 1 : -1;
      this.fill = setInterval(() => {
        this.fillCounter(step);
      }, 20);
    },
  },

  mounted() {
    this.counter = this.$el.querySelector('canvas').getContext('2d');

    this.loadScssValues();

    this.fill = setInterval(() => {
      this.fillCounter(+1);
    }, 20);
    window.addEventListener('resize', this.updateCanvasSize);
    this.updateCanvasSize();
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.updateCanvasSize);
  },
  methods: {
    loadScssValues() {
      const scssDOM = this.$el.querySelector('.scss');
      this.colorSuccess = window.getComputedStyle(scssDOM, null).getPropertyValue('color');
      this.colorBackground = window.getComputedStyle(scssDOM, null).getPropertyValue('background-color');
    },

    fillCounter(increment) {
      this.diff = (this.currentValue / 100) * Math.PI * 2 * 10;

      const { width, height } = this.counter.canvas;
      // Clear canvas
      this.counter.clearRect(0, 0, width, height);

      this.counter.lineWidth = ARC_SIZE;
      this.counter.beginPath();
      this.counter.strokeStyle = this.colorBackground;
      let center = this.$el.clientHeight / 2;
      center = center < ARC_SIZE ? ARC_SIZE : center;
      this.counter.arc(center, center, center - ARC_SIZE, STARTING_POINT, Math.PI * 2 + STARTING_POINT);
      this.counter.stroke();

      this.counter.beginPath();
      this.counter.strokeStyle = this.colorSuccess;
      // arc(x,y,radius,start,stop)
      this.counter.arc(center, center, center - ARC_SIZE, STARTING_POINT, this.diff / 10 + STARTING_POINT);

      this.counter.stroke();

      const finishAscending = increment > 0 && this.currentValue >= this.value;
      const finishDescending = increment < 0 && this.currentValue <= this.value;
      if (finishAscending || finishDescending) {
        clearTimeout(this.fill);

        if (this.currentValue === 100) {
          this.$emit('finish');
        }
      } else {
        this.currentValue += increment;
      }
    },
    updateCanvasSize() {
      if (window.innerWidth < 576) {
        this.canvasSize.width = 62;
        this.canvasSize.height = 62;
      } else {
        this.canvasSize.width = 70;
        this.canvasSize.height = 70;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.PieChart {
  display: flex;
  align-items: center;
  position: relative;
  transition: all 0.3s ease;

  .circle {
    background: $color-neutral-lightest;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    border-radius: 50px;
    height: 34px;
    width: 34px;
    margin: auto;
    display: grid;
    place-content: center;
    color: $color-neutral-darker;
    font-weight: 500;
    white-space: nowrap;

    span {
      @media #{$breakpoint-sm-max} {
        font-size: $font-size-xs;
      }
    }
  }

  b {
    font-weight: 600;
  }

  .scss {
    display: none;
    color: $color-success;
    background-color: $color-neutral-mid;
  }
}
</style>

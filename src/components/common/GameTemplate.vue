<template>
  <div class="GameTemplate">
    <BaseLoader v-if="loading" />

    <template v-else>
      <div class="header">
        <slot name="header" />
      </div>

      <div class="main">
        <slot name="main" />
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="scss" scoped>
.GameTemplate {
  height: 100vh;
  display: grid;
  overflow: hidden;
  align-items: flex-start;
  grid-template-rows: auto 1fr;

  .main {
    display: grid;
    align-content: center;
    grid-template-columns: 100%;
    grid-template-rows: 1fr;
    height: 100%;
    align-content: flex-start;
    //padding-bottom: 8rem;
    color: #000;
    overflow-x: hidden;
    overflow-y: auto;
  }
}
</style>

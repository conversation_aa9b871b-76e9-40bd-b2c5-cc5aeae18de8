<template>
  <div
    class="ScorePanel"
    :class="titleClass"
  >
    <!-- <h1
      v-if="titleImage && titleClass"
      class="title"
      :class="`title-color-${titleClass}`"
    >
      <img
        :src="titleImage"
        alt="tick"
      >
      <span>{{ $t('SCOREPANEL.TITLE') }}</span>
    </h1> -->

    <img
      class="finish-image"
      src="~@/assets/images/finish.svg"
      alt="finish image"
    />

    <DeepContent v-html="description" />

    <TransitionSlide>
      <div
        v-if="score !== undefined"
        class="score"
      >
        <div class="percentage">
          <span class="current">{{ currentScore }}</span>
          <span class="statement">{{ $tc('POINTS', currentScore) }}</span>
        </div>
      </div>
    </TransitionSlide>

    <div class="button-box">
      <TransitionSlide side="bottom">
        <BaseButton
          v-if="buttonVisible"
          class="BaseButton"
          @click.native="$emit('close-game')"
        >
          {{ $t('FINISH.BUTTON') }}
        </BaseButton>
      </TransitionSlide>
    </div>
  </div>
</template>

<script>
import TransitionSlide from '@/transitions/TransitionSlide';
import DeepContent from '@/components/common/DeepContent';

// import checkIcon from '@/assets/images/check.svg';
// import crossIcon from '@/assets/images/cross.svg';

export default {
  components: {
    TransitionSlide,
    DeepContent,
  },

  props: {
    score: {
      type: Number,
      default: undefined,
    },

    description: {
      type: String,
      default: undefined,
    },

    check: {
      type: Boolean,
      default: undefined,
    },
  },

  data() {
    return {
      currentScore: 0,
      buttonVisible: false,
    };
  },

  computed: {
    currentDelay() {
      return 500 / (this.score - this.currentScore);
    },

    titleClass() {
      return this.score === undefined && this.check === false ? 'danger' : 'success';
    },

    // titleImage() {
    //   return (this.titleClass === 'danger') ? crossIcon : checkIcon;
    // },
  },

  watch: {
    score(newScore) {
      if (newScore) {
        setTimeout(this.upgradeScore, this.currentDelay);
      }
    },
  },

  mounted() {
    if (this.score) {
      setTimeout(this.upgradeScore, this.currentDelay);
    } else {
      this.buttonVisible = true;
    }
  },

  methods: {
    upgradeScore() {
      if (this.currentScore < this.score) {
        this.currentScore += 1;
        setTimeout(this.upgradeScore, this.currentDelay);
      } else {
        this.addFinishAnimation();
      }
    },

    addFinishAnimation() {
      const current = this.$el.querySelector('.current');
      if (current) {
        current.classList.add('finish');
        this.buttonVisible = true;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.ScorePanel {
  padding: $spacing-m;
  display: grid;
  overflow: hidden;

  .finish-image {
    margin: auto;
    max-height: clamp(200px, 16vw, 300px);
  }

  .DeepContent {
    text-align: center;

    ::v-deep b {
      font-size: $font-size-3xl;
    }
  }

  .button-box{
    justify-self: center;
  }

  &.danger .DeepContent::v-deep b {
    color: $color-error-dark;
  }

  .score {
    text-align: center;

    .percentage {
      .current {
        color: $color-primary;
        font-size: $font-size-5xl;
        font-weight: 500;
        position: relative;
        margin-right: $spacing-s;

        &.finish {
          animation: finish 0.5s ease 0.5s;
        }
      }

      .statement {
        color: $color-primary-dark;
        font-size: $font-size-2xl;
      }
    }
  }

  .BaseButton {
    margin-left: auto;
  }

  @keyframes finish {
    0% {
      bottom: 0;
      color: $color-primary-light;
    }
    50% {
      bottom: 5px;
    }
    100% {
      bottom: 0;
    }
  }
}
</style>

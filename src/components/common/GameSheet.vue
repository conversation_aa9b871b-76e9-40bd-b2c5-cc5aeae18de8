<template>
  <div class="GameSheet">
    <TransitionSlide side="top">
      <div class="sheet">
        <h1 v-if="title">
          {{ title }}
        </h1>
        <p v-if="description">
          {{ description }}
        </p>
      </div>
    </TransitionSlide>

    <TransitionSlide>
      <div
        v-if="rounds"
        class="rounds"
      >
        <span>{{ rounds.completed }}</span> / <span>{{ rounds.total }}</span>
      </div>
    </TransitionSlide>
  </div>
</template>

<script>

import TransitionSlide from '@/transitions/TransitionSlide';

export default {
  components: {
    TransitionSlide,
  },

  props: {
    title: {
      type: String,
      default: undefined,
    },

    description: {
      type: String,
      default: undefined,
    },

    rounds: {
      type: Object,
      default: undefined,
    },
  },

};
</script>

<style lang="scss" scoped>
.GameSheet{
  padding: $spacing-m;
  display: flex;
  flex-direction: column;

  .sheet{
    h1{
      font-size: $font-size-5xl;
      font-weight: 600;
      margin-bottom: $spacing-l;
    }

    p{
      font-size: $font-size-2xl;
      margin-bottom: $spacing-xl;
    }
  }

  .rounds{
    font-size: $font-size-5xl;
    font-weight: 600;

    span:first-child{
      color: $color-primary;
    }
  }

  @media #{$breakpoint-sm-min} {
    padding: $spacing-2xl;
  }

  @media #{$breakpoint-xl-max} {
    .sheet{
      h1{
        font-size: $font-size-4xl;
        font-weight: 600;
        margin-bottom: $spacing-m;
      }

      p{
        font-size: $font-size-xl;
        margin-bottom: $spacing-l;
      }
    }

    .rounds{
      font-size: $font-size-4xl;
      font-weight: 500;
    }
  }
}
</style>

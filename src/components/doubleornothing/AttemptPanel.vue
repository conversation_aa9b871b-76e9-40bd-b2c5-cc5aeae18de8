<template>
  <div class="AttemptPanel">
    <FeedbackPanel state="correct">
      <template v-slot:content>
        <p>
          {{ $t('ANSWERPANEL.SUCCESS.DESCRIPTION') }}
        </p>

        <p class="attempt-question">
          {{ $t('ANSWERPANEL.SUCCESS.QUESTION.STATEMENT') }}
        </p>
      </template>

      <template v-slot:footer>
        <div class="button-box">
          <BaseButton
            variation="secondary"
            @click.native="$emit('modal-answer', false)"
          >
            {{ $t('ANSWERPANEL.SUCCESS.QUESTION.OPTION.1') }}
            <font-awesome-icon icon="fa-regular fa-face-frown" />
          </BaseButton>

          <BaseButton @click.native="$emit('modal-answer', true)">
            {{ $t('ANSWERPANEL.SUCCESS.QUESTION.OPTION.2') }}
            <font-awesome-icon icon="fa-solid fa-face-laugh-beam" />
          </BaseButton>
        </div>
      </template>
    </FeedbackPanel>
  </div>
</template>

<script>
import FeedbackPanel from '@/components/common/FeedbackPanel';

export default {
  components: {
    FeedbackPanel,
  },
};
</script>

<style lang="scss" scoped>
.AttemptPanel {
  .title {
    text-align: center;
    font-weight: bold;
    font-size: $font-size-3xl;
    margin-bottom: $spacing-xl;
    color: $color-primary;

    display: flex;
    align-items: center;
  }

  img {
    height: 50px;
    margin-right: $spacing-xs;
  }

  p {
    text-align: justify;
    margin-bottom: $spacing-xl;
  }

  .attempt-question {
    font-size: $font-size-l;
    font-weight: 500;
    text-align: center;
  }

  .button-box {
    display: flex;
    gap: $spacing-s;
    flex-wrap: wrap;
    justify-content: space-around;

    // .error-button:hover {
    //   color: $color-error;
    //   background: transparent;
    // }
  }
}
</style>

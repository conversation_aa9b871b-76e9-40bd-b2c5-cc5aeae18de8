<template>
  <div class="ScoreTable">
    <div
      v-for="(score, index) in getScores"
      :id="index"
      :key="index"
      class="score"
      :class="[`type--${score.scoreClass}`]"
    >
      {{ score.title }}

      <span class="points">{{ score.title }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    results: {
      type: Array,
      default: undefined,
    },

    maxScore: {
      type: Number,
      default: undefined,
    },
  },

  computed: {
    scores() {
      const multiplier = 2 ** 4; // (5 - 1)
      const extra = this.maxScore % multiplier;
      let step = (this.maxScore - extra) / multiplier;

      const scores = [];
      for (let i = 0; i < 4; i += 1) {
        scores.push({ title: `x${2 ** i}`, points: `+${step}` });
        step *= 2;
      }
      scores.push({ title: `x${multiplier}`, points: `+${step + extra}` });

      return scores;
    },

    getScores() {
      const currentIndex = this.results.findIndex((result) => !result);
      return this.scores?.map((score, i) => {
        const isCorrect = this.results[i] ?? false;

        let scoreClass = 'incorrect';
        if (isCorrect) {
          scoreClass = 'correct';
        } else if (i === currentIndex) {
          scoreClass = 'current';
        }

        return Object.assign(score, { scoreClass });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.ScoreTable {
  display: flex;
  flex-direction: column-reverse;
  justify-content: flex-end;
  text-align: center;
  font-size: $font-size-xl;
  gap: $spacing-xs;

  .score {
    background: var(--local-background-color);
    color: var(--local-color);
    padding: $spacing-s;
    border: 2px solid transparent;
    border-radius: $border-radius-m;
    box-shadow: $shadow-elevation-1;

    &.type {
      &--current {
        --local-background-color: #{$color-neutral-lightest};
        --local-color: #{$color-primary-dark};
        animation: pulse 2s ease-out infinite;
      }

      &--correct {
        --local-background-color: #{$color-primary};
        --local-color: #{$color-neutral-lightest};
      }

      &--incorrect {
        --local-background-color: #{$color-neutral-mid-light};
        --local-color: #{$color-neutral-mid-darker};
      }
    }

    position: relative;

    .points {
      opacity: 0;
      position: absolute;
      left: 50%;
      top: 10%;
      white-space: nowrap;
      border-radius: $border-radius-m;
      font-size: $font-size-2xl;
      color: #fff;
      background-color: $color-success;
      padding: $spacing-xs;
      z-index: 10;
      transition: all 1.5s ease;

      &.correct {
        animation: side 2s cubic-bezier(0.26, 0.65, 0.37, 0.99);
        left: 90%;
        top: 0%;
      }
    }
  }

  .active {
    background-color: $color-primary;
  }
}

@keyframes pulse {

  0%,
  50%,
  100% {
    border-color: $color-primary;
  }

  25%,
  75% {
    border-color: transparent;
  }
}

@keyframes side {
  0% {
    left: 50%;
    top: 20%;
  }

  50% {
    left: 90%;
    top: 0%;
  }

  85% {
    opacity: 1;
  }
}

@keyframes fade {
  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@media #{$breakpoint-lg-max} {
  .ScoreTable {
    flex-direction: initial;
    margin-bottom: $spacing-2xs;

    .score {
      width: 100%;
      padding: $spacing-xs;
      text-align: center;
    }
  }
}</style>

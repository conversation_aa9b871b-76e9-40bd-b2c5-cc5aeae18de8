<template>
  <div class="FailPanel">
    <FeedbackPanel state="incorrect">
      <template v-slot:content>
        {{ $t('ANSWERPANEL.FAILURE.DESCRIPTION') }}
      </template>
    </FeedbackPanel>
  </div>
</template>

<script>
import FeedbackPanel from '@/components/common/FeedbackPanel';

export default {
  components: {
    FeedbackPanel,
  },
  mounted() {
    setTimeout(() => {
      this.$emit('modal-answer', false);
    }, 4000);
  },
};
</script>

<style lang="scss" scoped>
.FailPanel {
  padding: $spacing-xl;

  .title {
    text-align: center;
    font-weight: bold;
    font-size: $font-size-3xl;
    margin-bottom: $spacing-m;
    color: #{$color-error};

    display: flex;
    align-items: center;

    img {
      height: 50px;
      margin-right: $spacing-xs;
    }
  }

  p {
    text-align: justify;
    margin-bottom: $spacing-m;
  }
}
</style>

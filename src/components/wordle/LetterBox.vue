<template>
  <div class="LetterBox">
    <div
      class="box"
      :class="{ flipped: letter.isFlipped }"
    >
      <div
        class="box-face front"
        :class="{ filled: letter.isFilled }"
      >
        {{ letter.wordLetter }}
      </div>
      <div
        class="box-face back"
        :class="letter.state"
      >
        {{ letter.wordLetter }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    letter: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.LetterBox {
  width: 60px;
  height: 60px;
  perspective: 1000px;

  @media #{$breakpoint-sm-max} {
    height: 45px;
    width: 45px;
  }

  @media (max-height: 800px) {
    height: 45px;
    width: 45px;
  }

  &.bigger {
    animation: bigger 0.7s ease-in-out;
  }

  &.shake {
    animation: shake 0.7s;
    // background: adjust-color($color: $color-error, $lightness: -20%);
  }

  .box {
    height: 100%;
    width: 100%;
    position: relative;
    transition: transform 0.4s;
    transform-style: preserve-3d;

    &.flipped {
      transform: rotateY(180deg);
    }

    .box-face {
      position: absolute;
      height: 100%;
      width: 100%;
      backface-visibility: hidden;
      background-color: $color-neutral-lightest;
      // outline: 1px solid $color-neutral-mid-dark;
      box-shadow: $shadow-elevation-1;
      display: grid;
      place-items: center;
      transition: outline 0.3s;

      font-size: 2rem;
      font-family: $font-family-secondary;
      font-weight: bold;

      &.filled {
        animation: zoom 0.2s;
      }
    }

    .back {
      transform: rotateY(180deg);

      &.absent {
        $color: hsl(210, 7%, 76%);
        $color-dark: adjust-color($color: $color,
          $lightness: -8%,
        );
        background: linear-gradient(180deg, $color, $color-dark);
        color: adjust-color($color: $color, $lightness: -35%);
      }

      &.correct {
        $color: hsl(120, 71%, 60%);
        $color-dark: adjust-color($color: $color,
          $lightness: -8%,
        );
        background: linear-gradient(180deg, $color, $color-dark);
        color: adjust-color($color: $color, $lightness: 35%);
      }

      &.present {
        $color: hsl(52, 82%, 60%);
        $color-dark: adjust-color($color: $color,
          $lightness: -8%,
        );
        background: linear-gradient(180deg, $color, $color-dark);
        color: adjust-color($color: $color, $lightness: -35%);
      }
    }
  }
}

@keyframes zoom {
  0% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}
</style>

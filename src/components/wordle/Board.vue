<template>
  <div class="Board">
    <Timer
      v-if="time"
      :time="time"
      :state="timer.state"
      :bonus="timer.bonus"
      @current-time="saveTime"
      @timer-controller="timerController"
    />

    <div class="main-container max-content-width">
      <transition>
        <div
          v-if="message"
          class="message"
        >
          {{ message }}
        </div>
      </transition>

      <div class="word-container">
        <div class="title-container">
          {{ text }}
        </div>

        <div
          v-for="(row, i) in board"
          :id="`row-${i}`"
          :key="i"
          :class="['row', { current: i === currentRowIndex }]"
        >
          <div
            v-for="(letter, j) in row"
            :key="j"
            class="letters"
          >
            <LetterBox :letter="letter" />
          </div>
        </div>
      </div>
      <div class="container-keyboard">
        <Keyboard @key="onKey" />
        <BaseButton @click="checkAnswer">
          {{ $t('CONFIRM.BUTTON') }}
          <font-awesome-icon icon="fa-solid fa-check" />
        </BaseButton>
      </div>
    </div>
  </div>
</template>

<script>
import Timer from '@/components/common/Timer';
import LetterBox from '@/components/wordle/LetterBox';
import Keyboard from '@/components/wordle/Keyboard';

const LETTER_STATES = {
  ABSENT: 'absent',
  CORRECT: 'correct',
  PRESENT: 'present',
};

const STORE = 'WordleModule';
const NUMBER_OF_ROWS = 6;

export default {
  components: {
    Timer,
    LetterBox,
    Keyboard,
  },

  props: {
    question: {
      type: Object,
      required: true,
    },

    initBonus: {
      type: Number,
      default: 0,
    },

    fail: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      timer: {
        bonus: this.initBonus,
        state: 'stopped',
      },
      elapsedTime: 0,
      attempt: undefined,
      currentAnswer: undefined,
      timeUsed: Math.abs(this.initBonus),

      board: undefined,
      currentRowIndex: 0,
      allowInput: true,
      message: undefined,
    };
  },

  computed: {
    text() {
      return this.question?.question;
    },

    time() {
      return this.question?.time ?? 0;
    },

    wordNumberOfLetters() {
      return this.question?.letters ?? 0;
    },

    currentRow() {
      return this.board[this.currentRowIndex];
    },
  },

  watch: {
    elapsedTime(time) {
      if (time) {
        this.currentAnswer = {
          word: this.attempt,
          time,
        };
        this.saveAnswer();
      }
    },
  },

  mounted() {
    this.timer.state = 'discounting';
  },

  async created() {
    this.initBoard();

    window.addEventListener('keyup', () => this.onKey);
  },

  methods: {
    initBoard() {
      const numberOfRows = 6;
      const numberOfColumns = this.wordNumberOfLetters;
      const boardCell = {
        wordLetter: '',
        state: undefined,
        isFlipped: false,
        isFilled: false,
      };
      const arrayGenetarator = (size) => [...Array(size).keys()];
      this.board = arrayGenetarator(numberOfRows).map(() => {
        const boardRow = arrayGenetarator(numberOfColumns).map(() => ({ ...boardCell }));
        return [...boardRow];
      });
    },

    onKey(key) {
      if (!key || !this.allowInput) return;

      if (key === 'Backspace') {
        this.clearTile();
      } else if (key === 'Enter') {
        this.checkAnswer();
      } else {
        this.fillTile(key.toUpperCase());
      }
    },

    fillTile(letter) {
      const blackLetter = this.currentRow.find(({ wordLetter }) => !wordLetter);
      if (!blackLetter) return;

      blackLetter.wordLetter = letter;
      blackLetter.isFilled = true;
    },

    clearTile() {
      const reverseArray = [...this.currentRow].reverse();
      const filledLetter = reverseArray.find(({ wordLetter }) => !!wordLetter);
      if (!filledLetter) return;

      filledLetter.wordLetter = '';
      filledLetter.isFilled = false;
    },

    timerController(data) {
      if (data.state) {
        this.timer.state = data.state;
        if (this.timer.state === 'stopped') {
          this.unanswered();
        }
      }

      if (data.bonus !== undefined) {
        this.timer.bonus = data.bonus;
      }
    },

    saveTime(currentTime) {
      this.elapsedTime = this.time - this.timeUsed - currentTime;
    },

    checkAnswer() {
      const word = this.currentRow.reduce((acc, { wordLetter }) => `${acc}${wordLetter}`, '');
      if (word.length !== this.wordNumberOfLetters) return;

      this.attempt = word.toLowerCase();
      this.timer.state = 'paused';
    },

    async unanswered() {
      this.currentAnswer = {
        word: undefined,
        time: this.time - this.timeUsed,
      };
      this.saveAnswer();
    },

    async saveAnswer() {
      const answer = {
        ...this.currentAnswer,
        questionId: this.question?.id,
      };
      const lettersStates = await this.$store.dispatch(`${STORE}/checkAnswer`, answer);
      await this.addLettersAnimation(lettersStates);

      const isCorrect = lettersStates.every(({ correct }) => correct);
      const isLastRow = this.currentRowIndex === this.board.length - 1;

      if (isCorrect || !this.currentAnswer?.word || isLastRow) {
        await this.addCorrectWordAnimation(isCorrect);
        this.$emit('next-question');
      } else {
        if (this.currentRowIndex < NUMBER_OF_ROWS) {
          this.currentRowIndex += 1;
        }
        this.timer.bonus = -5;
        this.timer.state = 'discounting';
      }
    },

    async addLettersAnimation(lettersStates) {
      this.allowInput = false;

      lettersStates.forEach(({ correct, present }, i) => {
        const currentLetter = this.currentRow[i];

        if (correct) {
          currentLetter.state = LETTER_STATES.CORRECT;
        } else if (present) {
          currentLetter.state = LETTER_STATES.PRESENT;
        } else {
          currentLetter.state = LETTER_STATES.ABSENT;
        }
      });
      await this.flipLetter(0);

      this.allowInput = true;
    },

    async flipLetter(letterIndex) {
      return new Promise((resolve) => {
        this.currentRow[letterIndex].isFlipped = true;

        setTimeout(async () => {
          const newIndex = letterIndex + 1;
          if (newIndex < this.currentRow.length) {
            await this.flipLetter(newIndex);
          }
          resolve();
        }, 400);
      });
    },

    addCorrectWordAnimation(isCorrect) {
      const query = `#row-${this.currentRowIndex}.row .LetterBox`;
      const letters = this.$el.querySelectorAll(query);
      const className = isCorrect ? 'bigger' : 'shake';
      const animationDuration = 0.7;
      let delay = 0;

      const timeOut = () => (delay + animationDuration) * 1000;

      for (let i = 0; i < letters.length; i += 1) {
        const letter = Array.from(letters)[i];
        letter.classList.add(className);
        letter.style['animation-delay'] = `${delay}s`;
        delay += 0.1;

        setTimeout(() => {
          letter.classList.remove(className);
        }, timeOut());
      }

      return new Promise((resolve) => {
        setTimeout(() => {
          resolve();
        }, timeOut());
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.Board {
  line-height: 1;

  .message {
    position: absolute;
    left: 50%;
    top: 40%;
    outline: 2px solid $color-primary;
    color: $color-primary-dark;
    background-color: $color-neutral-lightest;
    font-family: $font-family-primary;
    font-size: $font-size-xl;
    padding: 16px 20px;
    z-index: 2;
    border-radius: 5px;
    transform: translateX(-50%);
    transition: opacity 0.3s ease-out;
    font-weight: 600;

    &.v-leave-to {
      opacity: 0;
    }
  }

  .main-container {
    display: flex;
    align-items: center;
    gap: $spacing-xl;
    width: 100%;
    max-width: 950px;
    margin: auto;
    padding: $spacing-xl 0;
    height: calc(100% - 10px);

    @media #{$breakpoint-lg-max} {
      flex-direction: column;
      gap: $spacing-l;
      padding: $spacing-l 0;
    }

    .title-container {
      text-align: center;
      font-family: $font-family-primary;
      font-weight: bold;
      font-size: clamp($font-size-l , 4vw, $font-size-3xl);
      color: $color-primary-dark;
      margin-bottom: 2rem;
      @media #{$breakpoint-sm-max} {
        margin-bottom: 1rem;
      }
    }

    .container-keyboard {
      flex-grow: 1;
      .BaseButton {
        margin: 0 auto;
        margin-top: 2rem;
      }
    }

    .word-container {
      justify-self: center;
      display: grid;
      gap: $spacing-xs;
      flex-grow: 1;
      justify-items: center;
      align-items: center;

      .row {
        display: flex;
        justify-content: center;
        gap: $spacing-xs;
        transition: all 0.3s ease;

        &.current {
          background-color: $color-primary-lightest;
          border-radius: 5px;
          padding: $spacing-xs;
        }

        &:not(.current) .LetterBox {
          height: 40px;
          width: 40px;
        }
      }
    }
  }
}

.shake {
  animation: shake 0.5s;
}

.bigger {
  transition: all 0.5s ease-in-out;
  animation-name: bigger;
  animation-duration: 0.5s;
}
</style>

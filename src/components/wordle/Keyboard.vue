<template>
  <div class="Keyboard">
    <div
      v-for="(row, i) in rows"
      :key="i"
      class="row"
    >
      <div
        v-if="i === 1"
        class="spacer"
      />

      <button
        v-for="(key, j) in row"
        :key="j"
        :class="[key.length > 1 && 'big']"
        @click="$emit('key', key)"
      >
        <span
          v-if="key !== 'Backspace'"
          class="letter"
        >
          {{ key }}
        </span>

        <svg
          v-else
          xmlns="http://www.w3.org/2000/svg"
          height="24"
          viewBox="0 0 24 24"
          width="24"
        >
          <path
            fill="currentColor"
            d="M22 3H7c-.69 0-1.23.35-1.59.88L0 12l5.41 8.11c.********** 1.59.89h15c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H7.07L2.4 12l4.66-7H22v14zm-11.59-2L14 13.41 17.59 17 19 15.59 15.41 12 19 8.41 17.59 7 14 10.59 10.41 7 9 8.41 12.59 12 9 15.59z"
          />
        </svg>
      </button>

      <div
        v-if="i === 1"
        class="spacer"
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      rows: ['qwertyuiop'.split(''), 'asdfghjklñ'.split(''), [...'zxcvbnm'.split(''), 'Backspace']],
    };
  },
};
</script>

<style lang="scss" scoped>
.Keyboard {
  user-select: none;
}

.row {
  display: flex;
  gap: 6px;
  width: 100%;
  margin: 0 auto 8px;
  touch-action: manipulation;
}

.spacer {
  flex: 0.5;
}

button {
  font-family: $font-family-secondary;
  font-weight: bold;
  font-size: $font-size-m;
  border: 0;
  padding: 0;
  height: 45px;
  min-width: 30px;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  // background-color: #d3d6da;
  color: $color-neutral-dark;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  text-transform: uppercase;
  -webkit-tap-highlight-color: $color-neutral-dark;
  // transition: all 0.2s 1.5s;
  box-shadow: $shadow-elevation-1;
}

.letter {
  color: $color-neutral-dark;
}

button:last-of-type {
  margin: 0;
}

button.big {
  flex: 1.5;
}

@media #{$breakpoint-sm-max} {
  .row {
    width: 98%;
  }
  button {
    height: 30px;
    min-width: 24px;
  }
}
</style>

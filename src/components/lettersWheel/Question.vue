<template>
  <div
    class="Question"
    :class="[themeClass]"
  >
    <h1 class="title">
      {{ !currentTip ? $t('BEGIN.LETTER') : $t('CONTAIN.LETTER') }}

      <b class="letter">
        {{ currentLetter }}
      </b>
    </h1>

    <p class="description">
      {{ statement }}
    </p>

    <div class="actions">
      <div class="answer">
        <BaseInput
          v-model="answerInput"
          type="text"
          v-bind="{ placeholder: $t('ANSWER.PLACEHOLDER') }"
        />

        <BaseButton
          class="send"
          size="s"
          @click="$emit('save-answer', answerInput)"
        >
          <font-awesome-icon icon="fa-solid fa-angle-right" />
        </BaseButton>
      </div>

      <BaseButton
        class="skip"
        variation="error"
        @click="$emit('next-question')"
      >
        {{ $t('SKIP.BUTTON') }}
        <font-awesome-icon icon="fa-solid fa-angles-right" />
      </BaseButton>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    question: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      answerInput: undefined,
    };
  },

  computed: {
    statement() {
      return this.question?.question;
    },

    currentLetter() {
      return this.question?.letter;
    },

    currentTip() {
      return this.question?.type;
    },
  },
};
</script>

<style lang="scss" scoped>
.Question {
  display: grid;
  min-height: 300px;
  grid-template-rows: auto 1fr auto;
  gap: $spacing-l;

  .title {
    align-self: center;
    color: $color-neutral-dark;
    font-size: $font-size-4xl;
    .letter {
      color: $color-primary;
      font-weight: bold;
    }
  }

  .description {
    color: $color-neutral-dark;
    font-size: $font-size-l;
    overflow-wrap: anywhere;
  }

  .actions {
    display: grid;

    .answer {
      display: flex;
      border-radius: $border-radius-xl;
      background-color: $color-neutral-lighter;
      margin-bottom: $spacing-s;

      .BaseInput {
        flex: 1;
      }

      .send {
        font-family: $font-family-secondary;
        font-weight: 600;
        font-size: $font-size-xl;
      }
    }

    .skip {
      justify-self: center;
    }
  }

  @media #{$breakpoint-lg-max} {
    min-height: auto;

    .title {
      font-size: $font-size-3xl;
    }
    .description {
      margin-bottom: $spacing-xs;
    }
  }

  @media #{$breakpoint-md-max} {
    gap: $spacing-m;
    .title {
      font-size: $font-size-xl;
    }

    .description {
      font-size: $font-size-m;
    }
  }
}
</style>

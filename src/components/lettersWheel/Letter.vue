<template>
  <div
    class="Letter"
    :class="[`state--${state}`]"
  >
    {{ letter }}
  </div>
</template>

<script>
const states = ['inactive', 'default', 'correct', 'incorrect'];
export default {
  props: {
    letter: {
      type: String,
      default: '',
    },

    state: {
      type: String,
      default: 'default',
      validator: (state) => states.includes(state),
    },
  },
};
</script>

<style lang="scss" scoped>
.Letter{
  $size: 40px;
  width: $size;
  height: $size;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 600;

  text-transform: uppercase;
  font-family: $font-family-secondary;
  border-radius: $border-radius-xl;
  background: var(--local-background);
  color: var(--local-color);
  box-shadow: var(--local-shadow);
  transition: all .3s ease-in-out;

  &.state{
    &--inactive{
      --local-background: #{$color-neutral-mid};
      --local-color: #{$color-neutral-mid-darker};
      --local-shadow: none;
    }

    &--default{
      $color: adjust-color( $color-primary, $saturation: -15%, $lightness: 10%);
      --local-background: #{$color-neutral-mid-darker};
      --local-color: #fff;
      --local-shadow: #{$shadow-elevation-1};
    }

    &--correct{
      --local-background: #{$color-success};
      --local-color: #fff;
      --local-shadow: #{$shadow-elevation-1};
    }

    &--incorrect{
      --local-background: #{$color-error};
      --local-color: #fff;
      --local-shadow: #{$shadow-elevation-1};
    }

    @keyframes bounce {
      0% {
        transform: translateX(0px);
        timing-function: ease-in;
      }
      37% {
        transform: translateX(5px);
        timing-function: ease-out;
      }
      55% {
        transform: translateX(-5px);
        timing-function: ease-in;
      }
      73% {
        transform: translateX(4px);
        timing-function: ease-out;
      }
      82% {
        transform: translateX(-4px);
        timing-function: ease-in;
      }
      91% {
        transform: translateX(2px);
        timing-function: ease-out;
      }
      96% {
        transform: translateX(-2px);
        timing-function: ease-in;
      }
      100% {
        transform: translateX(0px);
        timing-function: ease-in;
      }
    }

    @keyframes correct {
      0% { transform: translateY(0px); background-color:#00D700; }
      50% { transform: translateY(-5px); }
      75% { transform: translateY(2px); }
      100% { transform: translateY(0px); }
    }
  }

  &.active{
    --local-background: #{$color-primary};
    --local-color: #fff;
    $shadow-colored: 0 0 2px 2px adjust-color($color-primary, $alpha: -0.5);
    --local-shadow: inset 0 0 0 2px #fff, #{$shadow-colored};
  }

}
</style>

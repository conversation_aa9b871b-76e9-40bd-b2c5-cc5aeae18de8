<template>
  <div class="Wheel">
    <div class="container">
      <div class="timerCircle">
        <InnerCircle
          :global-time="globalTime"
          :finish="finish"
          :used-time="usedTime"
          :timer-stopped="timerStopped"
          @current-time="currentTime"
          @finish="$emit('finish')"
        />
      </div>

      <div class="circleContainer">
        <div class="circle">
          <Letter
            v-for="(letter, i) in keyboard"
            :key="i"
            :class="[{ active: isActive(letter.letter) }]"
            :letter="letter.letter"
            :state="letter.state"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Letter from '@/components/lettersWheel/Letter';
import InnerCircle from '@/components/lettersWheel/InnerCircle';

const alphabet = 'abcdefghijklmnopqrstuvwxyz';

export default {
  components: {
    Letter,
    InnerCircle,
  },

  props: {
    questions: {
      type: Array,
      default: undefined,
    },

    currentQuestion: {
      type: Object,
      default: undefined,
    },

    globalTime: {
      type: Number,
      required: true,
    },

    usedTime: {
      type: Number,
      default: 0,
    },

    finish: {
      type: Boolean,
      default: false,
    },

    timerStopped: {
      type: <PERSON>olean,
      default: false,
    },
  },

  data() {
    return {
      alphabet: alphabet.toUpperCase().split(''),
      attempts: [],
    };
  },

  computed: {
    keyboard() {
      return this.alphabet.map((letter) => {
        const currentQuestion = this.questions.find((question) => question.letter.toUpperCase() === letter);
        // this.fadeLetter(letter);
        if (!currentQuestion) return { letter, state: 'inactive' };
        if (currentQuestion.correct === undefined) return { letter, state: 'default' };

        return { letter, state: currentQuestion.correct ? 'correct' : 'incorrect' };
      });
    },
  },

  watch: {
    question(newValue) {
      if (newValue) {
        this.attempts = [];
      }
    },
  },

  methods: {
    isActive(letter) {
      return this.currentQuestion?.letter.toUpperCase() === letter.toUpperCase();
    },

    currentTime(newValue) {
      this.$emit('current-time', newValue);
    },
  },
};
</script>

<style lang="scss" scoped>
.Wheel {
  font-family: $font-family-secondary;

  .container {
    //height: 450px;
    //width: 450px;
    position: relative;
    display: grid;
    place-content: center;

    .circleContainer .circle {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;

      .Letter {
        $s-item: 2.75em;
        $s-circle: $s-item * 11;
        $s-item-mobile: calc($s-item / 2);
        $s-circle-mobile: $s-item * 9.5;
        $nb-items: 26;

        position: absolute;
        top: calc(50% - #{$s-item-mobile});
        left: calc(50% - #{$s-item-mobile});

        cursor: default;
        display: block;
        width: $s-item;
        height: $s-item;
        transition: all 0.3s ease-in-out;
        border-radius: $border-radius-xl;
        color: white;
        font-weight: 600;
        line-height: $s-item;
        text-align: center;

        $rot: 270;
        $angle: calc(360 / $nb-items);
        @for $i from 1 through $nb-items {
          &:nth-of-type(#{$i}) {
            transform: rotate($rot * 1deg) translate(calc($s-circle-mobile / 2)) rotate($rot * -1deg);
          }

          $rot: $rot + $angle;
        }
      }
    }
  }
}
</style>

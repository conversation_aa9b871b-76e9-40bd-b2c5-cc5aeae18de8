<template>
  <div
    class="Inner"
    :class="[themeClass]"
  >
    <div class="timer">
      <div
        v-if="currentTime > 0"
        class="time"
      >
        <span class="minute">
          {{ minutes | formatTime }}
        </span>
        :
        <span class="second">
          {{ seconds | formatTime }}
        </span>
      </div>

      <div
        v-if="currentTime <= 0"
        class="time"
      >
        <span class="minute">00</span>:<span class="second">00</span>
      </div>

      <svg
        width="390"
        height="390"
      >
        <circle
          id="circle1"
          cx="195"
          cy="195"
          r="190"
        />

        <circle
          id="circle2"
          cx="195"
          cy="195"
          r="190"
          :stroke-dasharray="' ' + circumference"
          :style="{ strokeDashoffset: strokeDashoffset }"
        />
      </svg>
    </div>
  </div>
</template>

<script>
export default {
  filters: {
    formatTime(value) {
      return value < 10 ? `0${value}` : value;
    },
  },

  props: {
    globalTime: {
      type: Number,
      required: true,
    },

    usedTime: {
      type: Number,
      default: 0,
    },

    finish: {
      type: Boolean,
      default: false,
    },

    timerStopped: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    const circumference = 190 * 2 * Math.PI;

    return {
      circumference,
      progress: 0,
      timer: undefined,
      currentTime: undefined,
      time: undefined,
    };
  },

  computed: {
    strokeDashoffset() {
      return Math.max(0, this.circumference - (this.progress / this.time) * this.circumference);
    },

    seconds() {
      return Math.floor(this.currentTime % 60);
    },

    minutes() {
      return Math.floor(this.currentTime / 60);
    },
  },

  watch: {
    finish(newValue) {
      if (newValue) {
        this.finishEvent();
      }
    },

    currentTime(newValue) {
      if (newValue) {
        this.$emit('current-time', this.currentTime);
      }
    },
  },

  async created() {
    this.time = this.globalTime;
    this.currentTime = this.globalTime;

    this.currentTime -= this.usedTime;
    this.progress += this.usedTime;

    this.startTimer();
  },

  methods: {
    startTimer() {
      this.timer = setInterval(() => {
        if (this.currentTime > 0) {
          if (!this.timerStopped) {
            this.progress += 1;
            this.currentTime -= 1;
          }
        } else {
          this.finnishWheel();
        }
      }, 1000);
    },

    finishEvent() {
      this.$emit('current-time', this.currentTime);
    },

    finnishWheel() {
      clearInterval(this.timer);
      this.currentTime = null;

      this.$emit('finish');
    },
  },
};
</script>

<style lang="scss" scoped>
@import url('https://fonts.googleapis.com/css2?family=Space+Mono:wght@400;700&display=swap'); //? fonts

.Inner {
  &.theme {
    &--light {
      --local-background-color: #{$color-neutral-lighter};
    }

    &--dark {
      --local-background-color: #{$color-neutral-darker};
    }
  }

  .timer {
    display: grid;
    place-content: center;

    .time {
      position: absolute;
      z-index: 10;
      font-size: 70px;
      font-weight: 500;
      color: $color-neutral-dark;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      display: grid;
      place-content: center;
      grid-template-columns: repeat(3, auto);
    }

    svg {
      //position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      margin: auto;
      transform: scale(0.9);
    }

    circle {
      fill: transparent;
    }
  }

  #circle1 {
    stroke-width: 10px;
    stroke: $color-primary;
  }

  #circle2 {
    stroke-width: 11.5px;
    stroke: white;
    transition: 1s linear;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
  }
}
</style>

<template>
  <div
    v-if="questions"
    class="Panel"
  >
    <div class="letters">
      <div
        v-for="(letter, i) in headerLetters"
        :key="i"
        class="letter-content"
      >
        <Letter
          :state="letter.state"
          :letter="letter.value"
        />

        <span
          class="text"
          :class="[letter.state]"
        >
          {{ letter.text }}
        </span>
      </div>
    </div>

    <Question
      v-if="currentQuestion"
      :key="currentQuestion.id"
      :question="currentQuestion"
      @next-question="$emit('next-question')"
      @save-answer="$emit('save-answer', $event)"
    />
  </div>
</template>

<script>
import Question from '@/components/lettersWheel/Question';
import Letter from '@/components/lettersWheel/Letter';

export default {
  components: {
    Question,
    Letter,
  },

  props: {
    currentQuestion: {
      type: Object,
      default: undefined,
    },

    questions: {
      type: Array,
      default: undefined,
    },
  },

  computed: {
    defaultNumber() {
      const num = this.questions.filter((letter) => letter.correct === undefined);
      const n = num.length.toString();

      return n;
    },

    correctNumber() {
      const num = this.questions.filter((letter) => letter.correct === true);
      const n = num.length.toString();

      return n;
    },

    incorrectNumber() {
      const num = this.questions.filter((letter) => letter.correct === false);
      const n = num.length.toString();

      return n;
    },

    headerLetters() {
      const states = ['correct', 'incorrect', 'default'];

      return states.map((state) => {
        const valueFunction = `${state}Number`;
        const text = this.$t(`LETTER.${state.toUpperCase()}`);
        const value = this?.[valueFunction] ?? 0;

        return { state, value, text };
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.Panel {
  background-color: #fff;
  max-width: 500px;
  width: 100%;
  border-radius: $border-radius-l;
  box-shadow: $shadow-elevation-2;
  padding: $spacing-2xl $spacing-4xl;
  position: relative;

  .letters {
    margin-top: -5.5rem;
    margin-bottom: $spacing-2xl;

    display: flex;
    justify-content: space-around;
    gap: 1.5rem;

    .letter-content {
      display: grid;
      justify-items: center;
      gap: $spacing-xs;

      .Letter {
        $size: 70px;
        height: $size;
        width: $size;
        color: #fff;
        font-size: $font-size-3xl;
      }

      .text {
        text-transform: uppercase;
        font-weight: 600;
        font-size: $font-size-s;

        &.correct {
          color: $color-success-dark;
        }
        &.incorrect {
          color: $color-error-dark;
        }
        &.default {
          color: $color-neutral-dark;
        }
      }
    }
  }

  @media #{$breakpoint-lg-max} {
    padding: $spacing-l $spacing-2xl;
    transform: translate(-$spacing-l, 0);

    .letters {
      margin-top: -3.5rem;
      margin-bottom: $spacing-l;
      .letter-content {
        .Letter {
          $size: 50px;
          height: $size;
          width: $size;
          font-size: $font-size-xl;
        }
      }
    }
  }

  @media #{$breakpoint-md-max} {
    transform: translate(0, 0);
  }

  @media #{$breakpoint-sm-max} {
    padding: $spacing-m $spacing-l;
    .letters {
      margin-top: -2.5rem;
    }
  }
}
</style>

<template>
  <div
    class="Letter"
    :class="[`state--${state}`]"
  >
    {{ letter }}
  </div>
</template>

<script>
const states = ['default', 'correct', 'incorrect'];
export default {
  props: {
    letter: {
      type: String,
      default: undefined,
    },

    state: {
      type: String,
      default: 'default',
      validator: (state) => states.includes(state),
    },
  },
};
</script>

<style lang="scss" scoped>
.Letter {
  $size: 40px;
  width: $size;
  height: $size;
  display: flex;
  justify-content: center;
  align-items: center;

  text-transform: uppercase;
  font-weight: 600;
  color: $color-neutral-darkest;
  background-color: var(--local-background-color);

  margin: $spacing-3xs;
  // border: 1px solid;
  box-shadow: $shadow-elevation-1;
  border-radius: $border-radius-s;

  position: relative;

  &::before {
    transform: rotateZ(-45deg);
  }

  &::after {
    transform: rotateZ(45deg);
  }

  &::before,
  &::after {
    content: '';
    left: 0;
    top: 50%;
    position: absolute;
    height: 3px;
    background: transparent;
    transition: all 0.3s ease;
  }

  &.state {
    &--default {
      --local-background-color: #fff;
      transition: all 0.3s ease;

      &:hover {
        --local-background-color: #{$color-neutral-light};
        cursor: pointer;
      }
    }

    &--correct {
      --local-background-color: #{$color-success};

      animation: correct 0.5s;
    }

    &--incorrect {
      --local-background-color: #{$color-error};

      animation: bounce 0.5s;

      &::before,
      &::after {
        background: adjust-color($color-error, $alpha: -0.2, $lightness: -30%);
        right: 0;
      }
    }
  }
}
</style>

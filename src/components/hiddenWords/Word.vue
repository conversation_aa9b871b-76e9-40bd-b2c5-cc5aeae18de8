<template>
  <div class="Word">
    <Timer
      v-if="time"
      :time="time"
      :state="timer.state"
      :bonus="timer.bonus"
      @current-time="saveTime"
      @timer-controller="timerController"
    />

    <div class="content">
      <h1
        v-if="title"
        class="title"
      >
        {{ title }}
      </h1>

      <TransitionFade>
        <img
          class="image"
          :src="image"
        />
      </TransitionFade>

      <p class="statement">
        <span
          v-for="( statementWord, i ) in statement "
          :key="i"
          class="word"
        >
          <span
            v-for="( letter, j ) in statementWord "
            :key="j"
            :class="[letter.type]"
          >
            {{ letter.character }}
          </span>
        </span>
      </p>

      <div class="letters">
        <Letter
          v-for="( letter, i ) in keyboard "
          :key="i"
          :letter="letter.letter"
          :state="letter.state"
          @click.native="checkLetter(letter.letter)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Timer from '@/components/common/Timer';
import Letter from '@/components/hiddenWords/Letter';

import TransitionFade from '@/transitions/TransitionFade';

const alphabet = 'abcdefghijklmnñopqrstuvwxyz';

export default {
  components: {
    Timer,
    Letter,
    TransitionFade,
  },

  props: {
    question: {
      type: Object,
      default: undefined,
    },
  },

  data() {
    return {
      letters: alphabet.split(''),
      timer: {
        bonus: 0,
        state: 'stopped',
      },
      elapsedTime: 0,
      penalty: -5,
      attempts: [],
    };
  },

  computed: {
    title() {
      return this.question?.question;
    },

    image() {
      const image = this.question?.imageUrl;
      if (!image) return undefined;

      return process.env.NODE_ENV === 'offline' ? image : `/${image}`;
    },
    time() {
      return this.question?.time;
    },

    keyboard() {
      return this.letters.map((letter) => {
        const answeredLetter = this.attempts.find((answerLetter) => answerLetter.letter === letter);

        return { letter, state: this.getLetterStateInKeyboard(answeredLetter?.correct) };
      });
    },

    word() {
      // TODO: Habria que cambiar {'answers': []} por => {'word': "hoja"}
      return this.question?.answers[0]?.answer?.toLowerCase() ?? '';
    },

    splitWord() {
      return this.word.split('');
    },

    clues() {
      return this.question?.clues ?? [];
    },

    statement() {
      const sliptWords = this.word.split(' ');

      let i = 0;
      return sliptWords.map((word) => {
        const characters = word.split('').map((letter) => {
          const isVisible = this.clues.includes(i);
          const isSpace = letter === ' ';
          const isRight = this.isAlreadyClicked(letter);
          i += 1;

          const character = isVisible || isSpace || isRight ? letter : '';

          return { character, type: this.getLetterTypeInStatement(isVisible, isRight, isSpace) };
        });

        return characters;
      });
    },
  },

  watch: {
    question(newValue) {
      if (newValue) {
        this.timer.state = 'discounting';
        this.attempts = [];
      }
    },

    elapsedTime(newTime) {
      if (newTime) {
        this.timer.state = 'paused';
        this.finishQuestion();
      }
    },
  },

  mounted() {
    this.timer.state = 'discounting';
  },

  methods: {
    timerController(data) {
      if (data.state) {
        this.timer.state = data.state;

        if (this.timer.state === 'stopped') {
          this.finishQuestion();
        }
      }

      if (data.bonus !== undefined) {
        this.timer.bonus = data.bonus;
      }
    },

    saveTime(currentTime) {
      this.elapsedTime = this.time - currentTime;
    },

    isAlreadyClicked(letter) {
      return !!this.attempts.find((answerLetter) => answerLetter.letter === letter);
    },

    isCorrect(letter) {
      const possibleLetters = this.splitWord.filter((possibleLetter, i) => !this.clues.includes(i));
      return possibleLetters.includes(letter);
    },

    getLetterStateInKeyboard(isCorrect) {
      if (isCorrect === undefined) return isCorrect;

      return isCorrect ? 'correct' : 'incorrect';
    },

    getLetterTypeInStatement(isVisible, isRight, isSpace) {
      if (!isVisible && isRight) {
        return 'answer';
      }
      if (isSpace) {
        return 'blank';
      }
      return undefined;
    },

    checkLetter(letter) {
      if (!this.isAlreadyClicked(letter)) {
        const isCorrect = this.isCorrect(letter);

        if (!isCorrect) {
          this.applyPenalty();
        }

        this.attempts.push({ letter, correct: isCorrect });

        if (this.isWordCompleted()) {
          this.timerController({ state: 'paused' });
        }
      }
    },

    applyPenalty() {
      this.timer.bonus = this.penalty;
    },

    isWordCompleted() {
      const lettersToComplete = this.splitWord.filter((letter, i) => !this.clues.includes(i) && letter !== ' ');
      return lettersToComplete.every((letterToComplete) => this.isAlreadyClicked(letterToComplete));
    },

    finishQuestion() {
      const currentAnswer = {
        questionId: this.question.id,
        attempts: this.attempts,
        time: this.elapsedTime,
      };

      this.$emit('finish-question', currentAnswer);
    },
  },
};
</script>

<style lang="scss" scoped>
.Word {

  .content {
    max-width: 800px;
    width: 100%;
    max-height: calc(100vh - 120px);
    margin: 0 auto;
    display: grid;
    grid-template-rows: auto 1fr auto auto;
    gap: 1rem;
    justify-items: center;

    .title {
      color: $color-neutral-darkest;
      padding: clamp($spacing-m, 2vw, $spacing-xl);
      text-align: center;
      font-size: clamp($font-size-l, 2vw, $font-size-2xl);
      font-weight: 500;
    }

    .image {
      border-radius: $border-radius-s;
      background-color: $color-neutral-mid-light;
      box-shadow: $shadow-elevation-1;
      height: 100%;
      min-height: 200px;
      max-width: 90%;
      object-fit: cover;
    }

    .statement {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-m;
      justify-content: center;
      margin: auto;
      width: 100%;

      .word {
        display: flex;
        flex-wrap: wrap;
        $fontSize: clamp($font-size-xl, 2vw, $font-size-2xl);
        font-size: $fontSize;
        font-weight: 600;
        justify-content: center;
        gap: $spacing-2xs;

        span {
          text-align: center;
          padding: $spacing-3xs;
          display: inline-block;
          border-bottom: 2px solid;
          text-transform: uppercase;
          min-height: $fontSize;
          width: 28px;

          &.answer {
            color: $color-primary;
          }

          &.blank {
            width: 15px;
            border-bottom: none;
          }
        }
      }

      &.correct .word span {
        animation: correct 0.5s;
      }

      &.incorrect .word span {
        animation: bounce 0.5s;
        background-color: $color-error-light;
      }
    }

    .letters {
      display: flex;
      flex-flow: row wrap;
      justify-content: center;
      margin-top: clamp($spacing-s, 2vw, $spacing-m);
      padding: $spacing-xs;

      &.disabled {
        pointer-events: none;
      }
    }
  }
}
</style>

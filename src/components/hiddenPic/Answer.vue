<template>
  <div class="Answer">
    <div :class="['words', { correct: isCorrect, incorrect: isCorrect === false }]">
      <div
        v-for="(word, i) in answer"
        :key="i"
        class="word"
      >
        <div
          v-for="(letter, j) in word"
          :id="letter.name"
          :key="i + j"
          class="letter"
        >
          <span v-if="!letter.isEditable">
            {{ letter.value }}
          </span>

          <input
            v-else
            v-model="letter.value"
            :name="letter.name"
            type="text"
            maxlength="1"
            autocomplete="off"
            @click="focusLetter(letter.name)"
            @input="moveFocus({ key: $event.data }, i, j)"
            @keyup="deleteLetter($event, i, j)"
          />
        </div>
      </div>
    </div>

    <BaseButton
      :class="{ disabled: !allLettersAreFilled }"
      @click="saveAnswer"
    >
      <font-awesome-icon icon="fa-solid fa-angle-right" />
    </BaseButton>
  </div>
</template>

<script>
const EMPTY_LETTER = '_';

export default {
  props: {
    answers: {
      type: String,
      required: true,
    },

    isCorrect: {
      type: Boolean,
      default: undefined,
    },
  },

  data() {
    return {
      answer: undefined,
    };
  },

  computed: {
    allLettersAreFilled() {
      const availableLetters = this.getEditableLetters();
      return availableLetters.every((letter) => letter.value);
    },
  },

  created() {
    this.mountAnswer();
  },

  mounted() {
    this.focusFirstAvailableLetter();
  },

  methods: {
    mountAnswer() {
      const wordsAnswer = this.answers.split(' ');
      this.answer = wordsAnswer.map((word, i) => {
        const letters = word.split('');

        return letters.map((letter, j) => {
          const isEditable = letter === EMPTY_LETTER;
          const value = isEditable ? undefined : letter;
          const name = `letter${i}-${j}`;

          return { isEditable, value, name };
        });
      });
    },

    focusFirstAvailableLetter() {
      this.answer.some((word) => {
        const firstLetter = word.find((letter) => letter.isEditable && !letter.value);
        if (!firstLetter) return false;

        this.focusLetter(firstLetter?.name);
        return true;
      });
    },

    splitWord(word) {
      return word?.split('');
    },

    deleteLetter({ key }, wordIndex, letterIndex) {
      // todo backspace
      if (key === 'Backspace') this.moveFocus({ key }, wordIndex, letterIndex);
    },

    moveFocus(e, wordIndex, letterIndex) {
      const { key } = e;

      const availableLetters = this.getEditableLetters();
      const currentLetterName = `letter${wordIndex}-${letterIndex}`;
      const currentLetterIndex = availableLetters.findIndex((l) => l.name === currentLetterName);

      if (!this.isAvailableKey(key)) {
        availableLetters[currentLetterIndex].value = '';
        return;
      }

      const isBackspace = key === 'Backspace';
      availableLetters[currentLetterIndex].value = !isBackspace ? key : '';
      const direction = isBackspace ? -1 : 1;
      const nextLetterIndex = currentLetterIndex + direction;

      if (nextLetterIndex in availableLetters) {
        const nextLetterName = availableLetters[nextLetterIndex].name;
        this.focusLetter(nextLetterName);
      } else {
        const currentLetter = this.$el.querySelector(`#${currentLetterName} > input`);
        currentLetter.blur();
      }
    },

    isAvailableKey(key) {
      const isLetter = /^[a-zA-ZñÑ]$/g.test(key);
      const isBackspace = key === 'Backspace';

      return isLetter || isBackspace;
    },

    getEditableLetters() {
      return this.answer.reduce((acc, word) => {
        const letters = word.filter((letter) => letter.isEditable);
        return [...acc, ...letters];
      }, []);
    },

    focusLetter(letterNameTag) {
      const currentLetter = this.$el.querySelector(`#${letterNameTag} > input`);
      if (currentLetter) {
        const elementFocus = this.$el.querySelector('.letter input.focus');
        if (elementFocus) elementFocus.classList.remove('focus');

        currentLetter.classList.add('focus');
        currentLetter.focus();
        currentLetter.select();
      }
    },

    blurLetter(letterNameTag) {
      const currentLetter = this.$el.querySelector(`#${letterNameTag} > input`);
      if (currentLetter) {
        currentLetter.blur();
      }
    },

    saveAnswer() {
      const answer = this.answer.map((word) => word.map((letter) => letter.value || '_').join('')).join(' ');
      this.$emit('save-answer', answer);
    },

    letterPressed(letter) {
      if (!this.isAvailableKey(letter)) return;
      const elementFocus = this.$el.querySelector('.letter input.focus');
      const { name } = elementFocus;
      const [wordIndex, letterIndex] = name.match(/\d+/g);
      this.moveFocus({ key: letter }, wordIndex, letterIndex);
    },
  },
};
</script>

<style lang="scss" scoped>
.Answer {
  display: flex;
  gap: $spacing-xl $spacing-m;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
  padding-bottom: 5px;

  .words {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    padding: $spacing-xs;
    border-radius: $border-radius-l;
    background-color: #fff;
    gap: $spacing-l;

    &.incorrect .letter input {
      animation: bounce 0.5s;
      transition: all 0.3s ease;
      background: adjust-color($color: $color-error, $lightness: 35%) !important;
      color: adjust-color($color: $color-error, $lightness: -30%) !important;
    }

    &.correct .letter input {
      animation: correct 0.5s;
      transition: all 0.3s ease;
      background: adjust-color($color: $color-success, $lightness: 30%) !important;
      color: adjust-color($color: $color-success, $lightness: -30%) !important;
    }

    .word {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: $spacing-xs;

      .letter input {
        width: 35px;
        height: 35px;
        display: grid;
        place-content: center;
        padding: $spacing-2xs;
        border: none;
        font-size: $font-size-2xl;
        text-align: center;
        text-transform: uppercase;
        border-radius: $border-radius-s;
        font-family: $font-family-primary;
        font-weight: 500;
        color: $color-primary;
        padding: $spacing-xs;
        background-color: $color-neutral-lightest;

        &:focus,
        &:focus-visible {
          border: none;
          outline: none;
          box-shadow: 0 0 3px 2px $color-primary-light;
          background: adjust-color($color: $color-primary, $lightness: 35%);
        }
      }
    }
  }

  .BaseButton {
    width: $spacing-m;
    border-radius: 35px;
    aspect-ratio: 1/1;
    font-size: $font-size-3xl;
  }

  .BaseButton:active {
    transform: translateY(2px);
  }
}
</style>

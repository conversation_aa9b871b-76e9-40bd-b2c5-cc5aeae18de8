<template>
  <div
    v-if="image"
    class="Question"
  >
    <Timer
      v-if="time"
      :time="time"
      :state="timer.state"
      :bonus="timer.bonus"
      @current-time="saveTime"
      @timer-controller="timerController"
    />

    <div class="content">
      <h1
        v-if="title"
        class="title"
      >
        {{ title }}
      </h1>

      <canvas id="canvas" />

      <div
        v-if="clue"
        class="clue-container"
      >
        <img
          :class="['bulb', { disabled: visible }]"
          :src="require('@/assets/images/hiddenPic/lightBulb.svg')"
          alt="img"
          @click="visibleClue"
        />

        <TransitionSlide side="left">
          <p
            v-if="visible"
            class="clue"
          >
            {{ clue }}
          </p>
        </TransitionSlide>
      </div>

      <Answer
        v-if="answerWord"
        :key="question.id"
        :answers="answerWord"
        :is-correct="isCorrect"
        @save-answer="selectAnswer"
      />
    </div>
  </div>
</template>

<script>
import Timer from '@/components/common/Timer';
import Answer from '@/components/hiddenPic/Answer';
import TransitionSlide from '@/transitions/TransitionSlide';

export default {
  components: {
    Timer,
    Answer,
    TransitionSlide,
  },

  props: {
    question: {
      type: Object,
      default: undefined,
    },

    isCorrect: {
      type: Boolean,
      default: undefined,
    },

    disabled: {
      type: Boolean,
      default: false,
    }
  },

  data() {
    return {
      timer: {
        bonus: 0,
        state: 'stopped',
      },
      elapsedTime: 0,
      answer: undefined,
      visible: false,
      canvas: undefined,
      ctx: undefined,
      imageCanvas: undefined,
      imageResizedWidth: undefined,
      imageResizedHeight: undefined,
      originalChildSize: undefined,
      originalParentSize: undefined,
      blurTime: 0,
      penalty: 0,
      blurEffectPaused: false,
    };
  },

  computed: {
    id() {
      return this.question?.id;
    },

    title() {
      return this.question?.title;
    },

    image() {
      return this.question?.image;
    },

    imagePath() {
      return process.env.NODE_ENV === 'offline' ? this.image : `/${this.image}`;
    },

    words() {
      return this.question?.words;
    },

    clue() {
      return this.question?.clue;
    },

    answerWord() {
      const splitedWords = this.words?.split('');
      const letters = splitedWords?.map((letter) => {
        if (letter === ' ') return letter;
        // eslint-disable-next-line no-param-reassign
        letter = '_';
        return letter;
      });

      return letters.join('');
    },

    time() {
      return this.question?.time;
    },
  },

  watch: {
    question(newValue) {
      if (newValue) {
        this.timer.state = 'discounting';
      }
    },

    isCorrect(newValue) {
      const timeHasFinished = this.time <= this.elapsedTime;
      const hasFailed = newValue === false;
      this.timer.bonus = 0;
      if (hasFailed && !timeHasFinished) {
        this.timer.state = 'discounting';
        this.timer.bonus = -5;
        this.blurEffectPaused = false;
      }
    },
  },

  mounted() {
    this.timer.state = 'discounting';
    this.blurEffectPaused = false;
    this.loadImage();
  },

  methods: {
    loadImage() {
      this.blurTime = ((this.time * 80) / 100 / 20) * 1000;
      this.canvas = document.getElementById('canvas');
      this.ctx = this.canvas.getContext('2d');
      this.imageCanvas = new Image();
      this.imageCanvas.src = this.imagePath;
      this.imageCanvas.onload = () => {
        const blurPixels = 20;
        this.blur(blurPixels);
      };
    },

    blur(blurPixels) {
      const { ctx, canvas, imageCanvas: image } = this;

      const newBlurPixels = this.getNewBlur(blurPixels);
      if (newBlurPixels >= 0) {
        ctx.filter = `blur(${newBlurPixels}px)`;
        ctx.drawImage(image, 0, 0, image.width, image.height, 0, 0, canvas.width, canvas.height);

        setTimeout(() => {
          this.blur(newBlurPixels);
        }, this.blurTime);
      }
    },

    getNewBlur(blurPixels) {
      if (this.blurEffectPaused) return blurPixels;

      if (blurPixels < 0) return 0;

      return blurPixels - 1;
    },

    visibleClue() {
      if (this.visible) return;

      this.penalty = (this.time * 15) / 100;
      this.visible = true;
      this.timer.bonus = -this.penalty;
    },

    timerController(data) {
      if (data.state) {
        this.timer.state = data.state;

        if (this.timer.state === 'stopped') {
          this.elapsedTime = this.time;
          this.unanswered();
        }
      }

      if (data.bonus) {
        this.timer.bonus = data.bonus;
      }
    },

    saveTime(currentTime) {
      this.elapsedTime = this.time + this.timer.bonus - currentTime;
      this.saveAnswer();
    },

    unanswered() {
      const currentAnswer = {
        questionId: this.question.id,
        value: undefined,
        time: this.time,
      };
      this.blurEffectPaused = true;

      this.$emit('finish-question', currentAnswer);
    },

    selectAnswer(value) {
      if (this.disabled) return;
      this.answer = value;
      this.timer.state = 'paused';
    },

    saveAnswer() {
      const currentAnswer = {
        questionId: this.question.id,
        value: this.answer,
        time: this.elapsedTime,
      };
      this.blurEffectPaused = true;

      this.$emit('finish-question', currentAnswer);
    },
  },
};
</script>

<style lang="scss" scoped>
.Question {
  .Timer {
    margin-bottom: $spacing-m;
  }

  .content {
    max-width: 800px;
    width: 100%;
    margin: 0 auto;

    .title {
      color: $color-neutral-darkest;
      padding: $spacing-xs;
      text-align: center;
      font-weight: 500;
      font-size: $font-size-xl;
      text-transform: uppercase;
      margin-bottom: $spacing-m;
    }

    canvas {
      margin: 0 auto;
      width: 800px;
      height: 450px;
      margin-bottom: $spacing-m;
      width: 100%;
      border-radius: $border-radius-m;
    }

    .clue-container {
      display: flex;
      gap: 15px;
      align-items: center;
      $clueLightColor: hsl(51, 100%, 63%);
      $lightBulbOnShadow: 0 0 10px #{$clueLightColor};

      .bulb {
        aspect-ratio: 1/1;
        padding: $spacing-s;
        border-radius: 50%;
        background-color: adjust-color($color: $clueLightColor, $lightness: 15%);
        transition: background-color 0.3s, transform 0.5s;

        &:hover {
          background-color: $clueLightColor;
          box-shadow: $lightBulbOnShadow;
          transform: scale(1.2);
          cursor: pointer;
        }

        &.disabled {
          filter: grayscale(80%);
          pointer-events: none;
        }
      }

      .clue {
        color: $color-primary-darker;
        padding: $spacing-s $spacing-m;
        border-radius: $border-radius-m;
        background-color: $clueLightColor;
        box-shadow: $lightBulbOnShadow;
        color: adjust-color($color: $clueLightColor, $lightness: -45%);
        font-weight: 500;
      }
    }
  }
}
</style>

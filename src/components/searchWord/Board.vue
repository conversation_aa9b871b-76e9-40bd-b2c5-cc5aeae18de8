<template>
  <div class="Board">
    <Timer
      v-if="time"
      :time="time"
      :state="timer.state"
      :bonus="timer.bonus"
      @current-time="saveTime"
      @timer-controller="timerController"
    />

    <div class="main-container max-content-width">
      <h1 class="title">
        {{ text }}
      </h1>

      <div class="board">
        <div
          class="grid"
          @mousedown.prevent="startPosition"
          @mouseup.prevent="finishedPosition"
          @touchstart.prevent="startPosition"
          @touchend.prevent="finishedPosition"
          @mousemove.prevent="draw"
          @touchmove.prevent="draw"
        >
          <div
            v-for="(row, ind) in grid"
            :key="ind"
            class="row"
          >
            <div
              v-for="(colm, j) in row"
              :key="j"
              :class="`colm${ind}-${j}`"
            >
              {{ colm }}
            </div>
          </div>
          <svg id="svg"></svg>
        </div>

        <div class="words-container">
          <a
            class="words-button"
            @click.prevent="isOpen = !isOpen"
          >
            Lista de palabras

            <font-awesome-icon
              class="icon"
              :icon="`fa-solid fa-angle-${isOpen ? 'down' : 'up'}`"
            />
          </a>

          <ul
            v-show="isOpen"
            class="list"
          >
            <li
              v-for="(word, i) in words"
              :key="i"
              :class="['word ' + i, { strikethrough: isAnsweredCorrectly(word) }]"
            >
              <span>
                {{ word }}
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';

import Timer from '@/components/common/Timer';
import { reverseString } from '../../utils/strings';

const STORE = 'SearchWordModule';
const alphabet = 'ABCDEFGHIJKLMNÑOPQRSTUVWXYZ';
const GRID_WIDTH = 12;
const GRID_HEIGHT = 12;
const CORRECT_COLOR = 'hsl(120, 71%, 60%)';
const INCORRECT_COLOR = 'hsl(5, 100%, 45%)';
const INITIAL_WIDTH = 700;

const ROUNDERS = (row, col) => {
  const i = parseInt(row, 10);
  const j = parseInt(col, 10);
  return [
    `${i - 1}-${j - 1}`,
    `${i - 1}-${j}`,
    `${i - 1}-${j + 1}`,
    `${i}-${j - 1}`,
    `${i}-${j + 1}`,
    `${i + 1}-${j - 1}`,
    `${i + 1}-${j}`,
    `${i + 1}-${j + 1}`,
  ];
};

export default {
  components: {
    Timer,
  },

  props: {
    question: {
      type: Object,
      required: true,
    },

    initBonus: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      timer: {
        bonus: this.initBonus,
        state: 'stopped',
      },
      elapsedTime: 0,
      letters: alphabet.split(''),
      correctNumber: '0',
      incorrectNumber: '0',
      isOpen: true,
      grid: [],
      width: GRID_WIDTH,
      height: GRID_HEIGHT,
      startLetterPosition: undefined,
      startLetterHtml: undefined,
      // columnArray: [],
      // selWordHtml: [],
      painting: false,
      allowPaint: false,
      canvas: undefined,
      newLine: undefined,
      wordLayouts: {
        H: (x, y, offset) => [x + offset, y],
        BH: (x, y, offset) => [x - offset, y],
        V: (x, y, offset) => [x, y + offset],
        BV: (x, y, offset) => [x, y - offset],
        D: (x, y, offset) => [x + offset, y + offset],
        UD: (x, y, offset) => [x + offset, y - offset],
        BD: (x, y, offset) => [x - offset, y + offset],
        DD: (x, y, offset) => [x - offset, y - offset],
      },
      isValidPlacement: {
        H: (x, y, wl) => x + wl <= this.width,
        BH: (x, y, wl) => x + 1 >= wl,
        V: (x, y, wl) => y + wl <= this.height,
        BV: (x, y, wl) => y + 1 >= wl,
        D: (x, y, wl) => x + wl <= this.width && y + wl <= this.height,
        UD: (x, y, wl) => x + wl <= this.width && y + 1 >= wl,
        BD: (x, y, wl) => x + 1 >= wl && y + 1 <= this.height,
        DD: (x, y, wl) => x + 1 >= wl && y + 1 >= wl,
      },
      currentAttemptWord: undefined,
      scale: 1,
      boardToSearch: undefined,
    };
  },

  computed: {
    ...get(STORE, ['areAllWordsCorrect', 'questionWordsAnsweredCorrectly']),

    wordsAnsweredCorrectly() {
      return this.questionWordsAnsweredCorrectly(this.question?.id);
    },

    text() {
      return this.question?.question;
    },

    words() {
      return this.question?.words.map((word) => word.toUpperCase());
    },

    time() {
      return this.question?.time ?? 0;
    },

    ctx() {
      return document.querySelector('#canvas').getContext('2d');
    }
  },

  watch: {
    elapsedTime(time) {
      if (time) {
        this.currentAnswer = {
          word: this.attempt ?? this.word,
          time,
        };
        this.saveAnswer(); // todo check here
      }
    },
  },

  mounted() {
    this.timer.state = 'discounting';
    this.drawEmptyGrid();
    this.wordsPlace();

    this.resizeBoard();

    setTimeout(() => {
      this.highlightWordsAnsweredPreviously();
    }, 1000);
  },

  methods: {
    resizeFix() {
      const parent = this.$el.querySelector('.grid');
      if (!parent) return 1;
      const position = parent.getBoundingClientRect();
      return INITIAL_WIDTH / position.width;
    },
  
    resizeBoard() {
      const gridBoard = this.$el.querySelector('.grid');
      const BOARD_WIDTH_FROM_WHICH_RESCALING_BEGINS = 740;
      const GRID_BOARD_WIDTH = 700;
      const resizeObserver = new ResizeObserver(([board]) => {
        this.scale = 1;
        if (board.contentRect.width <= BOARD_WIDTH_FROM_WHICH_RESCALING_BEGINS) {
          // const width = GRID_BOARD_WIDTH * (board.contentRect.width / BOARD_WIDTH_FROM_WHICH_RESCALING_BEGINS);
          const width = board.contentRect.width - (BOARD_WIDTH_FROM_WHICH_RESCALING_BEGINS - GRID_BOARD_WIDTH);
          this.scale = width / GRID_BOARD_WIDTH;
        }
        gridBoard.style.transform = `scale(${this.scale})`;
      });

      resizeObserver.observe(this.$el);
    },

    highlightWordsAnsweredPreviously() {
      this.loadBoardToSearchOnStructure();

      this.wordsAnsweredCorrectly.forEach((word) => {
        const result = this.startSearch(word);
        const firstLetter = this.$el.querySelector(`.colm${result[0].key}`);
        const lastLetter = this.$el.querySelector(`.colm${result[result.length - 1].key}`);
        const { x: firstLetterLeft, y: firstLetterTop, width, height } = firstLetter.getBoundingClientRect();
        const { x: lastLetterLeft, y: lastLetterTop } = lastLetter.getBoundingClientRect();
        const parent = this.$el.querySelector('.grid');
        const position = parent.getBoundingClientRect();

        const resizeFixValue = this.resizeFix()
        this.renderLine(
          (firstLetterLeft + width / 2 - position.left) * resizeFixValue,
          (firstLetterTop + height / 2 - position.top) * resizeFixValue,
          (lastLetterLeft + width / 2 - position.left) * resizeFixValue,
          (lastLetterTop + height / 2 - position.top) * resizeFixValue
        );
        this.newLine.setAttribute('stroke', CORRECT_COLOR);
      });
    },

    loadBoardToSearchOnStructure() {
      this.boardToSearch = this.grid.reduce((acc, row, i) => {
        const rowLetters = row.reduce((acc2, cell, j) => ({ ...acc2, [`${i}-${j}`]: cell }), {});
        return { ...acc, ...rowLetters };
      }, {});
    },

    drawLetter(letterKey, color) {
      const letter = this.$el.querySelector(`.colm${letterKey}`);
      if (!letter) return;
      letter.style.background = color;
    },

    startSearch(word) {
      const firstLetter = word.charAt(0).toUpperCase();
      const firstLetterKeys = this.getAllKeysMatching(firstLetter);
      const secondtLetter = word.charAt(1).toUpperCase();

      const firstSlices = firstLetterKeys.reduce((acc, key) => {
        const secondLetterMatchingKeys = this.getAllValidKeysAround(key, secondtLetter);
        if (!secondLetterMatchingKeys.length) return acc;

        const slice = secondLetterMatchingKeys.map((secondKey) => [key, secondKey]);
        return [...acc, ...slice];
      }, []);

      const newWord = word.substring(2, word.length);
      return firstSlices.reduce((acc, [key1, key2]) => {
        const direction = this.getDirection(key1, key2);
        const match = this.search(key2, newWord, direction);
        const newMatch = [{ key: key1, letter: firstLetter }, { key: key2, letter: secondtLetter }, ...match];

        return newMatch.length > acc.length ? newMatch : acc;
      }, []);
    },

    getDirection(key1, key2) {
      const [row1, col1] = key1.split('-');
      const [row2, col2] = key2.split('-');

      return [row2 - row1, col2 - col1];
    },

    search(letterKey, word, [directionRow, directionCol]) {
      if (!word) return [];

      const firstLetter = word.charAt(0).toUpperCase();
      const newWord = word.substring(1, word.length);

      const [row, col] = letterKey.split('-');
      const nextKey = `${parseInt(row, 10) + directionRow}-${parseInt(col, 10) + directionCol}`;
      if (this.boardToSearch[nextKey] !== firstLetter) return [];

      const match = this.search(nextKey, newWord, [directionRow, directionCol]);
      return [{ key: nextKey, letter: firstLetter }, ...match];
    },

    getAllKeysMatching(letter) {
      const matches = Object.entries(this.boardToSearch).reduce((acc, [key, value]) => {
        if (value === letter) return { ...acc, [key]: value };

        return acc;
      }, {});
      return Object.keys(matches);
    },

    getAllValidKeysAround(letterKey, letter) {
      if (!letterKey) return [];

      const [row, col] = letterKey.split('-');
      return ROUNDERS(row, col).filter((position) => this.boardToSearch[position] === letter);
    },

    isAnsweredCorrectly(word) {
      return this.wordsAnsweredCorrectly?.includes(word) || this.wordsAnsweredCorrectly?.includes(reverseString(word));
    },

    drawEmptyGrid() {
      for (let i = 0; i < GRID_HEIGHT; i += 1) {
        this.grid.push([]);
        for (let j = 0; j < GRID_WIDTH; j += 1) {
          this.grid[i].push('');
        }
      }
    },

    wordsPlace() {
      // let flag = true;
      this.words.forEach((word) => {
        const validCells = this.getValidCells(word);
        const choiceCell = validCells[Math.floor(Math.random() * validCells.length)];
        if (validCells.length > 0) {
          for (let i = 0; i < word.length; i += 1) {
            const curCell = choiceCell.layout(choiceCell.cell[0], choiceCell.cell[1], i);
            this.grid[curCell[0]][curCell[1]] = word[i];
          }
        }
        // else {
        //   flag = false;
        // }
      });

      // if (flag) {
      this.fillGrid();
      // }
    },

    getValidCells(word) {
      const validCells = [];
      let checkLayout;
      let nextCell;
      let maxOverlap = 2;

      Object.keys(this.wordLayouts).forEach((layout) => {
        nextCell = this.wordLayouts[layout];
        checkLayout = this.isValidPlacement[layout];
        let x = 0;
        let y = 0;
        while (y < GRID_HEIGHT) {
          if (checkLayout(x, y, word.length)) {
            const overlap = this.calcOverlap(word, x, y, nextCell);
            if (overlap >= maxOverlap || overlap > -1) {
              maxOverlap = overlap;
              validCells.push({ cell: [x, y], layout: nextCell, overlap });
            }
          }
          x += 1;
          if (x >= GRID_WIDTH) {
            y += 1;
            x = 0;
          }
        }
      });

      return validCells;
    },

    fillGrid() {
      this.grid = this.grid.map((row) => {
        const newRow = row.map((cell) => {
          const isEmpty = cell === '';

          return isEmpty ? this.randomLetter() : cell.toUpperCase();
        });

        return newRow;
      });
    },

    randomLetter() {
      const randomIndex = Math.floor(Math.random() * this.letters.length);
      return this.letters[randomIndex];
    },

    startPosition(event) {
      this.removeBodyScroll();

      const { target } = event;
      const isTouchEvent = /^(touch)/g.test(event.type);
      const { clientX, clientY } = isTouchEvent ? event.changedTouches[0] : event;

      if (!this.allowPaint) {
        this.painting = true;
        this.allowPaint = true;

        this.startLetterPosition = this.getLetterPosition(target);

        const parent = target.closest('.grid');
        const position = parent.getBoundingClientRect();
        const resizeFixValue = this.resizeFix()
        this.renderLine(
          (clientX - position.left) * resizeFixValue,
          (clientY - position.top) * resizeFixValue,
          (clientX - position.left) * resizeFixValue,
          (clientY - position.top) * resizeFixValue
        );
      }
    },

    getLetterPosition(target) {
      const [startPosition] = target.classList;
      const [, row, column] = startPosition.match(/(\d+)-(\d+)/);
      return { row: parseInt(row, 10), column: parseInt(column, 10) };
    },

    renderLine(x1, y1, x2, y2) {
      const svgElement = document.getElementById('svg');
      this.newLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      this.newLine.setAttribute('stroke', '#add8e6');
      this.newLine.setAttribute('stroke-linecap', 'round');
      this.newLine.setAttribute('stroke-width', '25');
      this.newLine.setAttribute('stroke-dasharray', '1');
      // this.newLine.setAttribute('stroke-dasharray', '2,14');
      this.newLine.setAttribute('stroke-opacity', '0.4');
      this.newLine.setAttribute('x1', x1);
      this.newLine.setAttribute('y1', y1);
      this.newLine.setAttribute('x2', x2);
      this.newLine.setAttribute('y2', y2);
      svgElement.appendChild(this.newLine);
    },

    finishedPosition(event) {
      const isTouchEvent = /^(touch)/g.test(event.type);
      const { clientX, clientY } = isTouchEvent ? event.changedTouches[0] : event;
      const target = document.elementFromPoint(clientX, clientY);

      this.resetBodyScroll();
      if (!this.painting) return;

      try {
        const letters = this.getWordHightlightedLetters(target);
        if (!letters) throw new Error();

        const word = letters?.reduce((acc, { letter }) => acc + letter, '');

        const isAnsweredPreviously = this.wordsAnsweredCorrectly.includes(word) || this.wordsAnsweredCorrectly.includes(reverseString(word));
        if (isAnsweredPreviously) {
          throw new Error('answered');
        }

        const selWordHtml = letters?.map(({ position }) => position) ?? [];
        if (word.length > 1) {
          this.currentAttemptWord = word;
          this.timer.state = 'paused';
        }

        const isCorrect = this.words.includes(word) || this.words.includes(reverseString(word));
        if (!isCorrect) {
          throw new Error();
        }

        this.newLine.setAttribute('stroke', CORRECT_COLOR);
        this.animateLetters(selWordHtml);
        this.addCorrectAnswer();
      } catch (error) {
        if (error.message === 'answered') {
          this.newLine.remove();
          return;
        }

        this.newLine.setAttribute('stroke', INCORRECT_COLOR);
        setTimeout(() => {
          this.newLine.remove();
        }, 1000);
      } finally {
        this.painting = false;
        setTimeout(() => {
          this.allowPaint = false;
        }, 1000);
      }
    },

    getWordHightlightedLetters(target) {
      const { row: endRow, column: endColumn } = this.getLetterPosition(target);
      const { row: startRow, column: startColumn } = this.startLetterPosition;

      const isHorizontalMovement = endRow === startRow;
      const isVerticalMovement = endColumn === startColumn;
      const isDiagonal = startRow !== endRow && startColumn !== endColumn;

      if (!isHorizontalMovement && !isVerticalMovement && !isDiagonal) {
        return undefined;
      }

      if (isHorizontalMovement) {
        return this.getHorizontalWord({
          endRow,
          endColumn,
          startRow,
          startColumn,
        });
      }

      if (isVerticalMovement) {
        return this.getVerticalWord({
          endRow,
          endColumn,
          startRow,
          startColumn,
        });
      }

      return this.getDiagonalWord({
        endRow,
        endColumn,
        startRow,
        startColumn,
      });
    },

    getHorizontalWord({ endRow, endColumn, startColumn }) {
      const isRevert = endColumn < startColumn;
      const letters = isRevert
        ? this.getWordLetters(this.grid[endRow], endColumn, startColumn).reverse()
        : this.getWordLetters(this.grid[endRow], startColumn, endColumn);

      return letters.map((letter) => ({
        letter: letter.value,
        position: `colm${endRow}-${letter.position}`,
      }));
    },

    getVerticalWord({ endRow, endColumn, startRow, startColumn }) {
      const gridColumn = [];
      for (let z = 0; z < GRID_HEIGHT; z += 1) {
        gridColumn.push(this.grid[z][startColumn]);
      }

      const isRevert = endRow < startRow;
      const letters = isRevert
        ? this.getWordLetters(gridColumn, endRow, startRow).reverse()
        : this.getWordLetters(gridColumn, startRow, endRow);

      return letters.map((letter) => ({
        letter: letter.value,
        position: `colm${letter.position}-${endColumn}`,
      }));
    },

    getDiagonalWord({ endRow, endColumn, startRow, startColumn }) {
      const expectedLastLetterDOM = document.querySelector(`.colm${endRow}-${endColumn}`);
      const expectedLastLetter = expectedLastLetterDOM?.innerText;
      const lastLetterMatchWithExpected = (letter) => !!letter && letter === expectedLastLetter;
      let startCol = startColumn;
      const gridColumn = [];

      const rightDown = startRow <= endRow && startColumn <= endColumn;
      const rightUp = startRow >= endRow && startColumn <= endColumn;
      const leftDown = startRow <= endRow && startColumn >= endColumn;
      const leftUp = startRow >= endRow && startColumn >= endColumn;

      if (rightDown) {
        for (let i = startRow; i <= endRow; i += 1) {
          gridColumn.push({ letter: this.grid[i][startCol], position: `colm${i}-${startCol}` });
          startCol += 1;
        }
      } else if (rightUp) {
        for (let i = startRow; i >= endRow; i -= 1) {
          gridColumn.push({ letter: this.grid[i][startCol], position: `colm${i}-${startCol}` });
          startCol += 1;
        }
      } else if (leftDown) {
        for (let i = startRow; i <= endRow; i += 1) {
          gridColumn.push({ letter: this.grid[i][startCol], position: `colm${i}-${startCol}` });
          startCol -= 1;
        }
      } else if (leftUp) {
        for (let i = startRow; i >= endRow; i -= 1) {
          gridColumn.push({ letter: this.grid[i][startCol], position: `colm${i}-${startCol}` });
          startCol -= 1;
        }
      }

      const lastLetter = gridColumn[gridColumn.length - 1];
      return lastLetterMatchWithExpected(lastLetter.letter) ? gridColumn : [];
    },

    getWordLetters(letters, start, end) {
      return letters.reduce((acc, letter, i) => {
        if (i >= start && i <= end) {
          const newLetter = { value: letter, position: i };
          return [...acc, newLetter];
        }
        return acc;
      }, []);
    },

    animateLetters(selWordHtml) {
      const time = (0.7 * selWordHtml.length + selWordHtml.length * 0.2) * 1000;
      const letters = [...selWordHtml];
      letters.forEach((tag, i) => {
        const letterDOM = this.$el.querySelector(`.${tag}`);
        letterDOM.classList.add('bigger');
        letterDOM.style.animationDelay = `${0.2 * i}s`;
        setTimeout(() => {
          letterDOM.classList.remove('bigger');
        }, time);
      });
    },

    addCorrectAnswer() {
      this.correctNumber = (parseInt(this.correctNumber, 10) + 1).toString();
      this.$emit('correct-answer', this.correctNumber);
    },

    draw(event) {
      if (!this.painting) return;

      const isTouchEvent = /^(touch)/g.test(event.type);
      const { clientX, clientY } = isTouchEvent ? event.changedTouches[0] : event;
      this.allowPaint = true;
      const parent = document.querySelector('.grid');
      const position = parent.getBoundingClientRect();
      const resizeFixValue = this.resizeFix()
      this.newLine.setAttribute('x2', (clientX - position.left) * resizeFixValue);
      this.newLine.setAttribute('y2', (clientY - position.top) * resizeFixValue);
    },

    calcOverlap(word, x, y, layoutFn) {
      let overlap = 0;
      for (let i = 0; i < word.length; i += 1) {
        const curCell = layoutFn(x, y, i);
        const square = this.grid[curCell[0]][curCell[1]];
        if (square === word[i]) {
          overlap += 1;
        } else if (square !== '') {
          return -1;
        }
      }
      return overlap;
    },

    haveCollision(x, y, wl, layoutFn) {
      for (let i = 0; i < wl; i += 1) {
        const curCell = layoutFn(x, y, i);
        if (this.grid[curCell[0]][curCell[1]] !== '') {
          return true;
        }
      }
      return false;
    },

    timerController(data) {
      if (data.state) {
        this.timer.state = data.state;
        if (this.timer.state === 'stopped') {
          this.saveAnswer(); // todo finish
        }
      }

      if (data.bonus !== undefined) {
        this.timer.bonus = data.bonus;
      }
    },

    saveTime(currentTime) {
      this.elapsedTime = this.time - currentTime;
    },

    async saveAnswer() {
      const word = this.currentAttemptWord?.toLowerCase();
      const time = this.elapsedTime.toFixed(2);
      const correct = this.words.includes(this.currentAttemptWord) || this.words.includes(reverseString(this.currentAttemptWord));
      const answer = {
        questionId: this.question.id,
        attempt: { word, time, correct },
      };

      await this.$store.dispatch(`${STORE}/saveAnswer`, answer);
      if (this.areAllWordsCorrect(this.question.id) || !this.currentAttemptWord) {
        const delay = this.currentAttemptWord ? this.currentAttemptWord.length * 200 : 1000;
        setTimeout(() => {
          this.finishQuestion();
        }, delay);
      } else {
        this.currentAttemptWord = undefined;
        this.timer.state = 'discounting';
      }
    },

    finishQuestion() {
      this.$emit('next-question');
    },

    resetBodyScroll() {
      document.querySelector('body').classList.remove('no-scroll');
    },
    removeBodyScroll() {
      document.querySelector('body').classList.add('no-scroll');
    },
  },
};
</script>

<style lang="scss" scoped>
.Board {
  .main-container {
    display: grid;
    gap: $spacing-m;
    padding: $spacing-m;
    width: auto;

    .title {
      color: $color-neutral-darkest;
      padding: $spacing-xs;
      text-align: center;
      font-weight: 500;
      font-size: $font-size-xl;
      text-transform: uppercase;
    }

    .board {
      display: flex;
      // flex-wrap: wrap;
      gap: $spacing-l;
      justify-content: center;
      overflow: hidden;

      @media #{$breakpoint-lg-max} {
        flex-flow: column-reverse;
      }

      .grid {
        border: 3px solid $color-primary;
        border-radius: $border-radius-m;
        background-color: $color-neutral-lighter;
        width: 700px;
        user-select: none;
        position: relative;
        z-index: 1;
        flex-shrink: 0;
        margin: auto;
        transform-origin: 0 0;

        @media (height < 750px) and (width > 991px) {
          transform: scale(.9) !important;
          transform-origin: top right;
        }

        #svg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: -1;
        }

        .row {
          text-align: center;
          display: flex;

          [class^='colm'] {
            font-size: $font-size-3xl;
            font-family: $font-family-secondary;
            font-weight: bold;
            color: $color-neutral-dark;
            text-align: center;
            flex: 1;
            display: grid;
            place-content: center;
            min-height: 35px;

            // @media #{$breakpoint-md-max} {
            //   font-size: $font-size-xl;
            // }
            // @media #{$breakpoint-sm-max} {
            //   font-size: $font-size-l;
            // }
          }
        }
      }

      .words-container {
        border-radius: $border-radius-m;
        background-color: $color-primary-lighter;
        text-align: center;
        overflow: hidden;
        position: relative;
        max-width: 350px;
        flex-shrink: 1;

        @media #{$breakpoint-lg-max} {
          max-width: 100%;
        }

        .words-button {
          padding: $spacing-m $spacing-xl;
          background: $color-primary-dark;
          font-size: $font-size-l;
          font-weight: 500;
          text-transform: uppercase;
          color: #fff;
          text-decoration: none;
          font-family: $font-family-secondary;
          display: flex;
          justify-content: center;
          gap: $spacing-m;

          @media #{$breakpoint-lg-min} {
            pointer-events: none;

            .icon {
              display: none;
            }
          }

          @media #{$breakpoint-sm-max} {
            font-size: $font-size-s;
            padding: $spacing-s $spacing-m;
          }
        }

        .list {
          padding: $spacing-m $spacing-xl;
          display: grid;
          gap: $spacing-xs;

          .word {
            font-family: $font-family-secondary;
            font-size: $font-size-xl;
            letter-spacing: 2px;
            font-weight: 500;
            color: $color-primary-dark;

            span {
              position: relative;

              &::after {
                content: '';
                position: absolute;
                left: -$spacing-2xs;
                right: calc(100% + #{$spacing-2xs});
                border-bottom: 3px solid $color-primary-dark;
                transition: all ease 0.5s;
                transform: rotate(-3deg);
              }
            }

            &.strikethrough {
              span::after {
                right: -$spacing-2xs;
                top: 50%;
              }
            }
          }

          // @media #{$breakpoint-md-max} {
          @media #{$breakpoint-lg-max} {
            display: flex;
            flex-wrap: wrap;
            gap: $spacing-m;
            justify-content: center;

            .word {
              font-size: $font-size-m;
            }
          }

          @media #{$breakpoint-sm-max} {
            padding: $spacing-s $spacing-m;

            .word {
              font-size: $font-size-s;
            }
          }
        }
      }
    }
  }

  .bigger {
    transition: all 0.7s ease-in-out;
    // transform: scale(1);
    animation-name: bigger;
    animation-duration: 0.7s;
    //animation-fill-mode: both;
  }
}
</style>

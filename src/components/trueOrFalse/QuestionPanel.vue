<template>
  <div
    class="QuestionPanel"
    :class="{minimized}"
    @click="close"
  >
    <div class="panel">
      <div
        v-if="image"
        class="image"
        :style="{ 'background-image': `url(${image})` }"
        alt="question image"
      />

      <div
        v-if="text"
        class="text"
        :class="formatWhenWordsTooLong(text)"
      >
        {{ text }}
      </div>
    </div>

    <div
      v-if="minimized"
      class="panel-icon-minimized"
    >
      <font-awesome-icon
        class="minimized-icon"
        :icon="['fas', 'eye']"
      />
    </div>

    <font-awesome-icon
      class="icon"
      :icon="['fas', 'hand-pointer']"
    />
  </div>
</template>

<script>
import LongWordsMixin from '@/mixins/LongWordsMixin';
import { disableScroll, enableScroll } from '@/utils/animation';

export default {
  mixins: [LongWordsMixin],
  props: {
    question: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      minimized: false,
    };
  },

  computed: {
    text() {
      return this.question?.text;
    },

    image() {
      const image = this.question?.imageUrl;
      if (!image) return undefined;

      return process.env.NODE_ENV === 'offline' ? image : `/${image}`;
    },
  },

  mounted() {
    const $el = document.querySelector('.main');
    disableScroll($el);
  },

  methods: {
    async close() {
      const $main = document.querySelector('.main');
      const $answers = document.querySelector('.answers');

      if (this.minimized) {
        $answers.classList.remove('question-minimized');
        disableScroll($main);
      } else {
        $answers.classList.add('question-minimized');
        enableScroll($main);
      }

      this.minimized = !this.minimized;
    },
  },
};
</script>

<style lang="scss" scoped>
.QuestionPanel {
  cursor: pointer;

  position: absolute;
  inset: 0;
  z-index: 2;
  display: grid;
  place-content: center;
  max-height: calc(100vh - 10px);
  background-color: adjust-color($color-primary-darkest, $lightness: -5%, $alpha: -.3);
  backdrop-filter: blur(2px);
  transition: background-color 0.3s linear;
  animation: fadeIn .5s linear;

  &.minimized {
    backdrop-filter: none;
    max-height: 100%;
    height: 100px;
    width: 100px;
    margin: $spacing-s auto;
    background-color: transparent;
    overflow: hidden;

    .panel {
      transition:
      transform .3s linear,
      transform-origin .3s linear,
      filter .5s ease-in-out;
      backdrop-filter: none;
      transform: scale(0.3);
      transform-origin: 50% 50%;
      margin: -100px 0;
      box-shadow: $shadow-elevation-2!important;
      filter: grayscale(50%);
      min-width: 290px;
      .image {
        height: 130px;
      }
    }

    .icon {
      display: none;
    }

    .panel-icon-minimized {
      animation: visible .3s linear .3s forwards;
    }
  }

  .panel {
    border: 2px solid #fff;
    width: 400px;
    margin: $spacing-m auto;
    background-color: #fff;
    border-radius: $border-radius-l;
    animation: appear-from-bottom 0.8s ease-in-out;
    transition: all 0.5s ease-in-out;

    @media #{$breakpoint-sm-max} {
      width: 300px;
    }

    @media screen and (max-height: #{$sm}px){
      width: 250px;
    }

    .image {
      width: 100%;
      height: 200px;
      background-size: cover;
      background-position: center;
      background-color: $color-primary-lightest;
      border-radius: $border-radius-l $border-radius-l 0 0;
      @media #{$breakpoint-sm-max} {
        height: 150px;
      }
      @media screen and (max-height: #{$sm}px){
        height: 100px;
      }
    }

    .text {
      overflow: hidden;
      font-size: $font-size-l;
      font-family: $font-family-secondary;
      align-items: center;
      text-align: center;
      padding: $spacing-m $spacing-l;
      animation: appear-from-bottom 0.8s ease-in-out;
      background-color: #fff;
      border-radius: 0 0 $border-radius-l $border-radius-l;
      @media #{$breakpoint-sm-max}, screen and (max-height: #{$sm}px) {
        font-size: $font-size-s;
        padding: $spacing-s $spacing-m;
      }
    }
  }

  .panel-icon-minimized {
    position: absolute;
    inset: 0;
    z-index: 1;
    display: grid;
    opacity: 0;
    transition: visibility 1s linear;

    &::after {
      content: '';
      position: absolute;
      inset: 5px;
      background: adjust-color($color-primary-darkest, $lightness: -5%, $alpha: -.7);
      z-index: -1;
      border-radius: 3px;
    }

    .minimized-icon{
      color: #fff;
      font-size: 1.5rem;
      position: relative;
      z-index: 1;
      margin: auto;
      opacity: 0.8;
    }
  }

  .icon{
    margin: auto;
    color: #fff;
    font-size: 3rem;
    animation: vertical-bouncing 1s infinite;
  }

  @keyframes visible {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}
</style>

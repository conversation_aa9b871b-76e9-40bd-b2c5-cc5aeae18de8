<template>
  <div
    class="AnswerPanel"
    @click="clickOnResponsive"
  >
    <img
      v-if="image"
      :src="image"
      alt="answer image"
    />

    <div
      v-if="text"
      class="text"
    >
      {{ text }}
    </div>
  </div>
</template>

<script>
import { wait } from '@/utils/animation';

export default {
  props: {
    answer: {
      type: Object,
      required: true,
    },
  },

  computed: {
    text() {
      return this.answer?.answer;
    },

    image() {
      return this.answer?.image;
    },
  },

  methods: {
    async clickOnResponsive() {
      this.$el.classList.add('selected');
      await wait(600);
      this.$emit('answer', this.answer);
    },
  },
};
</script>

<style lang="scss" scoped>
.AnswerPanel {
  max-width: 350px;
  min-height: 100px;
  width: 100%;
  flex: 1 0 auto;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  background-color: $color-primary-lightest;
  box-shadow: $shadow-elevation-1;
  border-radius: $border-radius-l;
  overflow: hidden;
  transition: all 0.5s ease-in-out;
  display: grid;
  opacity: 0;
  cursor: pointer;

  img {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }

  &:not(.selected) {
    animation: fadeIn .5s linear forwards;
  }

  &.selected {
    opacity: 1;
    animation: correct-transparent 0.6s ease-in-out;
    filter: brightness(1.2);
  }

  .text {
    padding: $spacing-s;
    background: #fff;
    color: #000;
    font-size: $font-size-xl;
    font-family: $font-family-secondary;
    display: grid;
    height: 100%;
    place-items: center;
    text-align: center;
  }

  @media #{$breakpoint-md-min} {
    flex: 0 1 360px;
  }

  @media #{$breakpoint-lg-min} {
    min-height: 250px;
  }
}
</style>

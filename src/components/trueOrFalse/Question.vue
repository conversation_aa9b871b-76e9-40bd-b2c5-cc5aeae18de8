<template>
  <div
    class="Question"
    :class="[themeClass]"
  >
    <Timer
      :time="time"
      :bonus="timer.bonus"
      :state="timer.state"
      @current-time="saveTime"
      @timer-controller="timerController"
    />

    <div
      :class="{ columns: visibleAnswers.length <= 2 }"
      class="main-container"
    >
      <QuestionPanel
        :key="currentQuestion.id"
        :question="currentQuestion"
      />

      <TransitionFade>
        <div
          class="answers"
          :class="{ categorized: currentQuestion.categorized}"
        >
          <AnswerPanel
            v-for="(answer, i) in visibleAnswers"
            :key="i"
            :answer="answer"
            :style="{ animationDelay: `${i * 0.1}s` }"
            @answer="selectAnswer($event)"
          />
        </div>
      </TransitionFade>
    </div>
  </div>
</template>

<script>
import Timer from '@/components/common/Timer';
import QuestionPanel from '@/components/trueOrFalse/QuestionPanel';
import AnswerPanel from '@/components/trueOrFalse/AnswerPanel';

import TransitionFade from '@/transitions/TransitionFade';
import { wait } from '@/utils/animation';

export default {
  components: {

    TransitionFade,
    Timer,
    QuestionPanel,
    AnswerPanel,
  },

  props: {
    currentQuestion: {
      type: Object,
      default: undefined,
    },

    initBonus: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      visibleAnswers: [],
      answerId: undefined,
      timer: {
        state: 'stopped',
        bonus: this.initBonus,
      },
      elapsedTime: 0,
    };
  },

  computed: {
    answers() {
      return this.currentQuestion?.answers;
    },

    statementVisible() {
      return this.currentQuestion;
    },
    time() {
      return this.currentQuestion?.time ?? 0;
    },
  },

  watch: {
    question(newQuestion) {
      if (newQuestion) {
        this.timer.state = 'discounting';
        this.attempts = [];
      }
    },

    elapsedTime(newTime) {
      if (newTime) {
        this.saveAnswer();
      }
    },
  },

  async mounted() {
    this.timer.state = 'discounting';
    await wait(600);
    this.makeAnswersVisibles();
  },

  methods: {
    shuffle(items) {
      const randomItem = Math.floor(Math.random() * items.length);
      const item = items[randomItem];

      if (items.length === 0) return [];

      items.splice(randomItem, 1);
      return [item, ...this.shuffle(items)];
    },

    makeAnswersVisibles() {
      if (this.answers.length !== this.visibleAnswers.length) {
        const answer = this.answers[this.visibleAnswers.length];
        if (answer) {
          this.visibleAnswers.push(answer);
        }
        // setTimeout(this.makeAnswersVisibles, 250);
        this.makeAnswersVisibles();
      }
    },

    timerController(data) {
      if (data.state) {
        this.timer.state = data.state;
        if (this.timer.state === 'stopped') {
          this.unanswered();
        }
      }
      if (data.bonus) {
        this.timer.bonus = data.bonus;
      }
    },

    saveTime(currentTime) {
      this.elapsedTime = this.time + this.initBonus - currentTime;
    },

    unanswered() {
      const currentAnswer = {
        id: undefined,
        questionId: this.currentQuestion.id,
        time: this.time + this.initBonus,
      };

      this.$emit('select-answer', currentAnswer);
    },

    async selectAnswer(answer) {
      this.answerId = answer.id;
      this.timer.state = 'paused';
    },

    saveAnswer() {
      const currentAnswer = {
        id: this.answerId,
        questionId: this.currentQuestion.id,
        time: this.elapsedTime,
      };

      this.$emit('select-answer', currentAnswer);
    },
  },
};
</script>

<style lang="scss" scoped>
.Question {
  background-color: transparent;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-rows: auto 1fr;

  &.theme {
    &--light {
      --local-background-color: #fff;
      --local-color: #000;
    }

    &--dark {
      --local-background-color: $color-secondary-darker;
      --local-color: $color-neutral-lightest;
    }
  }

  .main-container {
    display: grid;
    grid-template-columns: 1fr;
    place-content: center;
    height: 100%;
    position: relative;

    gap: $spacing-m;
    align-content: center;

    &:has(.answers.question-minimized) {
      align-content: flex-start;
    }

    .answers {
      display: flex;
      flex-flow: row wrap;
      justify-content: center;
      align-content: center;
      gap: $spacing-l;
      padding: $spacing-m $spacing-l;
      max-width: 1200px;
      transition: transform 0.5s ease-in-out;
      margin: auto;
      width: 100%;
      transform: translateY(0);

      &.question-minimized {
        transform: translateY(calc(100px + 0.75rem + 0.75rem));
        align-content: flex-start;
      }

      &:not(.categorized) .AnswerPanel {
        background-size: 150px;

        &:nth-child(1) {
          background-color: adjust-color($color: $color-success, $lightness: 25%);
          background-image: url('~@/assets/images/correct.png');
          &.selected {
            animation: correct-transparent 0.5s ease-in-out!important;
          }
        }
        &:nth-child(2) {
          background-color: adjust-color($color: $color-error, $lightness: 25%);
          background-image: url('~@/assets/images/incorrect.png');
          &.selected {
            animation: shake 0.5s ease-in-out!important;
          }
        }
      }
    }
  }
}
</style>

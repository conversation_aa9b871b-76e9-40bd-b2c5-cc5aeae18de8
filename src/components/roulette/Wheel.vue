<template>
  <div class="Wheel">
    <canvas
      :width="canvas.width"
      :height="canvas.height"
    />

    <BaseButton
      class="spin"
      @click="loadSpin()"
    >
      {{ $t('WHEEL.BUTTON') }}
    </BaseButton>
  </div>
</template>

<script>
const arrowImage = require('@/assets/images/arrow.png');
const sectionQuestionImage = require('@/assets/images/cell_question.png');
const sectionSuccessImage = require('@/assets/images/cell_success.png');

export default {
  props: {
    sections: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      canvas: {
        el: undefined,
        ctx: undefined,
        height: 620,
        width: 620,
      },
      cellTypes: {
        INCOMPLETE: undefined,
        COMPLETE: undefined,
      },
      arrow: undefined,
      startAngle: 0,
      arc: 0,
      spinning: false,
      weights: [],
      MIN_WEIGHT: 1,
      MAX_WEIGHT: 5,
      BASE_WEIGHT_TRUE: 2,
      BASE_WEIGHT_FALSE: 5,
    };
  },

  created() {
    this.arrow = arrowImage;
    this.cellTypes.INCOMPLETE = sectionQuestionImage;
    this.cellTypes.COMPLETE = sectionSuccessImage;
    this.weights = this.sections.map((section) => (section ? this.BASE_WEIGHT_TRUE : this.BASE_WEIGHT_FALSE));
  },

  mounted() {
    this.canvas.el = document.querySelector('canvas');
    this.canvas.ctx = this.canvas.el.getContext('2d');
    this.arc = Math.PI / (this.sections.length / 2);
    const canvasDOM = this.$el.querySelector('canvas');
    const primaryColor = window.getComputedStyle(canvasDOM, null).getPropertyValue('color');
    this.render(primaryColor);
  },

  methods: {
    render(color) {
      this.clear();
      this.drawCircle({ borderColor: color });
      this.drawSectionImages();
      this.drawArrow();

      setTimeout(this.render, 10);
    },

    clear() {
      this.canvas.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    },

    drawCircle({ borderColor }) {
      const radius = 240;
      const insideRadius = 0;
      const x = this.canvas.width / 2;
      const y = this.canvas.height / 2;
      const spaceBetweenSections = 0.005;

      for (let i = 0; i < this.sections.length; i += 1) {
        const angle = this.startAngle + i * this.arc;
        this.canvas.ctx.strokeStyle = borderColor;
        this.canvas.ctx.lineWidth = 1;
        this.canvas.ctx.fillStyle = '#fff'; // ['orange', 'blue', 'yellow'][i];
        this.canvas.ctx.beginPath();
        this.canvas.ctx.arc(x, y, radius, angle, angle + this.arc - spaceBetweenSections, false);
        this.canvas.ctx.arc(x, y, insideRadius, angle, this.arc, angle, true);
        this.canvas.ctx.fill();
        this.canvas.ctx.stroke();
        this.canvas.ctx.save();
      }
    },

    drawSectionImages() {
      const x = this.canvas.width / 2;
      const y = this.canvas.height / 2;
      const textRadius = 230;

      for (let i = 0; i < this.sections.length; i += 1) {
        const cellImage = new Image();
        const angle = this.startAngle + i * this.arc;
        this.canvas.ctx.translate(
          x + Math.cos(angle + this.arc / 2) * textRadius,
          y + Math.sin(angle + this.arc / 2) * textRadius
        );
        this.canvas.ctx.rotate(angle + 0.05 + this.arc / 2 + Math.PI / 2);

        cellImage.src = this.sections[i] === false ? this.cellTypes.INCOMPLETE : this.cellTypes.COMPLETE;
        this.canvas.ctx.drawImage(cellImage, cellImage.width / 2 - 140, 20);
        this.canvas.ctx.restore();
      }
    },

    drawArrow() {
      const image = new Image();
      image.src = this.arrow;
      this.canvas.ctx.drawImage(image, 280, 50, image.width, image.height);
    },

    loadSpin() {
      if (!this.spinning) {
        this.spinning = true;
        const selectedIndex = this.selectWeightedSection();
        const sectionAngle = selectedIndex * this.arc * Math.random();
        const startingAngle = sectionAngle * 10 + 20;
        const totalSpinTime = startingAngle * 100 + 2000;
        const currentSpinTime = 0;
        const speed = 30;
        this.spin(totalSpinTime, currentSpinTime, startingAngle, speed);
      }
    },

    spin(totalTime, currentTime, startingAngle, speed) {
      if (currentTime < totalTime) {
        const spinTime = currentTime + speed;
        const spinAngle = startingAngle - this.easeOut(totalTime, spinTime, startingAngle);

        this.startAngle += (spinAngle * Math.PI) / 180;

        setTimeout(() => {
          this.spin(totalTime, spinTime, startingAngle, speed);
        }, speed);
      } else {
        this.stop();
      }
    },

    easeOut(finnishTime, initialTime, angle) {
      const time = initialTime / finnishTime;
      const ts = time ** 2;
      const tc = time ** 3;
      return angle * (tc + -3.1 * ts + 3.1 * time);
    },

    stop() {
      this.spinning = false;
      const index = this.calculateCategoryIndex();
      this.updateWeights(index);
      this.$emit('finish-spinnig', index);
    },

    selectWeightedSection() {
      const adjustedWeights = this.weights.map((weight, index) => (this.sections[index] ? 0 : weight));
      const totalWeight = adjustedWeights.reduce((acc, weight) => acc + weight, 0);
      let random = Math.random() * totalWeight;
      for (let i = 0; i < this.weights.length; i += 1) {
        random -= this.weights[i];
        if (random <= 0) {
          return i;
        }
      }
      return 0;
    },

    updateWeights(selectedIndex) {
      this.weights[selectedIndex] = Math.max(this.MIN_WEIGHT, this.weights[selectedIndex] - (this.sections[selectedIndex] ? 2 : 1));

      this.weights = this.weights.map((weight, index) => {
        if (index !== selectedIndex) {
          return Math.min(this.MAX_WEIGHT, weight + (this.sections[index] ? 1 : 2));
        }
        return weight;
      });
    },

    calculateCategoryIndex() {
      const degrees = (this.startAngle * 180) / Math.PI + 90;
      const arcd = (this.arc * 180) / Math.PI;
      return Math.floor((360 - (degrees % 360)) / arcd);
    },
  },
};
</script>

<style lang="scss" scoped>
.Wheel {
  display: flex;
  flex-flow: wrap;
  align-items: center;
  justify-content: center;
  height: 100%;

  canvas {
    max-width: 100%;
    max-height: 100%;
    color: $color-primary;
  }

  .spin {
    text-transform: uppercase;
  }

  @media #{$breakpoint-lg-min} {
    & {
      justify-content: center;
      max-height: 100vh;
    }
  }
}
</style>

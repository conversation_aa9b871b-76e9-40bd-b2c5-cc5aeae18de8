<template>
  <button
    :id="`answer${answer.id}`"
    class="Answer"
    :class="[themeClass, { selected: isSelected }]"
    @click="selectAnswer"
  >
    <TransitionSlide side="left">
      <div class="letter">
        {{ letter }}
      </div>
    </TransitionSlide>

    <div class="text">
      <TransitionSlide side="right">
        <p>
          {{ text }}
        </p>
      </TransitionSlide>
    </div>
  </button>
</template>

<script>
import TransitionSlide from '@/transitions/TransitionSlide';

export default {
  components: { TransitionSlide },
  props: {
    answer: {
      type: Object,
      required: true,
    },

    letter: {
      type: String,
      required: true,
    },
    isSelected: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    text() {
      return this.answer?.answer;
    },

    id() {
      return this.answer?.id;
    },
  },

  methods: {
    selectAnswer() {
      if (!this.isSelected) {
        this.$emit('select-answer', this.id);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.Answer {
  all: unset;
  cursor: pointer;
  $letterSize: 40px;
  display: grid;
  grid-template-columns: auto 1fr;
  gap: $spacing-m;
  align-items: center;

  padding: #{$spacing-l} #{$spacing-m};
  border-radius: $border-radius-l;
  background-color: var(--local-background-color);
  color: var(--local-color);
  position: relative;
  overflow: hidden;

  transition: all 0.3s ease, border 0.15s linear;

  .letter {
    height: $letterSize;
    width: $letterSize;

    display: grid;
    place-content: center;
    font-size: $font-size-l;
    font-weight: 500;
    background-color: $color-neutral-lighter;
    color: $color-neutral-darker;
    border-radius: 100%;
    text-transform: uppercase;

    transition: all 0.3s ease;
  }

  &:hover,
  &.selected {
    background-color: var(--local-background-hover-color);
    box-shadow: inset 0 0 0 2px $color-primary;

    .letter {
      background-color: $color-primary-dark;
      color: #fff;
    }
  }

  @media (pointer:none), (pointer:coarse) {
    &:hover:not(.selected) {
      background-color: var(--local-background-color);
      box-shadow: none;
      .letter {
          background-color: $color-neutral-lighter;
          color: $color-neutral-darker;
      }
    }
  }

  .text {
    overflow: hidden;
  }

  &.theme {
    &--light {
      --local-background-color: #fff; //#{$color-neutral-lightest};
      --local-background-hover-color: #{$color-primary-lighter};
      --local-color: #000;
      --local-border-color: #{$color-neutral-light};
    }

    &--dark {
      --local-background-color: #{$color-neutral-dark};
      --local-background-hover-color: #{$color-neutral-dark};
      --local-color: #{$color-primary-lighter};
      --local-border-color: #{$color-neutral-mid};
    }
  }

  &.correct {
    --local-background-color: #{$color-success-light};
    --local-background-hover-color: #{$color-success-light};
    --local-color: #{$color-success-dark};
    --local-border-color: #{$color-success};
    box-shadow: inset 0 0 0 2px $color-success;
    animation: correct 0.5s;
    .letter {
      background-color: $color-success-dark;
      color: #fff;
    }
  }

  &.incorrect {
    --local-background-color: #{$color-error-light};
    --local-background-hover-color: #{$color-error-light};
    --local-color: #{$color-error-dark};
    --local-border-color: #{$color-error};
    box-shadow: inset 0 0 0 2px $color-error;
    animation: bounce 0.5s;
    .letter {
      background-color: $color-error-dark;
      color: #fff;
    }
  }

  @media #{$breakpoint-sm-max} {
    grid-template-columns: 100%;
    justify-items: center;
    text-align: center;

    gap: $spacing-xs;
    padding: #{$spacing-s};

    .letter {
      font-size: $font-size-s;
      width: 30px;
      height: 30px;
    }
  }
}
</style>

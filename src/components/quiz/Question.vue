<template>
  <div
    class="Question"
    :class="[themeClass]"
  >
    <Timer
      :key="question.id"
      :time="time"
      :bonus="timer.bonus"
      :state="timer.state"
      @current-time="saveTime"
      @timer-controller="timerController"
    />

    <TransitionSlide side="top">
      <p class="statement">
        {{ statement }}
      </p>
    </TransitionSlide>

    <div class="main-container">
      <slot />

      <div
        v-if="image"
        class="image"
        :style="{ 'background-image': `url(${image})` }"
      />

      <div class="answers">
        <Answer
          v-for="(answer, i) in visibleAnswers"
          :key="answer.id"
          :theme="theme"
          :letter="letters[i]"
          :answer="answer"
          :is-selected="answerId === answer.id"
          @select-answer="selectAnswer"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Timer from '@/components/common/Timer';
import Answer from '@/components/quiz/Answer';

import TransitionSlide from '@/transitions/TransitionSlide';

export default {
  components: {
    Timer,
    Answer,
    TransitionSlide,
  },

  props: {
    question: {
      type: Object,
      required: true,
    },

    time: {
      type: Number,
      default: undefined,
    },

    initBonus: {
      type: Number,
      default: 0,
    },
  },

  data() {
    const alphabet = 'abcdefghijklmnñopqrstuvwxyz';
    return {
      letters: alphabet.split(''),
      visibleAnswers: [],
      answerId: undefined,

      timer: {
        state: 'stopped',
        bonus: this.initBonus,
      },
      elapsedTime: 0,
    };
  },

  computed: {
    statement() {
      return this.question?.question;
    },

    image() {
      const image = this.question?.imageUrl;
      if (!image) return undefined;

      return process.env.NODE_ENV === 'offline' ? image : `/${image}`;
    },

    answers() {
      const answers = this.question?.answers;
      if (!this.question?.random) return answers;

      return this.shuffle([...answers]);
    },
  },

  watch: {
    question(newQuestion) {
      if (newQuestion) {
        this.timer.state = 'discounting';
        this.attempts = [];
      }
    },

    elapsedTime(newTime) {
      if (newTime) {
        this.saveAnswer();
      }
    },
  },

  mounted() {
    this.timer.state = 'discounting';
    this.makeAnswersVisibles();
  },

  methods: {
    shuffle(items) {
      const randomItem = Math.floor(Math.random() * items.length);
      const item = items[randomItem];

      if (items.length === 0) return [];

      items.splice(randomItem, 1);
      return [item, ...this.shuffle(items)];
    },

    makeAnswersVisibles() {
      if (this.answers.length !== this.visibleAnswers.length) {
        const answer = this.answers[this.visibleAnswers.length];
        if (answer) {
          this.visibleAnswers.push(answer);
        }
        setTimeout(this.makeAnswersVisibles, 0);
      }
    },

    timerController(data) {
      if (data.state) {
        this.timer.state = data.state;

        if (this.timer.state === 'stopped') {
          this.unanswered();
        }
      }

      if (data.bonus) {
        this.timer.bonus = data.bonus;
      }
    },

    saveTime(currentTime) {
      this.elapsedTime = this.time + this.initBonus - currentTime;
    },

    unanswered() {
      const currentAnswer = {
        id: undefined,
        questionId: this.question.id,
        time: this.time + this.initBonus,
      };

      this.$emit('finish-question', currentAnswer);
    },

    selectAnswer(answerId) {
      this.answerId = answerId;
      this.timer.state = 'paused';
    },

    saveAnswer() {
      const currentAnswer = {
        id: this.answerId,
        questionId: this.question.id,
        time: this.elapsedTime,
      };

      this.$emit('finish-question', currentAnswer);
    },
  },
};
</script>

<style lang="scss" scoped>
.Question {
  color: var(--local-color);

  &.theme {
    &--light {
      --local-color: #000;
    }

    &--dark {
      --local-color: #{$color-neutral-lightest};
    }
  }

  .statement {
    padding: clamp($spacing-l, 2vw, $spacing-xl);
    text-align: center;
    font-size: clamp($font-size-l, 2vw, $font-size-2xl);
    font-weight: 500;
  }

  .main-container {
    display: flex;
    justify-content: center;
    max-width: 1200px;
    margin: auto;
    padding: 0 #{$spacing-s};
    gap: $spacing-xs;
    box-sizing: border-box;
    width: 100%;

    .image {
      background-color: $color-neutral-lighter;
      background-size: cover;
      background-position: center;
      min-width: 200px;
      max-width: 400px;
      width: 100%;
      box-shadow: $shadow-elevation-1;
      border-radius: $border-radius-l;
      max-height: 300px;
      flex: 1;
    }

    .answers {
      display: grid;
      align-content: start;
      gap: $spacing-xs;
      width: 100%;
      max-width: 900px;
      flex: 2;
      margin: auto;
      position: relative;
    }
  }

  @media #{$breakpoint-lg-max} {
    .main-container {
      justify-items: center;
      flex-direction: column;

      .image {
        max-width: inherit;
        aspect-ratio: 1 / 1;
      }
    }
  }
}
</style>

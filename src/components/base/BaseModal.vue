<template>
  <TransitionFade>
    <div
      class="BaseModal"
      :class="[`size--${size}`]"
    >
      <div class="modal">
        <div class="modal-body">
          <slot />
        </div>
      </div>
    </div>
  </TransitionFade>
</template>

<script>
import TransitionFade from '@/transitions/TransitionFade';

const sizes = ['s', 'm', 'l', 'xl', 'full'];
export default {
  components: { TransitionFade },

  props: {
    size: {
      type: String,
      default: sizes[0],
      validator: (size) => sizes.includes(size),
    },
  },
};
</script>

<style scoped lang="scss" sc>
// @import "~@/assets/styles/_global.scss";
.BaseModal {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  z-index: 20;
  padding: var(--local-padding);
  padding: $spacing-m;

  .modal {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-height: 100%;
    max-width: var(--max-width);
    margin: 0 auto;
    border-radius: var(--border-radius);
    background: $color-neutral-lightest;
    box-shadow: $shadow-elevation-3;
    border-radius: $border-radius-m;
    overflow-y: hidden;

    .modal-body {
      flex: 1;
      display: grid;
      align-items: flex-start;
      padding: $spacing-m;
      color: $color-neutral-darker;
      overflow-y: auto;
    }
  }

  &.size {
    &--s {
      --local-padding: #{$spacing-3xl} #{$spacing-m};
      --max-width: 550px;
      --border-radius: #{$border-radius-s};
    }

    &--m {
      --local-padding: #{$spacing-2xl} #{$spacing-m};
      --max-width: 800px;
      --border-radius: #{$border-radius-m};
    }

    &--l {
      --local-padding: #{$spacing-m};
      --max-width: 1000px;
      --border-radius: #{$border-radius-l};
    }

    &--xl {
      --local-padding: #{$spacing-m};
      --max-width: 1400px;
      --border-radius: #{$border-radius-l};
    }

    &--full {
      --local-padding: #{$spacing-2xs};
      --max-width: auto;
      --border-radius: #{$border-radius-m};
    }
  }
}
</style>

<template>
  <div class="BaseInput">
    <div
      class="input"
      :class="`theme--${theme}`"
    >
      <label
        :for="labelTag"
        class="invisible"
      >
        {{ labelTag }}
      </label>

      <input
        :id="labelTag"
        v-model="innerValue"
        :name="labelTag"
        :class="[{'right': units}]"
        v-bind="inputAttributes"
      />

      <span
        v-if="units"
        class="units"
      >
        {{ units }}
      </span>
    </div>

    <slot name="error" />
  </div>
</template>

<script>

export default {
  inheritAttrs: false,

  props: {
    value: {
      type: null,
      default: undefined,
    },

    theme: {
      type: String,
      default: 'default',
      validator: (theme) => ['default', 'light'].includes(theme),
    },
  },

  computed: {
    innerValue: {
      get() {
        return this.value;
      },

      set(newValue) {
        let value = newValue;
        if (this.$attrs.type === 'number') {
          if (newValue === '') {
            value = undefined;
          } else {
            value = parseFloat(newValue, 10);
          }
        }

        this.$emit('input', value);
      },
    },

    labelTag() {
      return this.$attrs.name;
    },

    units() {
      return this.$attrs?.units;
    },

    inputAttributes() {
      return {
        ...this.$attrs, label: undefined, type: this.type,
      };
    },

    type() {
      return (!this.$attrs.type || this.$attrs.disabled) ? 'text' : this.$attrs.type;
    },
  },
};
</script>

<style lang="scss" scoped>
.BaseInput {
  display: flex;
  flex-direction: column;

  .input {
    position: relative;
    display: grid;
    grid-auto-flow: column;
    grid-template-columns: auto 1fr;
    align-items: center;
    gap: $spacing-2xs;

    &.theme{
      &--default{
        --border-radius: #{$border-radius-xl};
        --box-shadow: none;
        --border-bottom: none;
        --background-color: #{$color-neutral-lighter};
        --left: #{$spacing-l};
        --padding-left: 3.5rem;
        --focus-shadow: #{$shadow-elevation-1}, 0 0 4px 0 #{$color-primary};

        input.invalid {
          border: solid 1px #{$color-error};
        }
      }

      &--light{
        --border-radius: 0;
        --box-shadow: none;
        --border-bottom: 1px solid #{$color-neutral-mid-dark};
        --background-color: transparent;
        --left: #{$spacing-s};
        --padding-left: #{$spacing-2xl};
        --focus-shadow: 0 1px 0px 0 #{$color-primary};

        input.invalid {
          border-bottom: solid 1px #{$color-error};
        }
      }
    }

    input {
      font-size: $font-size-m;
      border: none;
      padding: $spacing-s $spacing-m;
      border-bottom: var(--border-bottom);
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      background-color: var(--background-color);
      max-width: 500px;

      &.right{
        text-align: right;
      }

      &:focus {
        outline: none;
        box-shadow: inset 0 0 0 1px $color-primary;
      }

      &:-moz-focusring {
        text-shadow: 0 0 0 transparent;
      }

      &.invalid {
        border: solid 1px #{$color-error};
      }
    }
  }

  .units{
    font-weight: 500;
  }
}
</style>

<template>
  <button
    type="button"
    class="BaseButton"
    :class="[themeClass, `variation--${variation}`, `size--${size}`]"
    v-bind="$attrs"
    @click="($attrs.disabled) ? null : click()"
  >
    <slot />
  </button>
</template>

<script>
const variationValues = ['primary', 'secondary', 'error'];
const sizeValues = ['xs', 's', 'm', 'l', 'xl'];

export default {
  props: {
    variation: {
      type: String,
      default: 'primary',
      validator: (variation) => variationValues.includes(variation),
    },

    size: {
      type: String,
      default: 'm',
      validator: (size) => sizeValues.includes(size),
    },
  },

  methods: {
    click() {
      this.$el.classList.remove('animated');
      this.$el.classList.add('animated');

      this.$emit('click');
    },
  },
};
</script>

<style lang="scss" scoped>
.BaseButton {
  box-shadow: inset 0 0 0 1px var(--button-border-color);
  border: 0;
  border-radius: $border-radius-xl;
  color: var(--button-color);
  fill: var(--button-color);
  background: var(--button-background-color);
  padding: var(--button-padding);

  font-size: var(--button-font-size);
  font-weight: 500;
  text-transform: uppercase;

  position: relative;
  z-index: 1;

  display: grid;
  grid-auto-flow: column;
  gap: $spacing-xs;
  justify-content: center;
  align-items: center;

  // overflow: hidden;
  transition: all 0.3s ease;

  user-select: none;

  &:focus,
  &:hover {
    cursor: pointer;
    outline: none;
    color: var(--button-color-hover);
    fill: var(--button-color-hover);
    background: var(--button-background-color-hover);
  }

  &:disabled {
    filter: grayscale(90%) opacity(50%);
    pointer-events: none;
  }
}

.theme--light.variation {
  &--primary {
    --button-color: #{$color-neutral-lightest};
    --button-color-hover: #{$color-neutral-lightest};
    --button-background-color: #{$color-primary};
    --button-background-color-hover: #{$color-primary-dark};
  }

  &--secondary {
    --button-color: #{$color-neutral-dark};
    --button-color-hover: #{$color-primary-dark};
    --button-background-color: #{$color-neutral-lighter};
    --button-background-color-hover: #{$color-neutral-lightest};
  }

  &--error {
    --button-color: #{$color-error-dark};
    --button-color-hover: #{$color-error-dark};
    $color-background: adjust-color(
      $color-error,
      $saturation: -60%,
      $lightness: 35%
    );
    $color-background-hover: adjust-color(
      $color-error,
      $saturation: -60%,
      $lightness: 30%
    );
    --button-background-color: #{$color-background};
    --button-background-color-hover: #{$color-background-hover};
  }
}

.size {
  &--xs {
    --button-padding: 0;
    --button-font-size: #{$font-size-xs};
  }

  &--s {
    --button-padding: #{$spacing-xs} #{$spacing-m};
    --button-font-size: #{$font-size-xs};
  }

  &--m {
    --button-padding: #{$spacing-s} #{$spacing-xl};
    --button-font-size: #{$font-size-m};
  }

  &--l {
    --button-padding: #{$spacing-s} #{$spacing-l};
    --button-font-size: #{$font-size-m};
  }

  &--xl {
    --button-padding: #{$spacing-l} #{$spacing-2xl};
    --button-font-size: $font-size-2xl;
  }
}
</style>

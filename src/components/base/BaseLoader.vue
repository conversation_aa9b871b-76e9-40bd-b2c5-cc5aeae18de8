<template>
  <TransitionFade>
    <div
      class="BaseLoader"
      :class="[`type--${type}`]"
    >
      <div
        v-for="i in 4"
        :key="i"
        class="ball-content"
      >
        <div class="ball" />
      </div>
    </div>
  </TransitionFade>
</template>

<script>
import TransitionFade from '@/transitions/TransitionFade';

const types = ['full'];
export default {
  components: {
    TransitionFade,
  },

  props: {
    type: {
      type: String,
      default: 'full',
      validator: (type) => types.includes(type),
    },
  },
};
</script>

<style lang="scss" scoped>
.BaseLoader {
  &.type {
    &--full {
      --local-position: fixed;
      --local-background: #{$color-neutral-light};
      --local-padding: 0;
    }
  }

  $balls: 4;
  $size: 40px;
  $bounce_height: $size * 2;
  $flattened_height: calc($size / 2);
  $local-color: $color-primary-dark;
  $animation-duration: 0.5s;

  background-color: var(--local-background);
  position: var(--local-position);
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background: var(--local-background);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: $spacing-2xl;

  .ball-content {
    position: relative;
    display: inline-block;
    height: $bounce_height + $flattened_height;
    width: $size;
    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      transform: rotateX(80deg);
      height: 0px;
      border-radius: 50px;
      box-shadow: 0 0 8px 3px #00000061;
      z-index: -1;
      margin: auto;
      animation: shadow $animation-duration alternate infinite ease;
      width: 60%;
    }

    .ball {
      background: linear-gradient(45deg, #{$color-primary-darker}, #{$color-primary-light});
      position: absolute;
      top: 0;
      width: $size;
      height: $size;
      border-radius: 50%;
      background-color: $local-color;
      transform-origin: 50%;
      animation: bounce $animation-duration alternate infinite ease;
    }

    @for $i from 1 through $balls {
      &:nth-child(#{$i}) {
        &:after,
        .ball {
          animation-delay: $i * 0.2s;
        }
      }
    }
  }

  @keyframes bounce {
    0% {
      top: $bounce_height;
      height: $flattened_height;
      border-radius: 60px 60px 20px 20px;
      transform: scaleX(2);
    }
    35% {
      height: $size;
      border-radius: 50%;
      transform: scaleX(1);
    }
    100% {
      top: 0;
    }
  }

  @keyframes shadow {
    0% {
      width: 60%;
      opacity: 1;
    }
    100% {
      width: 0%;
      opacity: 0.5;
    }
  }
}
</style>

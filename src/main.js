import vueVimeoPlayer from 'vue-vimeo-player';
import Vue from 'vue';

import '@/config/axiosConfig';
import '@/config/baseComponentsImporter';
import ThemeMixin from '@/mixins/ThemeMixin';
import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import {
  faRotateRight,
  faAngleRight,
  faAngleDown,
  faAngleUp,
  faAnglesRight,
  faFaceLaughBeam,
  faCheck,
  faHandPointer,
  faEye,
} from '@fortawesome/free-solid-svg-icons';

import { faFaceFrown } from '@fortawesome/free-regular-svg-icons';

import App from './App';
import router from './router';
import store from './store/index';
import i18n from './i18n';
import GLOBALSETTINGS from './constants';

const MOCK_ENVIRONMENTS = ['offline', 'testing'];

Vue.use(vueVimeoPlayer);

/* add icons to the library */
library.add(faRotateRight, faAngleRight, faAnglesRight, faFaceFrown, faFaceLaughBeam, faCheck, faAngleDown, faAngleUp, faHandPointer, faEye);

/* add font awesome icon component */
Vue.component('font-awesome-icon', FontAwesomeIcon);

Vue.prototype.$settings = GLOBALSETTINGS;

Vue.config.productionTip = false;
Vue.mixin(ThemeMixin);

window.Vue = new Vue({
  router,
  store,
  i18n,
  render: (h) => h(App),
}).$mount('#app');

if (MOCK_ENVIRONMENTS.includes(process.env.NODE_ENV)) {
  // eslint-disable-next-line global-require
  const { makeServer } = require('./mocks/server');
  makeServer();
}

class CookieUtil {
  static setCookie(name, value, duration) {
    document.cookie = `${name}=${value}; secure; max-age=${duration}; path=/;`;
  }

  static getCookie(name) {
    const regex = new RegExp(`(?:(?:^|.*;\\s*)${name}\\s*=\\s*([^;]*).*$)|^.*$`);
    return document.cookie.replace(regex, '$1');
  }

  static removeCookie(name) {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  }
}

export default CookieUtil;

import { USER_TOKEN_KEY, USER_TOKEN_REFRESH_KEY, TOKEN, REFRESH_TOKEN, COOKIE_MAX_AGE } from '../constants/index';
import <PERSON><PERSON>Util from './cookie';

export function setToken(token) {
  localStorage.setItem(USER_TOKEN_KEY, token);
  CookieUtil.setCookie(TOKEN, token, COOKIE_MAX_AGE);
}

export function deleteToken() {
  localStorage.removeItem(USER_TOKEN_KEY);
  CookieUtil.removeCookie(TOKEN);
}

export function setRefreshToken(token) {
  localStorage.setItem(USER_TOKEN_REFRESH_KEY, token);
  CookieUtil.setCookie(REFRESH_TOKEN, token, COOKIE_MAX_AGE);
}

export function deleteRefreshToken() {
  localStorage.removeItem(USER_TOKEN_REFRESH_KEY);
  CookieUtil.removeCookie(REFRESH_TOKEN);
}

export function getToken() {
  return localStorage.getItem(USER_TOKEN_KEY);
}

export function getRefreshToken() {
  return localStorage.getItem(USER_TOKEN_REFRESH_KEY);
}

export const wait = (milliseconds = 0) => new Promise((resolve) => {
  setTimeout(() => {
    resolve();
  }, milliseconds);
});

const validateElement = ($el) => ($el && ($el instanceof HTMLElement));

export const animate = async ($el, className, milliseconds) => {
  if (!validateElement($el)) throw new Error('Invalid element');

  $el.classList.add(className);
  await wait(milliseconds);
  $el.classList.remove(className);
};

export const disableScroll = ($el) => {
  if (!validateElement($el)) throw new Error('Invalid element');

  if ($el.classList.contains('no-scroll')) return;

  $el.classList.add('no-scroll');
  $el.scrollTo(0, 0);
};

export const enableScroll = ($el) => {
  if (!validateElement($el)) throw new Error('Invalid element');

  if (!$el.classList.contains('no-scroll')) return;

  $el.classList.remove('no-scroll');
};

// eslint-disable-next-line import/no-extraneous-dependencies
import { faker } from '@faker-js/faker';

let generatedQuestions = [];
for (let i = 1; i <= 9; i += 1) {
  const image = faker.image.abstract(undefined, undefined, true);
  const text = faker.word.adjective();
  const pair = [...Array(2).keys()].map((j) => ({
    id: i * 10 + (j + 1),
    image,
    text,
    pair: i,
  }));

  generatedQuestions = [...generatedQuestions, ...pair];
}

export default {
  NAME: 'MemoryMatch',
  ATTEMPTS_DATA: {
    // answers: [
    //   {
    //     cards: [12, 11],
    //     time: 10,
    //     correct: true,
    //   },
    // ],
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions.map((question) => ({ ...question, pair: undefined })),
    time: 2000000,
  },
  CHECK_DATA: (data) => {
    if (!data.cards || data.cards.length !== 2) {
      return { correct: false };
    }

    const [cardId1, cardId2] = data.cards;
    const firstCard = generatedQuestions.find(({ id }) => id === cardId1);
    const secondCard = generatedQuestions.find(({ id }) => id === cardId2);

    return {
      correct: firstCard.pair === secondCard.pair,
    };
  },
};

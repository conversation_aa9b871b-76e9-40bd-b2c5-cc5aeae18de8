// eslint-disable-next-line import/no-extraneous-dependencies
import { faker } from '@faker-js/faker';

const generatedQuestions = [...Array(5).keys()].map((i) => {
  const id = i + 1;
  return {
    id,
    question: `¿Pregunta ${id}?`,
    imageUrl: faker.image.abstract(),
    random: true,
    answers: [...Array(3).keys()].map((j) => ({
      id: (id * 10) + (j + 1),
      answer: `Respuesta ${id}.${j + 1}`,
    })),
    time: 20,
  };
});

export default {
  NAME: 'Roulette',
  ATTEMPTS_DATA: {
    sections: [true, false, false],
    answers: [
      {
        id: 1,
        questionId: 1,
        correct: true,
        time: 5,
      },
    ],
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions,
  },
  CHECK_DATA: (data) => {
    const correct = (data.questionId < 5);
    return { correct };
  },
};

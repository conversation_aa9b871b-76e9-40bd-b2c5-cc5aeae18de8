// eslint-disable-next-line import/no-extraneous-dependencies
import { faker } from '@faker-js/faker';

const generatedQuestions = [...Array(5).keys()].map((i) => {
  const id = i + 1;
  return {
    id,
    question: `¿Pregunta ${id}?`,
    imageUrl: faker.image.abstract(),
    random: true,
    answers: [...Array(3).keys()].map((j) => ({
      id: id * 10 + (j + 1),
      answer: `Respuesta ${id}.${j + 1}`,
    })),
    time: 20,
  };
});

export default {
  NAME: 'Puzzle',
  ATTEMPTS_DATA: {
    // answers: [
    //   {
    //     id: 1,
    //     questionId: 1,
    //     time: 5,
    //     correct: true,
    //   },
    // ],
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions,
    image: faker.image.abstract(850, 850),
    times: [60, 60, 60, 5000000],
  },
  CHECK_DATA: (data) => {
    const correct = data.questionId < 5;
    return { correct };
  },
};

const LETTERS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const generatedQuestions = [...Array(5).keys()].map((i) => {
  const id = i + 1;
  const letter = LETTERS.charAt(i);
  return {
    id,
    letter,
    question: `¿Con la letra ${letter}, que animal hace sus propias telas de seda y se sube por la paredes mientras duermes placidamente?`,
  };
});

export default {
  NAME: 'LettersWheel',
  ATTEMPTS_DATA: {
    answers: [
      {
        questionId: 1,
        time: 5,
        correct: true,
        value: 'araña',
      },
    ],
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions,
    time: 27 * 1000,
  },
  CHECK_DATA: ({ value }) => {
    if (!value) return { correct: false };

    const correct = value.charAt(0) === 'a';
    return { correct };
  },
};

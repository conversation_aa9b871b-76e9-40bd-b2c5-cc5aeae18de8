// eslint-disable-next-line import/no-extraneous-dependencies
import { faker } from '@faker-js/faker';

const generatedQuestions = [...Array(2).keys()].map((i) => {
  const id = i + 1;
  return {
    id,
    clues: [0, 2],
    question: `¿Pregunta ${id}, cuantos puntos vale la hoja de papel que esta en la imagen de abajo?`,
    imageUrl: faker.image.abstract(),
    time: 20000,
    // TODO: Habria que cambiar {'answers': []} por => {'word': "hoja"}
    answers: [
      {
        id: 1,
        answer: 'montaña rusa de papel',
      },
    ],
  };
});

export default {
  NAME: 'HiddenWords',
  ATTEMPTS_DATA: {
    // answers: [
    //   {
    //     questionId: 1,
    //     attempts: [
    //       {
    //         letter: 'e',
    //         correct: false,
    //       },
    //       {
    //         letter: 'd',
    //         correct: false,
    //       },
    //       {
    //         letter: 'o',
    //         correct: true,
    //       },
    //       {
    //         letter: 'a',
    //         correct: true,
    //       },
    //     ],
    //     correct: true,
    //     time: 15.200000000000003,
    //   },
    // ],
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions,
  },
  CHECK_DATA: () => ({
    // correct, // TODO: Cambiar result por correct (true/false)
    result: { corrects: 2, incorrects: 1 },
  }),
};

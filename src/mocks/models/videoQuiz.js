// eslint-disable-next-line import/no-extraneous-dependencies
import { faker } from '@faker-js/faker';

const TRIGGER_TIMES = [5, 20, 35, 50, 78];
const generatedQuestions = [...Array(5).keys()].map((i) => {
  const id = i + 1;
  return {
    id,
    question: `¿Pregunta ${id}?`,
    imageUrl: faker.image.abstract(),
    random: true,
    answers: [...Array(3).keys()].map((j) => ({
      id: (id * 10) + (j + 1),
      answer: `Respuesta ${id}.${j + 1}`,
    })),
    triggerTime: TRIGGER_TIMES[i],
    // triggerTime: 10 * id,
    time: 40,
  };
});

export default {
  NAME: 'VideoQuiz',
  ATTEMPTS_DATA: {
    answers: [
      {
        id: 1,
        questionId: 1,
        correct: true,
        time: 5,
      },
    ],
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions,
    videoUrl: 'https://vimeo.com/678131807/5e5ab07d4a',
    videoDuration: 96,
  },
  CHECK_DATA: (data) => {
    const correct = ((data.id % 10) === 1);
    return { correct };
  },
};

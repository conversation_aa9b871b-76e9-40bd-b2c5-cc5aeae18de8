// eslint-disable-next-line import/no-extraneous-dependencies
import { faker } from '@faker-js/faker';

const generatedQuestions = [...Array(6).keys()].map((i) => {
  const id = i + 1;
  const word = faker.word.adjective();
  return {
    id,
    image: faker.image.abstract(),
    words: word,
    clue: faker.lorem.sentence(),
    title: `${faker.lorem.sentence()} (${word})`,
    time: 20000,
  };
});

export default {
  NAME: 'HiddenPic',
  ATTEMPTS_DATA: {
    answers: [
      // {
      //   value: 'asdf',
      //   questionId: 1,
      //   correct: true,
      //   time: 5,
      // },
    ],
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions,
  },
  CHECK_DATA: (data) => {
    if (!data.value) return { correct: false };

    const currentQuestion = generatedQuestions.find((q) => q.id === data.questionId);
    const correct = data.value === currentQuestion.words;
    return { correct };
  },
};

// eslint-disable-next-line import/no-extraneous-dependencies
import { faker } from '@faker-js/faker';

const generateArray = (size) => [...Array(size).keys()];
const generateSentences = ({ number, type }) => {
  const sentence = (id) => `${faker.lorem.sentence(20)} [id${id}-${type}] ${faker.lorem.sentence(20)}`;
  const sentences = generateArray(number).map((i) => sentence(i + 1));
  return sentences.reduce((acc, text) => `${acc} ${text}`, '');
};

const TYPES = {
  DRAG: 'drag',
  SELECT: 'select',
};

const generatedQuestions = Object.values(TYPES).map((type, i) => {
  const id = i + 1;
  return {
    id,
    text: generateSentences({ number: 2, type }),
    answers: generateArray(3).map((j) => ({
      id: id * 10 + (j + 1),
      answer: `Respuesta ${id}.${j + 1}`,
      // counter: 1,
    })),
  };
});

export default {
  NAME: 'FillGaps',
  ATTEMPTS_DATA: {
    answers: [
      // {
      //   questionId: 1,
      //   attempts: [
      //     {
      //       id: 2,
      //       word: 'Respuesta 1.2',
      //       correct: true,
      //     },
      //     {
      //       id: 1,
      //       word: 'Respuesta 1.1',
      //       correct: false,
      //     },
      //   ],
      //   time: 5,
      // },
      // {
      //   questionId: 2,
      //   attempts: [
      //     {
      //       id: 2,
      //       word: 'Respuesta 1.2',
      //       correct: true,
      //     },
      //     {
      //       id: 1,
      //       word: 'Respuesta 1.1',
      //       correct: false,
      //     },
      //   ],
      //   time: 4,
      // },
    ],
    // },
    // ],
    // time: 5, // ?
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions,
    time: 10000000,
  },
  CHECK_DATA: (data) => {
    if (!data.questionId) return { attempts: [] };

    const question = generatedQuestions.find(({ id: questionId }) => questionId === data?.questionId);
    const gapsId = (text) => {
      const gap = text.match(/id\d+-(select|drag)/g).map((a) => {
        const { id } = /(?<id>\d+)/g.exec(a).groups;
        return parseInt(id, 10);
      });

      return gap;
    };

    const gaps = gapsId(question.text);
    const attempts = gaps.map((id) => {
      const attempt = data.attempts?.find((a) => a.id === id);
      return {
        id,
        // word: attempt.word,
        correct: attempt ? !/3/g.test(attempt.word) : false,
      };
    });

    return { attempts };
  },
};

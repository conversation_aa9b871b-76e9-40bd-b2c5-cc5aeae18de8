// eslint-disable-next-line import/no-extraneous-dependencies
import { faker } from '@faker-js/faker';

const MOCK_VERB = 'jump';
const MOCK_VERB2 = 'listen';

const randomNumber = () => Math.floor(Math.random() * 7 + 4);
const generatedQuestions = [...Array(1).keys()].map((i) => {
  const id = i + 1;
  return {
    id,
    question: '¿Encuentar los verbos?',
    words: [
      MOCK_VERB,
      MOCK_VERB2,
      ...[...Array(8).keys()].map(() => faker.word.verb(randomNumber)),
    ],
    time: 10000,
  };
});

export default {
  NAME: 'SearchWord',
  ATTEMPTS_DATA: {
    answers: [
      {
        questionId: 1,
        attempts: [
          { word: MOCK_VERB, time: 5, correct: true },
          { word: MOCK_VERB2, time: 10, correct: true },
        ],
      },
    ],
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions,
  },
  CHECK_DATA: () => ({}),
};

// eslint-disable-next-line import/no-extraneous-dependencies
// import { faker } from '@faker-js/faker';

const generatedQuestions = [...Array(12).keys()].map((i) => {
  const id = i + 1;
  return {
    id,
    text: [
      'primero',
      'segundo',
      'tercero',
      'cuarto',
      'quinto',
      'sexto',
      'septimo',
      'octavo',
      'noveno',
      'decimo',
      'onceavo',
      'doceavo',
    ][i],
  };
});

const mainQuestions = [...Array(1).keys()].map((i) => ({
  id: i + 1,
  title: `Pregunta ${i + 1}`,
  questions: generatedQuestions,
  time: 20000000,
}));

const UNANSWERED_ATTEMPT = generatedQuestions.map(({ id }) => ({
  id,
  correct: false,
}));

export default {
  NAME: 'HigherLower',
  ATTEMPTS_DATA: {
    answers: [
      {
        questionId: 1,
        attempt: UNANSWERED_ATTEMPT,
        time: 10,
      },
    ],
  },
  QUESTIONS_DATA: {
    questions: mainQuestions,
  },
  CHECK_DATA: (data) => {
    if (!data.ids) {
      return { attempt: UNANSWERED_ATTEMPT };
    }

    return {
      attempt: data.ids.map((id, i) => ({
        id,
        correct: generatedQuestions[i].id === id,
      })),
    };
  },
};

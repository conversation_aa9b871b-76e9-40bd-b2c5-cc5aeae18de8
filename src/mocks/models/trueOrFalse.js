// eslint-disable-next-line import/no-extraneous-dependencies
import { faker } from '@faker-js/faker';

const generatedQuestions = [...Array(5).keys()].map((i) => {
  const id = i + 1;
  return {
    id,
    text: faker.lorem.sentences(2),
    categorized: false,
    imageUrl: faker.image.animals(),
    // random: true, //! deshabilitado (incompleto en codigo)
    answers: [...Array(2).keys()].map((j) => ({
      id: id * 10 + (j + 1),
      // answer: `Respuesta ${id}.${j + 1}`, // ? opcional
      // image: faker.image.animals(), // ? opcional
    })),
  };
});

export default {
  NAME: 'TrueOrFalse',
  ATTEMPTS_DATA: {
    answers: [
      {
        id: 12,
        questionId: 1,
        time: 3,
        correct: false,
      },
    ],
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions,
    time: 10000, // * Tiempo global del juego
  },
  CHECK_DATA: () => ({
    correct: true,
  }),
};

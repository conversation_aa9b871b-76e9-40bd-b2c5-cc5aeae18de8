// eslint-disable-next-line import/no-extraneous-dependencies
import { faker } from '@faker-js/faker';

const generatedQuestions = [...Array(5).keys()].map((i) => {
  const id = i + 1;
  return {
    id,
    question: `¿Pregunta ${id}?`,
    imageUrl: faker.image.abstract(),
    random: true,
    answers: [...Array(3).keys()].map((j) => ({ id: id * 10 + (j + 1), answer: `Respuesta ${id}.${j + 1}` })),
    time: 2000000,
  };
});

export default {
  NAME: 'DoubleOrNothing',
  ATTEMPTS_DATA: {
    // TODO: cambiar por answers
    attempts: [
      [
        {
          id: 1,
          questionId: 1,
          time: 5.803,
          correct: true,
        },
        {
          id: 2,
          questionId: 2,
          time: 3.307,
          correct: true,
        },
      ],
    ],
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions,
  },
  CHECK_DATA: (data) => {
    const correct = data.questionId < 5;
    return { correct };
  },
};

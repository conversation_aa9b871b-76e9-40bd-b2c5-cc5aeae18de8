// eslint-disable-next-line import/no-extraneous-dependencies
import { faker } from '@faker-js/faker';

function shuffle(word) {
  const letters = word.split('');
  const randomItem = Math.floor(Math.random() * letters.length);
  const item = letters[randomItem];

  if (letters.length === 0) return [];

  letters.splice(randomItem, 1);
  const shuffledLetters = [item, ...shuffle(letters.join(''))];
  return shuffledLetters.join('');
}

const generatedQuestions = [...Array(2).keys()].map((i) => {
  const id = i + 1;
  const word = faker.word.verb().toLowerCase();

  return {
    id,
    question: `¿Pregunta ${id}?`,
    word: shuffle(word),
    correctWord: word,
    time: 20000000,
  };
});

export default {
  NAME: 'GuessWord',
  ATTEMPTS_DATA: {
    answers: [
      {
        questionId: 1,
        attempts: [
          {
            word: 'penla',
            time: 2,
            correct: false,
          },
          {
            word: 'asdf',
            time: 2,
            correct: true,
          },
        ],
      },
    ],
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions.map((question) => ({ ...question, correctWord: undefined })),
  },
  CHECK_DATA: (data) => {
    const question = generatedQuestions.find(({ id }) => id === data.questionId);
    const correct = question.correctWord === data?.word;
    return { correct };
  },
};

// eslint-disable-next-line import/no-extraneous-dependencies
import { faker } from '@faker-js/faker';

const generatedQuestions = [...Array(2).keys()].map((i) => {
  const id = i + 1;
  const word = faker.word.verb(6).toLowerCase();
  console.log(word);
  return {
    id,
    question: `¿Pregunta ${id}?`,
    letters: word.length,
    correctWord: word,
    time: 10000000000,
  };
});

export default {
  NAME: 'Wordle',
  ATTEMPTS_DATA: {
    answers: [
      {
        questionId: 1,
        attempts: [{
          letters: [
            { letter: 'n', correct: false, present: true },
            { letter: 'i', correct: false, present: false },
            { letter: 'g', correct: false, present: false },
            { letter: 'g', correct: true, present: true },
            { letter: 'l', correct: false, present: false },
            { letter: 'e', correct: false, present: false },
          ],
          time: 4.3010,
        }],
      },
    ],
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions.map((question) => ({ ...question, correctWord: undefined })),
  },
  CHECK_DATA: (data) => {
    const question = generatedQuestions.find(({ id }) => id === data.questionId);
    const correctWordLetters = question.correctWord?.split('') ?? [];
    const wordLetters = data?.word?.split('') ?? [];

    const letters = wordLetters.map((letter, i) => {
      const expectedLetter = correctWordLetters[i];
      const correct = expectedLetter === letter;
      const present = correctWordLetters.indexOf(letter) >= 0;
      return { letter, correct, present };
    });

    return { letters };
  },
};

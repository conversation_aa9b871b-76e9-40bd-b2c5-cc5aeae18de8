// eslint-disable-next-line import/no-extraneous-dependencies
import { faker } from '@faker-js/faker';

const generatedQuestions = [...Array(20).keys()].map((i) => {
  const id = i + 1;
  return {
    id,
    question: `¿Pregunta ${id}?`,
    imageUrl: faker.image.abstract(),
    random: true,
    feedback: {
      active: true,
      correct: 'La respuesta es correcta!',
      incorrect: 'La respuesta es incorrecta... deberías estudiar más...',
    },
    answers: [...Array(3).keys()].map((j) => ({
      id: id * 10 + (j + 1),
      answer: `Respuesta ${id}.${j + 1}`,
    })),
  };
});

export default {
  NAME: 'Quiz',
  ATTEMPTS_DATA: {
    answers: [
      {
        id: 1,
        questionId: 1,
        correct: true,
        time: 5,
      },
    ],
  },
  QUESTIONS_DATA: {
    questions: generatedQuestions,
    time: 10000,
  },
  CHECK_DATA: (data) => {
    const correct = data.id % 2 === 0;
    return { correct };
  },
};

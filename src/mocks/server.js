/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable no-shadow */
/* eslint-disable import/prefer-default-export */
import { createServer } from 'miragejs';
import { faker } from '@faker-js/faker';

import Quiz from './models/quiz';
import Roulette from './models/roulette';
import Puzzle from './models/puzzle';
import HiddenWords from './models/hiddenWords';
import DoubleOrNothing from './models/doubleOrNothing';
import LettersWheel from './models/lettersWheel';
import TrueOrFalse from './models/trueOrFalse';
import FillGaps from './models/fillGaps';
import HiddenPic from './models/hiddenPic';
import MemoryMatch from './models/memoryMatch';
import HigherLower from './models/higherLower';
import GuessWord from './models/guessWord';
import Wordle from './models/wordle';
import SearchWord from './models/searchWord';
import Categorized from './models/Categorized';
import VideoQuiz from './models/videoQuiz';

const GAMES = {
  1: Quiz,
  2: Roulette,
  3: Puzzle,
  4: HiddenWords,
  5: DoubleOrNothing,
  6: LettersWheel,
  7: TrueOrFalse,
  8: FillGaps,
  9: HiddenPic,
  10: MemoryMatch,
  11: HigherLower,
  12: GuessWord,
  13: Wordle,
  14: SearchWord,
  15: Categorized,
  16: VideoQuiz,
};

const MOCK_START_DATA = (id) => ({
  course: {
    announcement: null,
    startedAt: '28-01-2021 15:42:39 UTC',
    finishedAt: null,
  },
  chapter: {
    id,
    startedAt: '28-01-2021 15:42:39 UTC',
    updatedAt: '28-01-2021 15:42:39 UTC',
    finishedAt: null,
    maxPoints: 600,
    chapter: {
      type: {
        normalizedName: GAMES[id].NAME,
      },
    },
    data: {
      ...GAMES[id].ATTEMPTS_DATA,
    },
  },
  locale: 'es',
  description: faker.lorem.paragraphs(3),
});

export function makeServer({ environment = 'development' } = {}) {
  const server = createServer({
    environment,

    routes() {
      // this.timing = 3000;
      this.namespace = 'api';

      this.get('/chapter/:id/start', (schema, request) => ({
        status: 200,
        error: false,
        data: {
          ...MOCK_START_DATA(request.params.id),
        },
      }));

      this.get('/chapters/:id/questions', (schema, request) => ({
        status: 200,
        error: false,
        data: {
          ...GAMES[request.params.id].QUESTIONS_DATA,
        },
      }));

      this.post('/chapter/:id/update', () => ({
        status: 200,
        error: false,
        data: {
          score: 450,
          description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>',
          isCheck: true,
        },
      }));

      this.post('/chapters/answer/:id/check', (schema, request) => {
        const data = JSON.parse(request.requestBody);
        return {
          status: 200,
          error: false,
          data: GAMES[request.params.id].CHECK_DATA(data),
        };
      });

      this.passthrough((request) => {
        const passesThrough = ['https://vimeo.com'].some((r) => {
          const route = new RegExp(`${r}/.*`);
          return request.url.match(route);
        });
        return passesThrough;
      });
    },
  });

  return server;
}

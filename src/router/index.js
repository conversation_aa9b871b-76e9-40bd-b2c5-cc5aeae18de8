import Vue from 'vue';
import VueRouter from 'vue-router';

Vue.use(VueRouter);

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import(/* webpackChunkName: "login" */ '@/views/Home'),
    beforeEnter: (to, from, next) => {
      const isOfflineMode = process.env.NODE_ENV === 'offline';
      const hasQueryId = to.query?.id;

      if (isOfflineMode && !hasQueryId) {
        next({ name: 'Menu' });
      } else {
        next();
      }
    },
  },
  {
    path: '/menu',
    name: 'Menu',
    component: () => import(/* webpackChunkName: "menu" */ '@/views/GamesMenu'),
    beforeEnter: (to, from, next) => {
      const isOfflineMode = process.env.NODE_ENV === 'offline';

      if (isOfflineMode) {
        next();
      } else {
        next({ name: 'Home' });
      }
    },
  },
  {
    path: '*',
    redirect: '/',
  },
];

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes,
});

export default router;

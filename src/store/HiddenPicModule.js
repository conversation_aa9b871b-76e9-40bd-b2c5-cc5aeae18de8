/* eslint-disable max-len */
/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  answers: [],
  time: undefined,
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => state.loading,

  questions: (state) => state.questions,

  getGlobalTime: (state) => () => state.time,

  getFirstQuestion: (state) => () => state.questions[state.answers.length],

  getAnswersResults: (state) => () => {
    const results = state.answers.reduce((acc, answer) => {
      const type = answer.correct ? 'correct' : 'incorrect';
      return { ...acc, [type]: (acc[type] + 1) };
    }, { correct: 0, incorrect: 0 });
    return Object.entries(results).map(([type, value]) => ({ type, value })); // todo reefactor in all games
  },

  getCurrentQuestion: (state, getters) => () => {
    const question = state.questions?.find((question) => !getters.isAnswered(question));
    return question;
  },

  isAnswered: (state) => (question) => {
    const answer = state.answers?.find((answer) => answer.questionId === question.id);
    return (answer !== undefined);
  },

  isLastQuestion: (state, getters) => () => (getters.getCurrentQuestion() === undefined),

  getNextQuestion: (state) => (questionId) => {
    const questionIndex = state.questions.findIndex((question) => question.id === questionId);
    if (questionIndex < 0) return undefined;

    const nextQuestionIndex = (questionIndex + 1);
    if (!(nextQuestionIndex in state.questions)) return undefined;

    return state.questions[nextQuestionIndex];
  },

  getElapsedTime: (state) => () => state.answers.reduce((acc, answer) => acc + answer.time, 0),

  isCorrect: (state) => (questionId) => state.answers.some((answer) => answer.questionId === questionId && answer.correct),
};

export const mutations = {
  ...make.mutations(state),

  ADD_ANSWER(state, newAnswer) {
    state.answers = [...state.answers, newAnswer];
  },
};

export const actions = {
  async fetchQuestions({ commit, dispatch, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.get(`/chapters/${currentChapterId}/questions`);

      const attemptData = rootGetters.getChapterAttemptData();
      if (attemptData) {
        dispatch('loadDataFromPreviousAttempt', attemptData);
      }
      commit('SET_QUESTIONS', data.questions);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async saveAnswer({
    state, commit, dispatch, rootGetters,
  }, answer) {
    try {
      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.post(`/chapters/answer/${currentChapterId}/check`, answer);
      const currentQuestion = state.questions.find((question) => question.id === answer.questionId);
      if (data.correct || currentQuestion.time <= answer.time) {
        const currentAnswer = { ...answer, correct: data.correct };
        commit('ADD_ANSWER', currentAnswer);
      }
      const dataToSave = { answers: state.answers };
      await dispatch('updateChapter', dataToSave, { root: true });
    } finally {
      commit('SET_LOADING', false);
    }
  },

  loadDataFromPreviousAttempt({ commit }, data) {
    if (data.answers) {
      commit('SET_ANSWERS', data.answers);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

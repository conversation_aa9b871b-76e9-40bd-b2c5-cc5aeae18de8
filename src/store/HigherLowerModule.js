/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  answers: [],
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => () => state.loading,

  getElapsedTime: (state) => (questionId) => {
    const answers = state.answers.filter((answer) => answer.questionId === questionId);
    return answers?.reduce((acc, answer) => acc + answer.time, 0) ?? 0;
  },

  getAnswersResults: (state) => () => {
    const results = {
      correct: 0,
      incorrect: 0
    };

    if (state.answers?.length) {
      state.answers.forEach((answer) => {
        const allCorrect = answer?.attempt.length && answer?.attempt.every((option) => option.correct);
        if (allCorrect) results.correct += 1;
        else results.incorrect += 1;
      });
    }

    return Object.entries(results).map(([type, value]) => ({ type, value }));
  },

  getQuestionsNotFinished: (state) => (timeOut) => {
    if (timeOut) {
      return [];
    }
    const hasAllAttemptsCorrect = ({ attempt }) => attempt.every(({ correct }) => correct);
    const answersFinished = state.answers.filter(hasAllAttemptsCorrect);

    if (!answersFinished.length) return state.questions;

    const questionsIdAvailable = answersFinished.map(({ questionId }) => questionId);
    const notFinished = ({ id }) => !questionsIdAvailable.includes(id);
    return state.questions.filter(notFinished);
  },

  getNextQuestion: (state, getters) => () => {
    const question = state.questions.find((question) => !getters.isAnswered(question));
    return question;
  },

  isAnswered: (state) => (question) => {
    const answer = state.answers.find((answer) => answer.questionId === question.id);
    return answer !== undefined;
  },

  getFirstAvailableQuestion: (state, getters) => (timeOut) => {
    const questions = getters.getQuestionsNotFinished(timeOut);
    return questions[0];
  },
};

export const mutations = {
  ...make.mutations(state),

  ADD_ANSWER(state, newAnswer) {
    state.answers = [...state.answers, newAnswer];
  },
};

export const actions = {
  async fetchQuestions({ commit, dispatch, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();
      const { data, error } = await axios.get(`/chapters/${currentChapterId}/questions`);
      if (error) throw new Error();

      const attemptData = rootGetters.getChapterAttemptData();
      if (attemptData) {
        dispatch('loadDataFromPreviousAttempt', attemptData);
      }

      commit('SET_QUESTIONS', data.questions);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async checkAnswer({ state, commit, dispatch, rootGetters }, answer) {
    let result = [];

    try {
      const currentChapterId = rootGetters.getChapterId();
      const endpoint = `/chapters/answer/${currentChapterId}/check`;
      const { data, error } = await axios.post(endpoint, answer);

      if (error) throw new Error();

      result = data.attempt;
      const isCorrect = data.attempt.every((attempt) => attempt.correct);
      const currentQuestion = state.questions.find((question) => question.id === answer.questionId);

      if (isCorrect || currentQuestion.time <= answer.time) {
        const currentAnswer = {
          questionId: answer.questionId,
          attempt: answer.ids ? data.attempt : [],
          time: answer.time,
        };
        commit('ADD_ANSWER', currentAnswer);
      }

      const dataToSave = { answers: state.answers };
      await dispatch('updateChapter', dataToSave, { root: true });
    } finally {
      commit('SET_LOADING', false);
    }

    return result;
  },

  loadDataFromPreviousAttempt({ commit }, data) {
    if (data.answers) {
      commit('SET_ANSWERS', data.answers);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

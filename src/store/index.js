import Vue from 'vue';
import Vuex from 'vuex';

import axios from 'axios';
import TimeModule from './TimeModule';
import QuizModule from './QuizModule';
import RouletteModule from './RouletteModule';
import DoubleOrNothingModule from './DoubleOrNothingModule';
import HiddenWordsModule from './HiddenWordsModule';
import PuzzleModule from './PuzzleModule';
import LettersWheelModule from './LettersWheelModule';
import TrueOrFalseModule from './TrueOrFalseModule';
import FillGapsModule from './FillGapsModule';
import HiddenPicModule from './HiddenPicModule';
import MemoryMatchModule from './MemoryMatchModule';
import HigherLowerModule from './HigherLowerModule';
import GuessWordModule from './GuessWordModule';
import WordleModule from './WordleModule';
import SearchWordModule from './SearchWordModule';
import VideoQuizModule from './VideoQuizModule';

Vue.use(Vuex);

export default new Vuex.Store({
  namespace: true,
  state: {
    loading: true,
    course: undefined,
    chapter: undefined,
    description: undefined,
    locale: undefined,
    score: undefined,
    isCheck: undefined,
    time: undefined,
  },

  getters: {
    isLoading: (state) => state.loading,
    getCourse: (state) => () => state.course,
    getChapter: (state) => () => state.chapter,
    getChapterId: (state) => () => state.chapter?.id,
    getChapterAttemptData: (state) => () => state.chapter?.data,
    getChapterGame: (state) => () => state.chapter?.chapter?.type?.normalizedName,
    getScore: (state) => () => state.score,
    getMaxPoints: (state) => () => state.chapter.maxPoints,
    getDescription: (state) => () => state.description,
    getCheck: (state) => () => state.isCheck,
    getGlobalTime: (state) => () => state.time,
  },

  mutations: {
    SET_LOADING(state, loading) {
      state.loading = loading;
    },

    SET_COURSE(state, course) {
      state.course = course;
    },

    SET_CHAPTER(state, chapter) {
      state.chapter = chapter;
    },

    SET_SCORE(state, score) {
      state.score = score;
    },

    SET_DESCRIPTION(state, description) {
      state.description = description;
    },
    SET_LOCALE(state, locale) {
      state.locale = locale;
    },
    SET_CHECK(state, check) {
      state.isCheck = check;
    },
    SET_TIME(state, time) {
      state.time = time;
    },

  },

  actions: {
    async initChapter({ commit, dispatch }, chapterId) {
      try {
        commit('SET_LOADING', true);
        const { data } = await axios.get(`chapter/${chapterId}/start`);

        commit('SET_COURSE', data.course);
        commit('SET_CHAPTER', data.chapter);
        commit('SET_DESCRIPTION', data.description);
        commit('SET_TIME', data.chapter.data.time);

        dispatch('loadLocale', data.locale);

        dispatch('TimeModule/initTimer', null, { root: true });
      } catch (e) {
        // e ...
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async updateChapter({ state }, data) {
      try {
        const chapterId = state.chapter.id;
        await axios.post(`chapter/${chapterId}/update`, { data });
      } catch (e) {
        // e ...
      }
    },

    async finishChapter({ state, commit, dispatch }) {
      try {
        commit('SET_LOADING', true);

        const chapterId = state.chapter.id;
        const body = { finished: true };

        dispatch('TimeModule/finishTimer', null, { root: true });

        const { data } = await axios.post(`chapter/${chapterId}/update`, body);
        commit('SET_DESCRIPTION', data.description);
        commit('SET_SCORE', data.score);
        commit('SET_CHECK', data.isCheck);
      } catch (e) {
        // e ...
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async loadLocale({ commit }, locale) {
      if (window.Vue.$settings.LOCALES.some((item) => item === locale)) {
        window.Vue.$i18n.locale = locale;
        commit('SET_LOCALE', locale);
      }
    },
  },

  modules: {
    TimeModule,
    QuizModule,
    RouletteModule,
    DoubleOrNothingModule,
    HiddenWordsModule,
    PuzzleModule,
    LettersWheelModule,
    TrueOrFalseModule,
    FillGapsModule,
    HiddenPicModule,
    MemoryMatchModule,
    HigherLowerModule,
    GuessWordModule,
    WordleModule,
    SearchWordModule,
    VideoQuizModule,
  },
});

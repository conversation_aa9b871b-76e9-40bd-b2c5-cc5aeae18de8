/* eslint-disable max-len */
/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';
import { reverseString } from '../utils/strings';

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  answers: [],
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => () => state.loading,

  getQuestion: (state) => () => state.questions,

  getFirstQuestion: (state, getters) => () => state.questions.find((question) => {
    const questionAnsweredCorrectly = getters.isCorrectOrUnanswered(question.id);
    return !questionAnsweredCorrectly;
  }),

  getAnswersResults: (state) => () => {
    const results = state.answers.reduce(
      (acc, answer) => {
        const correctAttempts = answer.attempts.filter(({ correct }) => correct)?.length ?? 0;
        const incorrectAttemts = answer.attempts.length - correctAttempts;
        return {
          ...acc,
          correct: acc.correct + correctAttempts,
          incorrect: acc.incorrect + incorrectAttemts,
        };
      },
      { correct: 0, incorrect: 0 },
    );
    return Object.entries(results).map(([type, value]) => ({ type, value })); // todo reefactor in all games
  },

  getNextQuestion: (state) => (questionId) => {
    const questionIndex = state.questions.findIndex(({ id }) => id === questionId);
    const nextIndex = questionIndex + 1;
    if (!(nextIndex in state.questions)) return undefined;

    return state.questions[nextIndex];
  },

  isCorrectOrUnanswered: (state) => (questionId) => {
    const answer = state.answers.find((answer) => answer.questionId === questionId);
    if (!answer) return undefined;

    const currentQuestion = state.questions.find((question) => question.questionId === questionId);
    if (!currentQuestion) return undefined;

    const correctAttempts = answer.attempts.filter(({ correct }) => correct)?.length ?? 0;
    const isCorrect = correctAttempts === currentQuestion.words.length;
    const isUnanswered = answer.attempts.some(({ word }) => !word);
    return isCorrect || isUnanswered;
  },

  getElapsedTime: (state) => (questionId) => {
    const currentAnswer = state.answers.find((answer) => answer.questionId === questionId);
    if (!currentAnswer) return 0;
    return currentAnswer.attempts.reduce((acc, attempt) => acc + (+attempt.time), 0);
  },

  areAllWordsCorrect: (state) => (questionId) => {
    const question = state.questions.find(({ id }) => id === questionId);
    const currentAnswer = state.answers.find((answer) => answer.questionId === questionId);
    if (!currentAnswer || !currentAnswer.attempts) return false;

    return question.words.every((word) => currentAnswer.attempts.find((attempt) => attempt.word === word.toLowerCase() || reverseString(attempt.word) === word.toLowerCase()));
  },

  questionWordsAnsweredCorrectly: (state) => (questionId) => {
    const answer = state.answers.find((answer) => answer.questionId === questionId);

    if (!answer) return [];

    const attempts = answer.attempts.filter((attempt) => attempt.correct && attempt?.word);
    return attempts.map(({ word }) => word.toUpperCase());
  },
};

export const mutations = {
  ...make.mutations(state),

  ADD_ANSWER(state, answer) {
    const currentAnswer = state.answers.find(({ questionId }) => questionId === answer.questionId);
    if (!currentAnswer) {
      const newAnswer = { questionId: answer.questionId, attempts: [answer.attempt] };
      state.answers = [...state.answers, newAnswer];
    } else {
      const attempts = currentAnswer?.attempts ?? [];
      currentAnswer.attempts = [...attempts, answer.attempt];
    }
    // const answers = state.answers.filter(({ questionId }) => questionId !== answer.questionId);
    // state.answers = [...answers, answer];
  },
};

export const actions = {
  async fetchQuestions({ commit, dispatch, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.get(`/chapters/${currentChapterId}/questions`);

      const attemptData = rootGetters.getChapterAttemptData();
      if (attemptData) {
        dispatch('loadDataFromPreviousAttempt', attemptData);
      }
      commit('SET_QUESTIONS', data.questions);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async saveAnswer({ state, commit, dispatch }, answer) {
    try {
      commit('ADD_ANSWER', answer);

      const dataToSave = {
        answers: state.answers,
      };
      await dispatch('updateChapter', dataToSave, { root: true });
    } finally {
      commit('SET_LOADING', false);
    }
  },

  loadDataFromPreviousAttempt({ commit }, data) {
    if (data.answers) {
      commit('SET_ANSWERS', data.answers);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

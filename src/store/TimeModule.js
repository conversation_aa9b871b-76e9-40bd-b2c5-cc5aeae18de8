/* eslint-disable max-len */
/* eslint-disable no-shadow */
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
  loading: true,
  lastTime: undefined,
  interval: undefined,
  delay: 10000,
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => () => state.loading,

  getElapsedTime: (state) => (currentTime) => {
    const elapsedTime = Math.round((currentTime - state.lastTime) / 1000);

    return elapsedTime;
  },
};

export const mutations = {
  ...make.mutations(state),

  CLEAR_INTERVAL(state) {
    clearInterval(state.interval);
  },
};

export const actions = {
  initTimer({ commit, state, dispatch }) {
    commit('SET_LAST_TIME', Date.now());

    const interval = setInterval(() => { dispatch('sendElapsedTime'); }, state.delay);
    commit('SET_INTERVAL', interval);
  },

  async sendElapsedTime({ commit, getters, dispatch }) {
    commit('SET_LOADING', true);

    const currentTime = Date.now();
    const elapsedTime = getters.getElapsedTime(currentTime);
    if (elapsedTime > 0) {
      await dispatch('updateChapter', { time: elapsedTime }, { root: true });
    }
    commit('SET_LAST_TIME', currentTime);

    commit('SET_LOADING', false);
  },

  finishTimer({ commit, dispatch }) {
    commit('SET_LOADING', true);

    commit('CLEAR_INTERVAL');
    dispatch('sendElapsedTime');

    commit('SET_LOADING', false);
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

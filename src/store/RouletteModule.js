/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  sections: [false, false, false],
  answers: [],
});

export const state = () => getDefaultState();

export const getters = {
  isLoading: (state) => () => state.loading,

  getSectionStates: (state) => () => state.sections,

  getAnswersResults: (state) => () => {
    const results = state.answers.reduce(
      (acc, answer) => {
        const type = answer.correct ? 'correct' : 'incorrect';
        return { ...acc, [type]: acc[type] + 1 };
      },
      { correct: 0, incorrect: 0 }
    );
    return Object.entries(results).map(([type, value]) => ({ type, value })); // todo reefactor in all games
  },

  getRandomQuestion: (state, getters) => () => {
    if (!state.questions || state.questions.length === 0) return undefined;

    const questions = getters.getQuestionsToSearchOn();

    const randomIndex = parseInt(Math.random() * questions.length, 10);
    return questions[randomIndex];
  },

  getQuestionsToSearchOn: (state, getters) => () => {
    let questionsToSearchOn = getters.getUnansweredQuestions();

    if (questionsToSearchOn.length === 0) {
      questionsToSearchOn = getters.getEmptyAnsweredQuestions();
    }

    if (questionsToSearchOn.length === 0) {
      questionsToSearchOn = getters.getFailedAnsweredQuestions();
    }

    if (questionsToSearchOn.length === 0) {
      questionsToSearchOn = state.questions;
    }

    return questionsToSearchOn;
  },

  getUnansweredQuestions: (state, getters) => () => state.questions.filter((question) => !getters.isAnswered(question)),

  getAnsweredQuestions: (state, getters) => () => state.questions.filter((question) => getters.isAnswered(question)),

  getEmptyAnsweredQuestions: (state) => () => {
    const answersObject = state.answers.reduce((acc, answer) => {
      const { id, questionId } = answer;
      const unanswered = id === undefined || id === null;

      if (!acc[questionId] && unanswered) acc[questionId] = answer;
      if (acc[questionId] && !unanswered) delete acc[questionId];
      return acc;
    }, {});

    const answers = Object.values(answersObject);
    return state.questions.filter((question) => answers.some((answer) => answer.questionId === question.id)); // ? parseInt
  },

  getFailedAnsweredQuestions: (state) => () => {
    const answersObject = state.answers.reduce((acc, answer) => {
      const { questionId, correct } = answer;
      if (!acc[questionId] && !correct) acc[questionId] = answer;
      if (acc[questionId] && correct) delete acc[questionId];
      return acc;
    }, {});

    const answers = Object.values(answersObject);
    return state.questions.filter((question) => answers.some((answer) => answer.questionId === question.id)); // ? parseInt
  },

  isAnswered: (state) => (question) => state.answers.some((answer) => answer.questionId === question.id), // ? parseInt

  isLastAnswerCorrect: (state) => () => {
    const answer = state.answers[state.answers.length - 1];

    return answer && answer?.correct;
  },
};

export const mutations = {
  ...make.mutations(state),

  SET_ANSWERS(state, answers) {
    state.answers = answers;
  },

  SET_SECTIONS(state, sections) {
    state.sections = sections;
  },

  SET_SECTION(state, { section, correctState }) {
    state.sections[section] = correctState;
  },

  ADD_ANSWER(state, newAnswer) {
    state.answers = [...state.answers, newAnswer];
  },
};

export const actions = {
  async fetchQuestions({ state, commit, dispatch, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.get(`/chapters/${currentChapterId}/questions`);

      const attemptData = rootGetters.getChapterAttemptData();
      if (attemptData) {
        dispatch('loadDataFromPreviousAttempt', attemptData);
      }

      commit('SET_QUESTIONS', data.questions);

      const dataToSave = {
        answers: state.answers,
        sections: state.sections,
      };
      await dispatch('updateChapter', dataToSave, { root: true });

      // todo remove after test it
      // console.log('rootState', rootState);
      // dispatch('initChapter', null, { root: true }); // llama al padre
      // dispatch('QuizModule/test', null, { root: true }); // llama al modulo quizmodule
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async saveAnswer({ state, commit, dispatch, rootGetters }, { answer, section }) {
    try {
      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.post(`/chapters/answer/${currentChapterId}/check`, answer);

      const { id, questionId, time } = answer;
      const currentAnswer = {
        id,
        questionId,
        time,
        correct: data.correct,
      };

      commit('ADD_ANSWER', currentAnswer);

      commit('SET_SECTION', { section, correctState: data.correct });

      const dataToSave = {
        answers: state.answers,
        sections: state.sections,
      };
      await dispatch('updateChapter', dataToSave, { root: true });
    } finally {
      commit('SET_LOADING', false);
    }
  },

  loadDataFromPreviousAttempt({ commit }, data) {
    if (data.answers) {
      commit('SET_ANSWERS', data.answers);
    }

    if (data.sections) {
      commit('SET_SECTIONS', data.sections);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

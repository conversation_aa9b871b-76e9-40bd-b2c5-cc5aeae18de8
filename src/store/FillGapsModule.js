/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';

const REGEXP_OPTION = /\[id\d+-(select|drag)\]/g;

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  answers: [],
  currentQuestion: undefined,
  time: undefined,
  // currentQuestionAttempts: [],
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => () => state.loading,

  getQuestion: (state) => () => state.questions,

  getGlobalTime: (state) => () => state.currentQuestion.time,

  getFirstQuestion: (state) => () => state.questions[state.answers.length],

  getAnswersResults: (state) => () => {
    const results = {
      correct: 0,
      incorrect: 0,
    };

    if (state.answers?.length) {
      state.answers.forEach((answer) => {
        const allCorrect = answer?.attempts.length && answer?.attempts.every((option) => option.correct);
        if (allCorrect) results.correct += 1;
        else results.incorrect += 1;
      });
    }

    return Object.entries(results).map(([type, value]) => ({ type, value }));
  },

  currentQuestionIndex: (state) => state.questions.findIndex(({ id }) => id === state.currentQuestion?.id),

  getQuestionGaps: (state) => {
    const questionText = state.currentQuestion?.text;
    return questionText?.match(REGEXP_OPTION)?.length ?? 0;
  },

  getElapsedTime: (state) => () => state.answers.reduce((acc, answer) => acc + answer.time, 0),

  getNextQuestion: (state) => () => {
    if (!state.currentQuestion) return undefined;

    const { id } = state.currentQuestion;
    let currentQuestionIndex = state.questions.findIndex((question) => question.id === id);
    currentQuestionIndex += 1;

    return currentQuestionIndex in state.questions ? state.questions[currentQuestionIndex] : undefined;
  },
};

export const mutations = {
  ...make.mutations(state),

  ADD_CURRENT_ANSWER_ATTEMPTS(state, answer) {
    const currentAnswer = state.answers.find(({ questionId }) => questionId === answer.questionId);
    if (!currentAnswer) {
      state.answers = [...state.answers, answer];
    } else {
      const previousAttempts = currentAnswer.attempts ?? [];
      currentAnswer.attempts = [...previousAttempts, ...answer.attempts];
      currentAnswer.correct = answer.correct;
    }
  },
};

export const actions = {
  async fetchQuestions({ commit, getters, dispatch, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.get(`/chapters/${currentChapterId}/questions`);

      const attemptData = rootGetters.getChapterAttemptData();
      if (attemptData) {
        dispatch('loadDataFromPreviousAttempt', attemptData);
      }

      const { questions } = data;
      commit('SET_QUESTIONS', questions ?? []);
      const firstQuestion = getters.getFirstQuestion();
      commit('SET_CURRENT_QUESTION', firstQuestion);
      commit('SET_TIME', firstQuestion.time);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  setCurrentQuestion({ commit }, currentQuestion) {
    commit('SET_CURRENT_QUESTION', currentQuestion);
  },

  async checkAnswer({ state, commit, dispatch, rootGetters }, { questionId, attempts, time }) {
    let attemptsState;

    try {
      const currentChapterId = rootGetters.getChapterId();
      const endpoint = `/chapters/answer/${currentChapterId}/check`;
      const { data } = await axios.post(endpoint, { questionId, attempts, time });
      attemptsState = data.attempts;
      const isCorrect = attemptsState.every((attempt) => attempt.correct);

      if (isCorrect) {
        commit('ADD_CURRENT_ANSWER_ATTEMPTS', { questionId, attempts: attemptsState, time });
      }
      const dataToSave = {
        answers: state.answers,
      };

      await dispatch('updateChapter', dataToSave, { root: true });
    } finally {
      commit('SET_LOADING', false);
    }

    return attemptsState;
  },

  loadDataFromPreviousAttempt({ commit }, data) {
    if (data.answers) {
      commit('SET_ANSWERS', data.answers);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

/* eslint-disable max-len */
/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  image: undefined,
  times: undefined,
  totalRemainingTime: undefined,
  answers: [],
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => state.loading,

  getAnswersResults: (state) => () => {
    const results = state.answers.reduce(
      (acc, answer) => {
        const type = answer.correct ? 'correct' : 'incorrect';
        return { ...acc, [type]: acc[type] + 1 };
      },
      { correct: 0, incorrect: 0 }
    );
    return Object.entries(results).map(([type, value]) => ({ type, value })); // todo reefactor in all games
  },

  getImage: (state) => () => state.image,

  getTimes: (state) => () => state.times,

  getRandomQuestion: (state, getters) => () => {
    if (!state.questions || state.questions.length === 0) return undefined;

    const questions = getters.getQuestionsToSearchOn();

    const randomIndex = parseInt(Math.random() * questions.length, 10);
    return questions[randomIndex];
  },

  getQuestionsToSearchOn: (state, getters) => () => {
    let questionsToSearchOn = getters.getUnansweredQuestions();

    if (questionsToSearchOn.length === 0) {
      questionsToSearchOn = getters.getEmptyAnsweredQuestions();
    }

    if (questionsToSearchOn.length === 0) {
      questionsToSearchOn = getters.getFailedAnsweredQuestions();
    }

    if (questionsToSearchOn.length === 0) {
      questionsToSearchOn = state.questions;
    }

    return questionsToSearchOn;
  },

  getUnansweredQuestions: (state, getters) => () => state.questions.filter((question) => !getters.isAnswered(question)),

  getAnsweredQuestions: (state, getters) => () => state.questions.filter((question) => getters.isAnswered(question)),

  getEmptyAnsweredQuestions: (state) => () => {
    const answersObject = state.answers.reduce((acc, answer) => {
      const { id, questionId } = answer;
      const unanswered = id === undefined || id === null;

      if (!acc[questionId] && unanswered) acc[questionId] = answer;
      if (acc[questionId] && !unanswered) delete acc[questionId];
      return acc;
    }, {});

    const answers = Object.values(answersObject);
    return state.questions.filter((question) => answers.some((answer) => answer.questionId === question.id));
  },

  getFailedAnsweredQuestions: (state) => () => {
    const answersObject = state.answers.reduce((acc, answer) => {
      const { questionId, correct } = answer;
      if (!acc[questionId] && !correct) acc[questionId] = answer;
      if (acc[questionId] && correct) delete acc[questionId];
      return acc;
    }, {});

    const answers = Object.values(answersObject);
    return state.questions.filter((question) => answers.some((answer) => answer.questionId === question.id));
  },

  isAnswered: (state) => (question) => state.answers.some((answer) => answer.questionId === question.id),

  isLastQuestion: (state, getters) => () => getters.getNextQuestion() === undefined,

  isLastAnswerCorrect: (state) => () => {
    const answer = state.answers[state.answers.length - 1];

    return answer && answer?.correct;
  },
};

export const mutations = {
  ...make.mutations(state),

  ADD_ANSWER(state, newAnswer) {
    state.answers = [...state.answers, newAnswer];
  },
};

export const actions = {
  async fetchQuestions({ commit, dispatch, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.get(`/chapters/${currentChapterId}/questions`);

      const attemptData = rootGetters.getChapterAttemptData();
      if (attemptData) {
        dispatch('loadDataFromPreviousAttempt', attemptData);
      }

      commit('SET_QUESTIONS', data.questions);
      commit('SET_IMAGE', data.image);
      commit('SET_TIMES', data.times);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async saveAnswer({ state, commit, dispatch, rootGetters }, { answer, remainingTime }) {
    let correct = false;

    try {
      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.post(`/chapters/answer/${currentChapterId}/check`, answer);

      correct = data?.correct ?? false;
      const currentAnswer = { ...answer, correct };
      currentAnswer.totalRemainingTime = remainingTime;
      commit('ADD_ANSWER', currentAnswer);

      const dataToSave = { answers: state.answers, remainingTime };
      await dispatch('updateChapter', dataToSave, { root: true });
    } finally {
      commit('SET_LOADING', false);
    }

    return correct;
  },

  async saveRemainingTime({ state, dispatch }, remainingTime) {
    const dataToSave = {
      answers: state.answers,
      remainingTime,
    };
    await dispatch('updateChapter', dataToSave, { root: true });
  },

  loadDataFromPreviousAttempt({ commit }, data) {
    if (data.answers) {
      commit('SET_ANSWERS', data.answers);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

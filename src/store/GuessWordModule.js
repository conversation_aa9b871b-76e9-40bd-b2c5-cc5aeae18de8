/* eslint-disable max-len */
/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  answers: [],
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => () => state.loading,

  getQuestion: (state) => () => state.questions,

  getFirstQuestion: (state, getters) => () => state.questions.find((question) => {
    const questionAnsweredCorrectly = getters.isCorrectOrUnanswered(question.id);
    return !questionAnsweredCorrectly;
  }),

  getAnswersResults: (state) => () => {
    const results = state.answers.reduce(
      (acc, answer) => {
        const correctAttempts = answer.attempts.filter(({ correct }) => correct)?.length ?? 0;
        const incorrectAttemts = answer.attempts.length - correctAttempts;
        return {
          ...acc,
          correct: acc.correct + correctAttempts,
          incorrect: acc.incorrect + incorrectAttemts,
        };
      },
      { correct: 0, incorrect: 0 },
    );
    return Object.entries(results).map(([type, value]) => ({ type, value })); // todo reefactor in all games
  },

  getNextQuestion: (state) => (questionId) => {
    const questionIndex = state.questions.findIndex(({ id }) => id === questionId);
    const nextIndex = questionIndex + 1;
    if (!(nextIndex in state.questions)) return undefined;

    return state.questions[nextIndex];
  },

  isCorrectOrUnanswered: (state) => (questionId) => {
    const answer = state.answers.find((answer) => answer.questionId === questionId);
    if (!answer) return undefined;

    const lastAttempt = answer.attempts[answer.attempts.length - 1];
    const isUnanswered = answer.attempts.some(({ word }) => !word);
    return lastAttempt?.correct || isUnanswered;
  },

  getElapsedTime: (state) => (questionId) => {
    const currentAnswer = state.answers.find((answer) => answer.questionId === questionId);
    if (!currentAnswer) return 0;

    return currentAnswer.attempts.reduce((acc, attempt) => acc + attempt.time, 0);
  },
};

export const mutations = {
  ...make.mutations(state),

  ADD_ANSWER(state, {
    questionId, word, time, correct,
  }) {
    const answer = state.answers.find((answer) => answer.questionId === questionId);
    // const question = state.questions.find((question) => question.id === questionId);
    const attempt = { word, time, correct, questionId };
    if (!answer) {
      const newAnswer = { questionId, attempts: [attempt] };
      state.answers = [...state.answers, newAnswer];
      /* if (attempt.correct || attempt.time === question.time) {
        state.answers = [...state.answers, newAnswer];
      } */
    } else {
      answer.attempts = [...answer.attempts, attempt];
    }
  },
};

export const actions = {
  async fetchQuestions({ commit, dispatch, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.get(`/chapters/${currentChapterId}/questions`);

      const attemptData = rootGetters.getChapterAttemptData();
      if (attemptData) {
        dispatch('loadDataFromPreviousAttempt', attemptData);
      }
      commit('SET_QUESTIONS', data.questions);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async checkAnswer({
    state, commit, dispatch, rootGetters,
  }, answer) {
    try {
      let result = null;
      const currentChapterId = rootGetters.getChapterId();
      const endpoint = `/chapters/answer/${currentChapterId}/check`;
      const { data, error } = await axios.post(endpoint, answer);
      if (error) throw new Error();
      const { questionId } = answer;
      const questionTime = state.questions.find((question) => question.id === questionId).time;
      if (data.correct || answer.time >= questionTime) {
        const currentAnswer = { ...answer, correct: data.correct };
        commit('ADD_ANSWER', currentAnswer);
      }
      const dataToSave = { answers: state.answers };
      await dispatch('updateChapter', dataToSave, { root: true });
      result = data.correct;
      return result;
    } finally {
      commit('SET_LOADING', false);
    }
  },

  loadDataFromPreviousAttempt({ commit }, data) {
    if (data.answers) {
      commit('SET_ANSWERS', data.answers);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

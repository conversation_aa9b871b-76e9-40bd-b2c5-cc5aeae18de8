/* eslint-disable max-len */
/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  steps: 5,
  answers: [],
  stage: [],
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => () => state.loading,

  getQuestions: (state) => () => state.questions,

  getFirstQuestion: (state) => () => state.questions[state.answers.length],

  getAnswersResults: (state) => () => [...Array(state.steps).keys()].map((i) => state.answers[i]?.correct ?? false),

  getRandomQuestion: (state, getters) => () => {
    if (!state.questions || state.questions.length === 0) return undefined;

    const questions = getters.getQuestionsToSearchOn();

    const randomIndex = parseInt(Math.random() * questions.length, 10);
    return questions[randomIndex];
  },

  getQuestionsToSearchOn: (state, getters) => () => {
    let questionsToSearchOn = getters.getUnansweredQuestions();

    if (questionsToSearchOn.length === 0) {
      questionsToSearchOn = getters.getEmptyAnsweredQuestions();
    }

    if (questionsToSearchOn.length === 0) {
      questionsToSearchOn = getters.getFailedAnsweredQuestions();
    }

    if (questionsToSearchOn.length === 0) {
      questionsToSearchOn = state.questions;
    }

    return questionsToSearchOn;
  },

  getUnansweredQuestions: (state, getters) => () => state.questions.filter((question) => !getters.isAnswered(question)),

  getAnsweredQuestions: (state, getters) => () => state.questions.filter((question) => getters.isAnswered(question)),

  getEmptyAnsweredQuestions: (state, getters) => () => {
    const answeredQuestions = getters.getAnsweredQuestions();

    return answeredQuestions.filter((question) => {
      const answer = state.answers.find((answer) => answer.questionId === question.id);
      return answer.id === undefined;
    });
  },

  getFailedAnsweredQuestions: (state, getters) => () => {
    const answeredQuestions = getters.getAnsweredQuestions();

    return answeredQuestions.filter((question) => {
      const answer = state.answers.find((answer) => answer.questionId === question.id);
      return !answer.correct;
    });
  },

  isAnswered: (state) => (question) => {
    const answer = state.answers.find((answer) => answer.questionId === question.id);
    return answer !== undefined;
  },

  isQuestionAnsweredCorrectly: (state) => (questionId) => {
    const currentAnswer = state.answers.find((answer) => answer.questionId === questionId && answer.id !== undefined);
    return currentAnswer?.correct;
  },

  // isLastQuestion: (state, getters) => () => (getters.getNextQuestion() === undefined),
  isLastQuestion: (state) => () => state.answers.length >= 5,

  getElapsedTime: (state) => () => state.answers.reduce((acc, answer) => acc + answer.time, 0),

  isLastAnswerCorrect: (state) => () => {
    const answer = state.answers[state.answers.length - 1];

    return answer && answer?.correct;
  },
};

export const mutations = {
  ...make.mutations(state),

  ADD_ANSWER(state, newAnswer) {
    state.answers = [...state.answers, newAnswer];
  },
};

export const actions = {
  async fetchQuestions({ commit, dispatch, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.get(`/chapters/${currentChapterId}/questions`);

      commit('SET_QUESTIONS', data.questions);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async saveAnswer({ state, commit, dispatch, rootGetters }, answer) {
    try {
      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.post(`/chapters/answer/${currentChapterId}/check`, answer);

      const { id, questionId, time } = answer;
      const currentAnswer = {
        id,
        questionId,
        time,
        correct: data.correct,
      };
      commit('ADD_ANSWER', currentAnswer);

      const dataToSave = {
        answers: state.answers,
      };
      await dispatch('updateChapter', dataToSave, { root: true });
    } finally {
      commit('SET_LOADING', false);
    }
  },
  async saveTime({ commit }, time) {
    commit('ADD_TIME', time);
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

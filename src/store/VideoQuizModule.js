/* eslint-disable no-unreachable */
/* eslint-disable max-len */
/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  time: undefined,
  url: undefined,
  answers: [],
  stage: [],
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => () => state.loading,

  getQuestions: (state, getters) => () => {
    const questions = state.questions.map((question, i) => {
      const isCorrect = getters.isAnswerCorrectly(question.id);
      const state = getters.getQuestionState(i, isCorrect);
      return { ...question, state };
    });

    return questions;
  },

  getQuestionState: (state, getters) => (questionIndex, correct) => {
    const { lastCorrectQuestion } = getters;
    const lastCorrectQuestionIndex = state.questions.findIndex(({ id }) => id === lastCorrectQuestion?.id);

    if (questionIndex === lastCorrectQuestionIndex + 1) {
      return 'active';
    }

    return correct ? 'correct' : 'unanswered';
  },

  lastCorrectQuestion: (state, getters) => {
    if (!state.questions) return undefined;

    const questions = [...state.questions].reverse();
    return questions.find(({ id }) => getters.isAnswerCorrectly(id));
  },

  isAnswerCorrectly: (state) => (questionId) => {
    const answersReversed = [...state.answers].reverse();
    return answersReversed.find((answer) => answer.questionId === questionId && answer.correct);
  },

  getGlobalTime: (state) => () => state.time,

  getUrl: (state) => () => state.url,

  getFirstQuestion: (state, getters) => () => {
    const questionAnsweredCorrectly = getters.isCorrectOrUnanswered();
    let indexQuestion = 0;
    if (questionAnsweredCorrectly) {
      indexQuestion = state.questions.findIndex((question) => questionAnsweredCorrectly === question.id) + 1;
    }
    return state.questions[indexQuestion];
  },

  getAnswersResults: (state) => () => {
    const results = state.answers.reduce(
      (acc, answer) => {
        const type = answer.correct ? 'correct' : 'incorrect';
        return { ...acc, [type]: acc[type] + 1 };
      },
      { correct: 0, incorrect: 0 }
    );
    return Object.entries(results).map(([type, value]) => ({ type, value })); // todo reefactor in all games
  },

  getNextQuestion: (state) => (questionId) => {
    const questionIndex = state.questions.findIndex(({ id }) => id === questionId);
    const nextIndex = questionIndex + 1;
    if (!(nextIndex in state.questions)) return undefined;

    return state.questions[nextIndex];
  },

  isAnswered: (state) => (question) => {
    const answer = state.answers.find((answer) => answer.questionId === question.id);
    return answer !== undefined;
  },

  isLastQuestion: (state, getters) => () => getters.getNextQuestion() === undefined,

  getElapsedTime: (state) => () => state.answers.reduce((acc, answer) => acc + answer.time, 0),

  isLastAnswerCorrect: (state) => () => {
    const answer = state.answers[state.answers.length - 1];

    return answer && answer?.correct;
  },

  isCorrectOrUnanswered: (state) => () => {
    if (!state.answers?.length) return undefined;
  
    const { questionId } = state.answers
      .filter(answer => answer.correct)
      .map(answer => {
        const question = state.questions.find(q => q.id === answer.questionId);
        return question ? { questionId: question.id, triggerTime: question.triggerTime } : { questionId: null, triggerTime: 0 };
      })
      .reduce((max, current) => (current.triggerTime > max.triggerTime ? current : max), { questionId: null, triggerTime: 0 });
  
    return questionId || undefined;
  },
};

export const mutations = {
  ...make.mutations(state),

  ADD_ANSWER(state, newAnswer) {
    state.answers = [...state.answers, newAnswer];
  },
};

export const actions = {
  async fetchQuestions({ commit, dispatch, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.get(`/chapters/${currentChapterId}/questions`);

      const attemptData = rootGetters.getChapterAttemptData();
      if (attemptData) {
        dispatch('loadDataFromPreviousAttempt', attemptData);
      }
      commit('SET_QUESTIONS', data.questions);
      commit('SET_TIME', data.videoDuration);
      commit('SET_URL', data.videoUrl);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async checkAnswer({ state, commit, dispatch, rootGetters }, answer) {
    let correct;

    try {
      const currentChapterId = rootGetters.getChapterId();
      const endpoint = `/chapters/answer/${currentChapterId}/check`;
      const { data, error } = await axios.post(endpoint, answer);

      if (error) throw new Error();

      correct = data?.correct ?? false;
      const { id, questionId, time } = answer;
      const currentAnswer = {
        id,
        questionId,
        time,
        correct,
      };
      commit('ADD_ANSWER', currentAnswer);

      const dataToSave = {
        answers: state.answers,
      };
      await dispatch('updateChapter', dataToSave, { root: true });
    } finally {
      commit('SET_LOADING', false);
    }

    return correct;
  },

  loadDataFromPreviousAttempt({ commit }, data) {
    if (data.answers) {
      commit('SET_ANSWERS', data.answers);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

/* eslint-disable max-len */
/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  answers: [],
  time: undefined,
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => () => state.loading,

  getQuestion: (state) => () => state.questions,

  getGlobalTime: (state) => () => state.time,

  getFirstQuestion: (state, getters) => () => state.questions.find((question) => {
    const questionAnsweredCorrectly = getters.isCorrectOrUnanswered(question.id);
    return !questionAnsweredCorrectly;
  }),

  getAnswerHitsAndMisses: () => (answer) => {
    const hits = answer.attempts.reduce((acc, { letters }) => {
      if (letters.length) {
        const isCorrect = letters.every((l) => l.correct);
        return acc + Number(isCorrect);
      }
      return 0;
    }, 0);

    const misses = answer.attempts.length - hits;

    return { hits, misses };
  },

  getAnswersResults: (state, getters) => () => {
    const results = state.answers.reduce((acc, answer) => {
      const { hits, misses } = getters.getAnswerHitsAndMisses(answer);

      return {
        correct: acc.correct + hits,
        incorrect: acc.incorrect + misses,
      };
    },
    { correct: 0, incorrect: 0 });

    return Object.entries(results).map(([type, value]) => ({ type, value }));
  },

  getNextQuestion: (state) => (questionId) => {
    const questionIndex = state.questions.findIndex(({ id }) => id === questionId);
    const nextIndex = questionIndex + 1;
    if (!(nextIndex in state.questions)) return undefined;

    return state.questions[nextIndex];
  },

  isCorrectOrUnanswered: (state) => (questionId) => {
    const answer = state.answers.find((answer) => answer.questionId === questionId);
    if (!answer) return undefined;

    const lastAttempt = answer.attempts[answer.attempts.length - 1];
    const isUnanswered = answer.attempts.some(({ word }) => !word);
    return lastAttempt?.correct || isUnanswered;
  },

  getElapsedTime: (state) => (questionId) => {
    const currentAnswer = state.answers.find((answer) => answer.questionId === questionId);
    if (!currentAnswer) return 0;

    return currentAnswer.attempts.reduce((acc, attempt) => acc + attempt.time, 0);
  },
};

export const mutations = {
  ...make.mutations(state),

  ADD_ANSWER(state, {
    questionId, letters, time,
  }) {
    const answer = state.answers.find((answer) => answer.questionId === questionId);

    const attempt = { letters, time };
    if (!answer) {
      const newAnswer = { questionId, attempts: [attempt] };
      state.answers = [...state.answers, newAnswer];
    } else {
      answer.attempts = [...answer.attempts, attempt];
    }
  },
};

export const actions = {
  async fetchQuestions({ commit, dispatch, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.get(`/chapters/${currentChapterId}/questions`);

      const attemptData = rootGetters.getChapterAttemptData();
      if (attemptData) {
        dispatch('loadDataFromPreviousAttempt', attemptData);
      }
      commit('SET_QUESTIONS', data.questions);
      commit('SET_TIME', data.time);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async checkAnswer({
    state, commit, dispatch, rootGetters,
  }, answer) {
    let lettersStates;

    try {
      const currentChapterId = rootGetters.getChapterId();
      const endpoint = `/chapters/answer/${currentChapterId}/check`;
      const { data, error } = await axios.post(endpoint, answer);

      if (error) throw new Error();

      lettersStates = data?.letters ?? [];

      const isCorrect = data?.letters.every(({ correct }) => correct);
      const currentQuestion = state.questions.find((question) => question.id === answer.questionId);

      if (isCorrect || currentQuestion.time <= answer.time) {
        const currentAnswer = {
          questionId: answer?.questionId,
          letters: lettersStates,
          time: answer.time,
        };
        commit('ADD_ANSWER', currentAnswer);
      }

      const dataToSave = {
        answers: state.answers,
      };
      await dispatch('updateChapter', dataToSave, { root: true });
    } finally {
      commit('SET_LOADING', false);
    }

    return lettersStates;
  },

  loadDataFromPreviousAttempt({ commit }, data) {
    if (data.answers) {
      commit('SET_ANSWERS', data.answers);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

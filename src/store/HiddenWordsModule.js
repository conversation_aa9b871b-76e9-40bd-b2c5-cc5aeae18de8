/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  answers: [],
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => () => state.loading,

  getQuestions: (state) => () => state.questions,

  getNextQuestion: (state, getters) => () => {
    const question = state.questions.find((question) => !getters.isAnswered(question));
    return question;
  },

  getAnswersResults: (state) => () => {
    const results = state.answers.reduce(
      (acc, answer) => {
        const type = answer.correct ? 'correct' : 'incorrect';
        return { ...acc, [type]: acc[type] + 1 };
      },
      { correct: 0, incorrect: 0 }
    );
    return Object.entries(results).map(([type, value]) => ({ type, value })); // todo reefactor in all games
  },

  isAnswered: (state) => (question) => {
    const answer = state.answers.find((answer) => answer.questionId === question.id);
    return answer !== undefined;
  },

  isLastQuestion: (state, getters) => () => getters.getNextQuestion() === undefined,

  isCorrect: (state) => (questionId) => {
    const answer = state.answers.find((a) => a.questionId === questionId);
    return answer && answer?.correct;
  },
};

export const mutations = {
  ...make.mutations(state),

  ADD_ANSWER(state, newAnswer) {
    state.answers = [...state.answers, newAnswer];
  },
};

export const actions = {
  async fetchQuestions({ commit, dispatch, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.get(`/chapters/${currentChapterId}/questions`);

      const attemptData = rootGetters.getChapterAttemptData();
      if (attemptData) {
        dispatch('loadDataFromPreviousAttempt', attemptData);
      }

      commit('SET_QUESTIONS', data.questions);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async saveAnswer({ state, commit, dispatch, rootGetters }, answer) {
    try {
      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.post(`/chapters/answer/${currentChapterId}/check`, answer);
      const { questionId, time } = answer;

      // ! --------
      // TODO: Mover comprobacion al back
      const answerValue = answer.attempts.reduce((acc, { letter }) => `${acc}${letter}`, '');
      const currentQuestion = state.questions.find((question) => question.id === questionId);
      const word = currentQuestion.answers[0].answer
        .toLowerCase()
        .split('')
        .filter((letter, i) => !currentQuestion.clues?.includes(i) && letter !== ' ');
      const correct = word.every((letter) => answerValue.indexOf(letter) >= 0);
      // ! --------

      const currentAnswer = {
        questionId,
        time,
        correct,
        value: data.result.value,
        corrects: data.result.corrects, // TODO: Borrar en el back
        incorrects: data.result.incorrects, // TODO: Borrar en el back
      };

      commit('ADD_ANSWER', currentAnswer);

      const dataToSave = {
        answers: state.answers,
      };
      await dispatch('updateChapter', dataToSave, { root: true });
    } finally {
      commit('SET_LOADING', false);
    }
  },

  loadDataFromPreviousAttempt({ commit }, data) {
    if (data.answers) {
      commit('SET_ANSWERS', data.answers);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

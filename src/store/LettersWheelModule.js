/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  time: undefined,
  answers: [],
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => () => state.loading,

  getQuestions: (state) => state.questions,

  getQuestionsWithAnswers: (state) => {
    if (!state.questions || !state.answers) return undefined;

    const r = state.questions.map((question) => {
      const answer = state.answers.find((answer) => answer.questionId === question.id);
      return { ...question, correct: answer?.correct };
    });
    return r;
  },

  getQuestionLetter: (state) => (letter) => state.questions.find((question) => question.letter === letter),

  getFirstQuestion: (state) => () => state.questions[state.answers.length],

  getAnswersResults: (state) => () => state.questions.map((question) => {
    const currentAnswer = state.answers.find((answer) => answer.questionId === question.id);
    if (!currentAnswer) return 'unanswered';

    return currentAnswer.correct ? 'correct' : 'incorrect';
  }),

  getNextQuestion: (state) => (questionId) => {
    const currentQuestionIndex = state.questions.findIndex((question) => question.id === questionId);
    const [, ...questions] = [...state.questions.slice(currentQuestionIndex), ...state.questions.slice(0, currentQuestionIndex)];

    return questions.find((question) => !(state.answers.some((answer) => answer.questionId === question.id)));
  },

  isAnswered: (state) => (question) => {
    const answer = state.answers.find((answer) => answer.questionId === question.id);
    return (answer !== undefined);
  },

  isLastQuestion: (state, getters) => (questionId) => (getters.getNextQuestion(questionId) === undefined),

  getState: (state, getters) => (letter) => {
    const question = getters.getQuestionLetter(letter);
    if (!question) return undefined;

    const { questionId } = question;
    const answer = state.answers.find((answer) => answer.questionId === questionId);
    return answer?.state;
  },

  getGlobalTime: (state) => state.time,

  usedTime: (state) => state.answers.reduce((acc, { time }) => acc + time, 0) ?? 0,

  remainingTime: (state, getters) => state.time - getters.usedTime,
};

export const mutations = {
  ...make.mutations(state),

  ADD_ANSWER(state, newAnswer) {
    const answers = state.answers.filter((answer) => answer.questionId !== newAnswer.questionId);
    state.answers = [...answers, newAnswer];
  },
};

export const actions = {
  async fetchQuestions({ commit, dispatch, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();

      const { data } = await axios.get(`/chapters/${currentChapterId}/questions`);
      const attemptData = rootGetters.getChapterAttemptData();
      if (attemptData) {
        dispatch('loadDataFromPreviousAttempt', attemptData);
      }
      commit('SET_QUESTIONS', data.questions.sort((a, b) => a.letter.localeCompare(b.letter)));
      commit('SET_TIME', data.time);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async saveAnswer({
    state, commit, dispatch, rootGetters,
  }, { questionId, value, time }) {
    try {
      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.post(`/chapters/answer/${currentChapterId}/check`, { questionId, value, time });

      const currentAnswer = {
        questionId, time, correct: data.correct, value,
      };

      commit('ADD_ANSWER', currentAnswer);

      const dataToSave = {
        answers: state.answers,
        totalQuestions: state.questions.length,
      };
      await dispatch('updateChapter', dataToSave, { root: true });
    } finally {
      commit('SET_LOADING', false);
    }
  },

  loadDataFromPreviousAttempt({ commit }, data) {
    if (data.answers) {
      commit('SET_ANSWERS', data.answers);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

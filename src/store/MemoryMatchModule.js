/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  answers: [],
  time: undefined,
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => () => state.loading,

  getQuestion: (state) => () => state.questions,

  getGlobalTime: (state) => () => state.time,

  getAnswersResults: (state) => () => {
    const results = state.answers.reduce((acc, answer) => {
      const type = answer.correct ? 'correct' : 'incorrect';
      return { ...acc, [type]: (acc[type] + 1) };
    }, { correct: 0, incorrect: 0 });
    return Object.entries(results).map(([type, value]) => ({ type, value })); // todo reefactor in all games
  },

  getElapsedTime: (state) => () => state.answers.reduce((acc, answer) => acc + answer.time, 0),

  currentPair: (state) => state.answers?.filter(({ correct }) => correct).length,

  cardsFlipped: (state) => state.answers.reduce((acc, { cards, correct }) => ((correct) ? [...acc, ...cards] : acc), []),
};

export const mutations = {
  ...make.mutations(state),

  ADD_ANSWER(state, newAnswer) {
    state.answers = [...state.answers, newAnswer];
  },
};

export const actions = {
  async fetchQuestions({ commit, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();
      const endpoint = `/chapters/${currentChapterId}/questions`;
      const { data, error } = await axios.get(endpoint);
      if (error) throw new Error();

      commit('SET_QUESTIONS', data.questions);
      commit('SET_TIME', data.time);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async saveAnswer({
    state, commit, dispatch, rootGetters,
  }, answer) {
    let cardsMatched = false;

    try {
      const currentChapterId = rootGetters.getChapterId();
      const endpoint = `/chapters/answer/${currentChapterId}/check`;
      const { data, error } = await axios.post(endpoint, answer);

      if (error) throw new Error();

      cardsMatched = data.correct;
      const currentAnswer = {
        cards: answer.cards,
        time: answer.time,
        correct: data.correct,
      };
      commit('ADD_ANSWER', currentAnswer);

      const dataToSave = { answers: state.answers };
      await dispatch('updateChapter', dataToSave, { root: true });
    } finally {
      commit('SET_LOADING', false);
    }

    return cardsMatched;
  },

  loadDataFromPreviousAttempt({ commit }, data) {
    if (data.answers) {
      commit('SET_ANSWERS', data.answers);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

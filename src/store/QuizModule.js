/* eslint-disable max-len */
/* eslint-disable no-shadow */
import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
  loading: true,
  questions: undefined,
  time: undefined,
  answers: [],
  stage: [],
});

const state = () => getDefaultState();

const getters = {
  isLoading: (state) => () => state.loading,

  getQuestion: (state) => () => state.questions,

  getGlobalTime: (state) => () => state.time,

  getFirstQuestion: (state) => () => state.questions[state.answers.length],

  getAnswersResults: (state) => () => {
    const results = state.answers.reduce(
      (acc, answer) => {
        const type = answer.correct ? 'correct' : 'incorrect';
        return {
          ...acc,
          [type]: acc[type] + 1,
        };
      },
      { correct: 0, incorrect: 0 }
    );
    return Object.entries(results).map(([type, value]) => ({
      type,
      value,
    })); // todo reefactor in all games
  },

  getNextQuestion: (state, getters) => () => {
    const question = state.questions.find((question) => !getters.isAnswered(question));
    return question;
  },

  isAnswered: (state) => (question) => {
    const answer = state.answers.find((answer) => answer.questionId === question.id);
    return answer !== undefined;
  },

  isLastQuestion: (state, getters) => () => getters.getNextQuestion() === undefined,

  getElapsedTime: (state) => () => state.answers.reduce((acc, answer) => acc + answer.time, 0),

  isLastAnswerCorrect: (state) => () => {
    const answer = state.answers[state.answers.length - 1];

    return answer && answer?.correct;
  },
};

export const mutations = {
  ...make.mutations(state),

  ADD_ANSWER(state, newAnswer) {
    state.answers = [...state.answers, newAnswer];
  },
};

export const actions = {
  async fetchQuestions({ commit, rootGetters }) {
    try {
      commit('SET_LOADING', true);

      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.get(`/chapters/${currentChapterId}/questions`);

      commit('SET_QUESTIONS', data.questions);
      commit('SET_TIME', data.time);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async saveAnswer({ state, commit, dispatch, rootGetters }, answer) {
    try {
      const currentChapterId = rootGetters.getChapterId();
      const { data } = await axios.post(`/chapters/answer/${currentChapterId}/check`, answer);

      const { id, questionId, time } = answer;
      const currentAnswer = {
        id,
        questionId,
        time,
        correct: data.correct,
      };
      commit('ADD_ANSWER', currentAnswer);

      const dataToSave = { answers: state.answers };
      await dispatch('updateChapter', dataToSave, { root: true });
    } finally {
      commit('SET_LOADING', false);
    }
  },

  loadDataFromPreviousAttempt({ commit }, data) {
    if (data.answers) {
      commit('SET_ANSWERS', data.answers);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};

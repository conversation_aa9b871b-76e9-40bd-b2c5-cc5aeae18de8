<template>
  <div id="app">
    <router-view v-if="menuPage" />

    <PageController v-else />
  </div>
</template>

<script>
import PageController from '@/components/common/PageController';

export default {
  components: {
    PageController,
  },

  computed: {
    menuPage() {
      return this.$route.name === 'Menu';
    },
  },
};
</script>

<style lang="scss">
/* @import url('https://fonts.googleapis.com/css?family=Roboto&display=swap');
@import url('https://fonts.googleapis.com/css?family=Oxygen');
@import url('http://fonts.cdnfonts.com/css/gotham-rounded-medium'); */
@import '~@/assets/styles/_global.scss';
@import '~@/assets/styles/app.scss';

#app {
  display: flex;
  flex-direction: column;
  font-family: $font-family-primary;

  min-height: 100vh;
}
</style>

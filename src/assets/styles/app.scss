* {
  box-sizing: border-box;
}

body {
  line-height: 1.4;
}

.no-scroll {
  overflow: hidden !important;
}

.max-content-width {
  max-width: 1200px;
  margin: 0 auto !important;
  width: 100%;
}

.cover-screen {
  background: url('~@/assets/images/background/fondo-juegos.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  flex: 1;
  display: grid;
  padding: $spacing-m !important;
  place-items: center;
  position: relative;
  z-index: 1;

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    z-index: -1;
    $background-color: adjust-color($color-neutral-darkest, $alpha: -0.4);
    background: $background-color;
  }

  .panel {
    display: grid;
    padding: $spacing-l;
    max-width: 600px;
    width: 100%;
    background: #fff;
    border-radius: 5px;
    box-shadow: $shadow-elevation-2;
    box-sizing: border-box;

    img {
      max-height: clamp(150px, 20vw, 350px);
      justify-self: center;
      margin-bottom: $spacing-m;
    }
  }
}

.text-with-long-words {
  hyphens: auto;
}

@keyframes bounce {
  0% {
    transform: translateX(0px);
    timing-function: ease-in;
  }
  37% {
    transform: translateX(5px);
    timing-function: ease-out;
  }
  55% {
    transform: translateX(-5px);
    timing-function: ease-in;
  }
  73% {
    transform: translateX(4px);
    timing-function: ease-out;
  }
  82% {
    transform: translateX(-4px);
    timing-function: ease-in;
  }
  91% {
    transform: translateX(2px);
    timing-function: ease-out;
  }
  96% {
    transform: translateX(-2px);
    timing-function: ease-in;
  }
  100% {
    transform: translateX(0px);
    timing-function: ease-in;
  }
}

@keyframes correct {
  0% {
    transform: translateY(0px);
    background-color: $color-success;
  }
  50% {
    transform: translateY(-5px);
  }
  75% {
    transform: translateY(2px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes correct-transparent {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  75% {
    transform: translateY(2px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes shake {
  0% {
    transform: translate(1px, 1px) rotate(0deg);
  }
  10% {
    transform: translate(-1px, -2px) rotate(-1deg);
  }
  20% {
    transform: translate(-3px, 0px) rotate(1deg);
  }
  30% {
    transform: translate(3px, 2px) rotate(0deg);
  }
  40% {
    transform: translate(1px, -1px) rotate(1deg);
  }
  50% {
    transform: translate(-1px, 2px) rotate(-1deg);
  }
  60% {
    transform: translate(-3px, 1px) rotate(0deg);
  }
  70% {
    transform: translate(3px, 1px) rotate(-1deg);
  }
  80% {
    transform: translate(-1px, -1px) rotate(1deg);
  }
  90% {
    transform: translate(1px, 2px) rotate(0deg);
  }
  100% {
    transform: translate(1px, -2px) rotate(-1deg);
  }
}

@keyframes bigger {
  0% {
    transform: scale(1);
  }
  20% {
    transform: scale(1.1);
  }
  40% {
    transform: scale(1.2);
  }
  50% {
    transform: scale(1.2);
    color: $color-primary;
  }
  60% {
    transform: scale(1.2);
  }
  80% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes increase {
  0% {
    transform: scale(1);
  }
  40% {
    transform: scale(1.1);
  }
  60% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1.25);
  }
}

@keyframes vertical-bouncing {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  75% {
    transform: translateY(2px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes outline-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

@keyframes appear-from-top {
  0% {
    opacity: 0;
    transform: translateY(-50%);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes appear-from-bottom {
  0% {
    opacity: 0;
    transform: translateY(50%);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes appear-from-left {
  0% {
    opacity: 0;
    transform: translateX(-50%);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes appear-from-right {
  0% {
    opacity: 0;
    transform: translateX(50%);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

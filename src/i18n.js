import Vue from 'vue';
import VueI18n from 'vue-i18n';

Vue.use(VueI18n);

function loadLocaleMessages() {
  const locales = require.context('./locales', true, /[A-Za-z0-9-_,\s]+\.json$/i);
  const messages = {};
  locales.keys().forEach((key) => {
    const matched = key.match(/([A-Za-z0-9-_]+)\./i);
    if (matched && matched.length > 1) {
      const locale = matched[1];
      messages[locale] = locales(key);
    }
  });
  return messages;
}

const getUserLocale = () => {
  const userData = localStorage.getItem('user-data');
  if (!userData) return process.env.VUE_APP_I18N_LOCALE;

  return JSON.parse(userData).locale || process.env.VUE_APP_I18N_LOCALE;
};

export default new VueI18n({
  locale: getUserLocale(),
  fallbackLocale: getUserLocale(),
  messages: loadLocaleMessages(),
});

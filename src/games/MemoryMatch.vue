<template>
  <div
    v-if="questionsGame"
    class="MemoryMatch"
  >
    <GameTemplate :loading="isLoadingData">
      <template v-slot:header>
        <Header :title="$t('MEMORYMATCH.TITLE')">
          <AnswerResultsResponsive
            :answer-results="getAnswersResults()"
            :progress="{ current: currentPair, total: questions.length / 2 }"
            :isVisible="false"
          />
        </Header>
      </template>

      <template v-slot:main>
        <Board
          :start-time="time"
          :init-bonus="bonus"
          :cards="questionsGame"
          @finish-game="finishGame"
        />
      </template>
    </GameTemplate>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import GameTemplate from '@/components/common/GameTemplate';
import Header from '@/components/header/Header';
import AnswerResultsResponsive from '@/components/header/AnswerResultsResponsive';
import Board from '@/components/memoryMatch/Board';

const STORE = 'MemoryMatchModule';

export default {
  components: {
    GameTemplate,
    AnswerResultsResponsive,
    Header,
    Board,
  },

  data() {
    return {
      questionsGame: undefined,
      bonus: undefined,
    };
  },

  computed: {
    ...get(STORE, [
      'isLoading',
      'getGlobalTime',
      'getElapsedTime',
      'getAnswersResults',
    ]),

    questions: get(`${STORE}/questions`),
    cardsFlipped: get(`${STORE}/cardsFlipped`),
    currentPair: get(`${STORE}/currentPair`),

    currentQuestionIndex() {
      return this.questions?.findIndex((question) => question.id === this.currentQuestion?.id) ?? 0;
    },

    isLoadingData() {
      return this.isLoading();
    },

    time() {
      return this.getGlobalTime();
    },
  },

  async created() {
    await this.$store.dispatch('MemoryMatchModule/fetchQuestions');
    this.loadQuestionsInitialState();
    this.bonus = -this.getElapsedTime();
  },

  methods: {
    loadQuestionsInitialState() {
      this.questionsGame = this.questions.map((card) => {
        const isMatched = this.cardsFlipped.includes(card.id);
        return { ...card, isFlipped: isMatched, isMatched };
      });
    },

    finishGame() {
      this.$emit('change-page', 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.MemoryMatch {
  height: 100%;
}
</style>

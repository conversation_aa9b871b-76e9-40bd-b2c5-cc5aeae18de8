<template>
  <div class="VideoQuiz">
    <GameTemplate :loading="isLoadingData || !time">
      <template v-slot:header>
        <Header :title="$t('VIDEOQUIZ.TITLE')">
          <AnswerResultsResponsive
            :answer-results="getAnswersResults()"
            :progress="{ current: currentQuestionIndex, total: questions.length }"
            :isVisible="false"
          />
        </Header>
      </template>

      <template v-slot:main>
        <Board
          v-if="questions"
          :questions="questions"
          :progress="progress"
          :video-time="videoTime"
          :url="url"
          @loaded="videoLoaded"
        />

        <BaseModal
          v-if="modalVisible && currentQuestion"
          size="l"
        >
          <Question
            v-if="currentQuestion"
            :key="currentQuestion.id"
            :question="currentQuestion"
            :time="currentQuestion.time"
            :init-bonus="bonus"
            @finish-question="finishQuestion"
          />
        </BaseModal>
      </template>
    </GameTemplate>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import GameTemplate from '@/components/common/GameTemplate';
import Header from '@/components/header/Header';
import AnswerResultsResponsive from '@/components/header/AnswerResultsResponsive';
import Board from '@/components/videoQuiz/Board';
// import QuizModal from '@/components/videoQuiz/QuizModal';
import Question from '@/components/quiz/Question';

const STORE = 'VideoQuizModule';
export default {
  name: 'Game',
  components: {
    GameTemplate,
    AnswerResultsResponsive,
    Header,
    Board,
    // QuizModal,
    Question,
  },

  data() {
    return {
      currentQuestion: undefined,
      bonus: undefined,
      modalVisible: false,
      videoTime: 0,
      progress: undefined,
      timer: undefined,
      video: undefined,
      playing: false,
    };
  },

  computed: {
    ...get(STORE, [
      'isLoading',
      'getFirstQuestion',
      'getQuestions',
      'getGlobalTime',
      'getUrl',
      'getAnswersResults',
      'getNextQuestion',
      'getElapsedTime',
      'isLastAnswerCorrect',
    ]),

    answers: get(`${STORE}/answers`),
    lastCorrectQuestion: get(`${STORE}/lastCorrectQuestion`),

    questions() {
      return this.getQuestions();
    },

    currentQuestionIndex() {
      if (!this.currentQuestion) {
        return this.questions.length;
      }

      const questionIndex = (this.questions || []).findIndex((question) => `${question.id}` === `${this.currentQuestion?.id}`);

      return questionIndex > -1 ? questionIndex : this.questions.length;
    },

    isLoadingData() {
      return this.isLoading();
    },

    time() {
      return this.getGlobalTime();
    },

    url() {
      return this.getUrl();
    },

    answerResult() {
      return this.getAnswersResults();
    },
  },

  async created() {
    await this.$store.dispatch('VideoQuizModule/fetchQuestions');

    this.currentQuestion = this.getFirstQuestion();
    if (!this.currentQuestion) this.finishQuiz();
    this.videoTime = this.time;
  },

  methods: {
    videoLoaded(video) {
      this.video = video;
      this.startTimer();
    },

    setVideoTimeToQuestionTime() {
      this.progress = this.lastCorrectQuestion?.triggerTime ?? 0;
      this.video.player.setCurrentTime(this.progress);
    },

    isPlaying() {
      this.playing = true;
    },

    isNotPlaying() {
      this.playing = false;
    },

    startTimer() {
      this.setVideoTimeToQuestionTime();
      let lastUpdate = Date.now();
      this.timer = setInterval(() => {
        const now = Date.now();
        const deltaTime = (now - lastUpdate) / 1000;
        this.video.player.on('play', this.isPlaying);
        this.video.player.on('pause', this.isNotPlaying);
        lastUpdate = now;
        if (this.videoTime > 0 && this.playing === true) {
          this.tick(deltaTime);
          if (this.currentQuestion && this.currentQuestion?.triggerTime <= this.progress) {
            this.currentTimeValue();
          }
        }
        if (this.progress >= this.videoTime && this.currentQuestion?.triggerTime==null) {
          
          this.finishQuiz();
        }
      }, 100);
    },

    tick(deltaTime) {
      this.progress += deltaTime;
    },

    currentTimeValue() {
      this.modalVisible = true;
      this.video.pause();
    },

    async finishQuestion(answer) {
      const correct = await this.checkAnswer(answer);

      await this.addFeedBackAnimation(answer?.id);

      if (correct /* || !answer */) {
        this.nextQuestion();
      } else {
        this.setVideoTimeToQuestionTime();
      }

      this.modalVisible = false;
      this.video.play();
    },

    async addFeedBackAnimation(answerId) {
      return new Promise((resolve) => {
        if (!answerId) resolve();

        const isCorrect = this.isLastAnswerCorrect();
        const $answer = this.$el.querySelector(`#answer${answerId}.Answer`);
        if (!$answer) resolve();

        const className = isCorrect ? 'correct' : 'incorrect';
        const $question = this.$el.querySelector('.Question');

        $question.classList.add('disabled');
        $answer.classList.remove('selected');
        $answer.classList.add(className);

        setTimeout(() => {
          $answer.classList.remove(className);
          $question.classList.remove('disabled');
          resolve();
        }, 1000);
      });
    },

    async checkAnswer(newAnswer) {
      const answer = {
        ...newAnswer,
        questionId: this.currentQuestion?.id,
      };
      return this.$store.dispatch(`${STORE}/checkAnswer`, answer);
    },

    async saveAnswer(answer) {
      await this.$store.dispatch('VideoQuizModule/saveAnswer', answer);
    },

    nextQuestion() {
      this.currentQuestion = this.getNextQuestion(this.currentQuestion.id);
    },

    finishQuiz() {
      this.video.pause();
      clearInterval(this.timer);
      this.videoTime = null;
      this.$emit('change-page', 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.VideoQuiz {
  height: 100%;
}
</style>

<template>
  <div class="HiddenWords">
    <GameTemplate :loading="isLoadingData || !currentQuestion">
      <template v-slot:header>
        <Header :title="$t('HIDDENWORDS.TITLE')">
          <AnswerResultsResponsive
            :answer-results="getAnswersResults()"
            :progress="{ current: currentQuestionIndex, total: questions.length }"
          />
        </Header>
      </template>

      <template v-slot:main>
        <section>
          <Word
            v-if="currentQuestion"
            :key="currentQuestion.id"
            :question="currentQuestion"
            @finish-question="finishQuestion"
          />
        </section>
      </template>
    </GameTemplate>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import GameTemplate from '@/components/common/GameTemplate';
import AnswerResultsResponsive from '@/components/header/AnswerResultsResponsive';
import Header from '@/components/header/Header';
import Word from '@/components/hiddenWords/Word';

const STORE = 'HiddenWordsModule';
export default {
  components: {
    GameTemplate,
    AnswerResultsResponsive,
    Header,
    Word,
  },

  data() {
    return {
      currentQuestion: undefined,
      isChecking: false
    };
  },

  computed: {
    ...get(STORE, ['isLoading', 'getQuestions', 'getNextQuestion', 'isLastQuestion', 'getAnswersResults', 'isCorrect']),

    questions: get(`${STORE}/questions`),

    currentQuestionIndex() {
      const index = this.questions?.findIndex((question) => question.id === this.currentQuestion?.id) ?? 0;

      return index < 0 ? this.questions.length : index;
    },

    isLoadingData() {
      return this.isLoading();
    },
  },

  async created() {
    await this.$store.dispatch(`${STORE}/fetchQuestions`);
    this.nextQuestion();
  },

  methods: {
    async finishQuestion(answer) {
      await this.saveAnswer(answer);
      await this.addFeedBackAnimation(answer?.questionId);
      this.nextQuestion();
    },
    async saveAnswer(answer) {
      if (this.isChecking) return;
      this.isChecking = true;
      await this.$store.dispatch(`${STORE}/saveAnswer`, answer);
      this.isChecking = false;
    },

    async addFeedBackAnimation(questionId) {
      return new Promise((resolve) => {
        if (!questionId) resolve();

        const isCorrect = this.isCorrect(questionId);
        const $answer = this.$el.querySelector('.statement');
        if (!$answer) resolve();

        const className = isCorrect ? 'correct' : 'incorrect';
        const $question = this.$el.querySelector('.letters');

        $question.classList.add('disabled');
        $answer.classList.remove('selected');
        $answer.classList.add(className);
        setTimeout(() => {
          $answer.classList.remove(className);
          $question.classList.remove('disabled');
          resolve();
        }, 1000);
      });
    },

    nextQuestion() {
      this.currentQuestion = this.getNextQuestion();
      if (!this.currentQuestion) {
        this.finishHiddenWords();
      }
    },

    finishHiddenWords() {
      this.$emit('change-page', 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.HiddenWords {
  height: 100%;
}
</style>

<template>
  <div
    v-if="currentQuestion"
    class="HiddenPic"
  >
    <GameTemplate :loading="loading">
      <template v-slot:header>
        <Header title="Adivinanza">
          <AnswerResultsResponsive
            :answer-results="getAnswersResults()"
            :progress="{ current: currentQuestionIndex, total: questions.length }"
          />
        </Header>
      </template>

      <template v-slot:main>
        <Question
          v-if="currentQuestion"
          :key="currentQuestion.id"
          :question="currentQuestion"
          :is-correct="currentQuestionCorrect"
          :disabled="checkingAnswer"
          @finish-question="saveAnswer"
        />
      </template>
    </GameTemplate>
  </div>
</template>

<script>
import { get, call } from 'vuex-pathify';
import GameTemplate from '@/components/common/GameTemplate';
import Header from '@/components/header/Header';
import AnswerResultsResponsive from '@/components/header/AnswerResultsResponsive';
import Question from '@/components/hiddenPic/Question';

const STORE = 'HiddenPicModule';

export default {
  components: {
    GameTemplate,
    AnswerResultsResponsive,
    Header,
    Question,
  },

  data() {
    return {
      currentQuestion: undefined,
      currentQuestionCorrect: undefined,
      checkingAnswer: false
    };
  },

  computed: {
    ...get(STORE, [
      'getFirstQuestion',
      'getAnswersResults',
      'isLastQuestion',
      'getElapsedTime',
      'getNextQuestion',
      'isCorrect',
    ]),

    loading: get(`${STORE}/loading`),
    // fails: get(`${STORE}/fails`),
    questions: get(`${STORE}/questions`),

    currentQuestionIndex() {
      return this.questions?.findIndex((question) => question.id === this.currentQuestion?.id) ?? 0;
    },
  },

  async created() {
    await this.storeLoadQuestions();

    this.currentQuestion = this.getFirstQuestion();
    if (!this.currentQuestion) this.finishGame();
  },

  methods: {
    ...call(STORE, {
      storeLoadQuestions: 'fetchQuestions',
      storeCheckAnswer: 'saveAnswer',
    }),

    async saveAnswer(answer) {
      if (!this.checkingAnswer) {
        this.checkingAnswer = true;
        await this.storeCheckAnswer(answer);
        this.checkingAnswer = false;
        this.currentQuestionCorrect = this.isCorrect(this.currentQuestion.id);

        setTimeout(() => {
          if (!answer.value || this.currentQuestionCorrect) {
            this.loadNextQuestion();
          }
          this.currentQuestionCorrect = undefined;
        }, 1200);
      }
    },

    loadNextQuestion() {
      const nextQuestion = this.getNextQuestion(this.currentQuestion?.id);
      if (!nextQuestion) {
        this.finishGame();
      } else {
        this.currentQuestion = nextQuestion;
      }
    },

    finishGame() {
      this.$emit('change-page', 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.HiddenPic {
  height: 100%;

  ::v-deep {
    .content {
      width: calc(100% - 1rem);
      display: grid;
      grid-template-rows: auto 1fr auto auto;
      gap: 1rem;
      max-height: calc(100vh - 130px);

      canvas {
        height: 100%;
        min-height: 300px;
      }

      .words .word .letter input {
        width: 100%;
        height: auto;
        max-height: 35px;
        aspect-ratio: 1/1;
        font-size: clamp(1rem, 2.5vw, 1.5rem);
        min-width: 40px;
      }
    }
  }
}
</style>

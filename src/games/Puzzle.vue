<template>
  <div class="Puzzle">
    <GameTemplate :loading="isLoading">
      <template v-slot:header>
        <Header :title="$t('PUZZLE.TITLE')">
          <AnswerResultsResponsive :answer-results="getAnswersResults()" :isVisible="false" />
        </Header>
      </template>

      <template v-slot:main>
        <div class="content">
          <MultiTimer
            v-if="isBoardLoaded"
            :timers="timers"
            @current-time="saveTime"
            @timer-controller="timerController"
          />

          <Board
            v-if="getCurrentImage"
            :image="getCurrentImage"
            :difficulty="0"
            @start="start"
            @finish="finishBoard"
            @loaded="loadedBoard"
          />

          <BaseModal
            v-if="isModalVisible"
            size="l"
          >
            <Question
              v-if="currentQuestion"
              :key="currentQuestion.id"
              :question="currentQuestion"
              :time="currentQuestion.time"
              @finish-question="finishQuestion"
            />
          </BaseModal>
        </div>
      </template>
    </GameTemplate>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';

import GameTemplate from '@/components/common/GameTemplate';
import Header from '@/components/header/Header';
import Board from '@/components/puzzle/Board';
import Question from '@/components/quiz/Question';
import MultiTimer from '@/components/common/MultiTimer';
import AnswerResultsResponsive from '@/components/header/AnswerResultsResponsive';

const STORE = 'PuzzleModule';
export default {
  components: {
    GameTemplate,
    Header,
    AnswerResultsResponsive,
    Board,
    Question,
    MultiTimer,
  },

  data() {
    return {
      timers: undefined,
      times: undefined,
      remainingTime: undefined,
      currentTimerActive: undefined,
      isModalVisible: false,
      currentQuestion: undefined,
      currentTimerTime: 0,
      isBoardLoaded: false,
    };
  },

  computed: {
    ...get(STORE, [
      'isLoading',
      'getAnswersResults',
      'getTimes',
      'getImage',
      'getRandomQuestion',
      'isLastAnswerCorrect',
    ]),

    getCurrentImage() {
      return this.getImage();
    },
  },

  async created() {
    await this.$store.dispatch(`${STORE}/fetchQuestions`);

    this.times = this.getTimes();
    this.timers = this.times.map((time) => ({ time, bonus: undefined, state: 'stopped' }));
    this.currentTimerActive = this.timers.length - 1;
  },

  methods: {
    calculeRemainingTime(currentTimerIsFinish) {
      const remainingTimes = this.times.filter((time, i) => {
        const isFinished = currentTimerIsFinish ? i < this.currentTimerActive : i <= this.currentTimerActive;
        return isFinished;
      });
      const remainingTime = remainingTimes.reduce((total, time) => total + time, 0);

      return remainingTime ?? 0;
    },

    start() {
      this.timers[this.currentTimerActive].state = 'discounting';
    },

    async finishQuestion(answer) {
      const correct = await this.saveAnswer(answer);

      await this.addFeedBackAnimation(answer?.id);

      this.closeQuestion();

      if (correct) {
        this.reloadCurrentTimer();
      } else {
        this.nextTimer();
      }
    },

    async addFeedBackAnimation(answerId) {
      return new Promise((resolve) => {
        if (!answerId) resolve();

        const isCorrect = this.isLastAnswerCorrect();
        const $answer = this.$el.querySelector(`#answer${answerId}.Answer`);
        if (!$answer) resolve();

        const className = isCorrect ? 'correct' : 'incorrect';
        const $question = this.$el.querySelector('.Question');

        $question.classList.add('disabled');
        $answer.classList.remove('selected');
        $answer.classList.add(className);

        setTimeout(() => {
          $answer.classList.remove(className);
          $question.classList.remove('disabled');
          resolve();
        }, 1000);
      });
    },

    async saveAnswer(answer) {
      const remainingTime = this.calculeRemainingTime(true);
      return this.$store.dispatch(`${STORE}/saveAnswer`, { answer, remainingTime });
    },

    timerController(data) {
      if (data.state) {
        this.timers[this.currentTimerActive].state = data.state;

        if (this.timers[this.currentTimerActive].state === 'stopped') {
          this.nextQuestion();
        }
      }

      if (data.bonus) {
        this.timers[this.currentTimerActive].bonus = data.bonus;
      }
    },

    nextQuestion() {
      this.currentQuestion = this.getRandomQuestion();
      this.openQuestion();
    },

    openQuestion() {
      this.isModalVisible = true;
    },

    closeQuestion() {
      this.isModalVisible = false;
      this.currentQuestion = undefined;
    },

    reloadCurrentTimer() {
      this.timers[this.currentTimerActive].state = 'discounting';
    },

    nextTimer() {
      this.currentTimerActive -= 1;
      if (this.currentTimerActive < 0) {
        this.finishPuzzle();
      } else {
        this.timers[this.currentTimerActive].state = 'discounting';
      }
    },

    saveTime(currentTime) {
      this.currentTimerTime = currentTime;
      this.finishPuzzle();
    },

    finishBoard() {
      this.timers[this.currentTimerActive].state = 'paused';
    },

    loadedBoard() {
      this.isBoardLoaded = true;
    },

    async finishPuzzle() {
      const remainingTime = this.calculeRemainingTime(true) + this.currentTimerTime;
      await this.$store.dispatch(`${STORE}/saveRemainingTime`, remainingTime);
      this.$emit('change-page', 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.Puzzle {
  height: 100%;

  .content {
    display: grid;
    align-content: flex-start;
    gap: $spacing-xl;
    box-sizing: border-box;
  }

  .MultiTimer {
    // max-width: 800px;
    width: 100%;
    margin: 0 auto;
  }
}
</style>

<template>
  <div class="DoubleOrNothing">
    <GameTemplate :loading="isLoadingData && !!maxScore">
      <template v-slot:header>
        <Header :title="$t('X2.TITLE')" />
      </template>

      <template v-slot:main>
        <Question
          v-if="currentQuestion"
          :key="currentQuestion.id"
          :question="currentQuestion"
          :time="time"
          :init-bonus="bonus"
          @finish-question="finishQuestion"
        >
          <ScoreTable
            :results="getAnswersResults()"
            :max-score="maxScore"
          />
        </Question>

        <BaseModal v-if="isModalVisible">
          <component
            :is="currentPanel"
            v-if="currentPanel"
            @modal-answer="modalAnswer"
          />
        </BaseModal>
      </template>
    </GameTemplate>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import GameTemplate from '@/components/common/GameTemplate';
import Header from '@/components/header/Header';
import Question from '@/components/quiz/Question';
import AttemptPanel from '@/components/doubleornothing/AttemptPanel';
import FailPanel from '@/components/doubleornothing/FailPanel';
import ScoreTable from '@/components/doubleornothing/ScoreTable';

export default {
  name: 'DoubleOrNothing',
  components: {
    GameTemplate,
    Header,
    Question,
    AttemptPanel,
    FailPanel,
    ScoreTable,
  },

  data() {
    return {
      currentQuestion: undefined,
      bonus: undefined,
      isModalVisible: false,
    };
  },

  computed: {
    ...get('DoubleOrNothingModule', [
      'isLoading',
      'getRandomQuestion',
      'isLastQuestion',
      'getAnswersResults',
      'isQuestionAnsweredCorrectly',
      'getElapsedTime',
      'maxScore',
      'isLastAnswerCorrect',
    ]),
    ...get('', ['getMaxPoints']),

    time() {
      return this.currentQuestion?.time;
    },

    maxScore() {
      return this.getMaxPoints();
    },

    isLoadingData() {
      return this.isLoading();
    },

    currentPanel() {
      const currentQuestionId = this.currentQuestion?.id;
      const isCorrect = this.isQuestionAnsweredCorrectly(currentQuestionId);
      return isCorrect ? 'AttemptPanel' : 'FailPanel';
    },
  },

  async created() {
    await this.$store.dispatch('DoubleOrNothingModule/fetchQuestions');
    this.currentQuestion = this.getRandomQuestion();
    this.bonus = -this.getElapsedTime();

    if (this.isLastQuestion()) {
      this.finishDoubleOrNothig();
    }
  },

  methods: {
    modalAnswer(attempt) {
      this.isModalVisible = false;
      if (attempt) {
        this.nextQuestion();
      } else {
        this.finishDoubleOrNothig();
      }
    },

    async finishQuestion(answer) {
      await this.saveAnswer(answer);

      await this.addFeedBackAnimation(answer?.id);

      if (this.isQuestionAnsweredCorrectly(this.currentQuestion.id)) {
        await this.showPoints();
      }

      if (this.isLastQuestion()) {
        this.finishDoubleOrNothig();
      } else {
        this.isModalVisible = true;
      }
    },

    async addFeedBackAnimation(answerId) {
      return new Promise((resolve) => {
        if (!answerId) resolve();

        const isCorrect = this.isLastAnswerCorrect();
        const $answer = this.$el.querySelector(`#answer${answerId}.Answer`);
        if (!$answer) resolve();

        const className = isCorrect ? 'correct' : 'incorrect';
        const $question = this.$el.querySelector('.Question');

        $question.classList.add('disabled');
        $answer.classList.remove('selected');
        $answer.classList.add(className);

        setTimeout(() => {
          $answer.classList.remove(className);
          $question.classList.remove('disabled');
          resolve();
        }, 1000);
      });
    },

    async showPoints() {
      return new Promise((resolve) => {
        const corrects = this.$el.querySelectorAll('.ScoreTable .score.type--correct');
        if (corrects.length <= 0) resolve();

        const lastCorrect = corrects[corrects.length - 1];
        const points = lastCorrect.querySelector('.points');
        points.classList.add('correct');

        setTimeout(() => {
          points.classList.remove('correct');
          resolve();
        }, 800);
      });
    },

    async saveAnswer(answer) {
      await this.$store.dispatch('DoubleOrNothingModule/saveAnswer', answer);
    },

    nextQuestion() {
      const isCorrect = this.isLastAnswerCorrect();
      if (this.isLastQuestion() || !isCorrect) {
        this.finishDoubleOrNothig();
      } else {
        this.currentQuestion = this.getRandomQuestion();
      }
    },

    finishDoubleOrNothig() {
      this.$emit('change-page', 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.DoubleOrNothing {
  height: 100%;
}
</style>

<template>
  <div class="Wordle">
    <GameTemplate :loading="isLoadingData">
      <template v-slot:header>
        <Header :title="$t('WORDLE.TITLE')">
          <AnswerResultsResponsive
            :answer-results="getAnswersResults()"
            :progress="{current: currentQuestionIndex, total: questions.length}"
          />
        </Header>
      </template>

      <template v-slot:main>
        <Board
          v-if="currentQuestion"
          :key="currentQuestion.id"
          :init-bonus="bonus"
          :question="currentQuestion"
          :fail="fail"
          @next-question="nextQuestion"
        />
      </template>
    </GameTemplate>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import GameTemplate from '@/components/common/GameTemplate';
import Header from '@/components/header/Header';
import AnswerResultsResponsive from '@/components/header/AnswerResultsResponsive';
import Board from '@/components/wordle/Board';

const STORE = 'WordleModule';

export default {
  name: 'Game',
  components: {
    GameTemplate,
    Header,
    AnswerResultsResponsive,
    Board,
  },

  data() {
    return {
      currentQuestion: undefined,
      bonus: undefined,
      fail: false,
    };
  },

  computed: {
    ...get(STORE, [
      'isLoading',
      'getGlobalTime',
      'getFirstQuestion',
      'getAnswersResults',
      'getNextQuestion',
      'getElapsedTime',
    ]),

    questions: get(`${STORE}/questions`),

    currentQuestionIndex() {
      return this.questions?.findIndex((question) => question.id === this.currentQuestion?.id) ?? 0;
    },

    isLoadingData() {
      return this.isLoading();
    },
  },

  async created() {
    await this.$store.dispatch(`${STORE}/fetchQuestions`);

    this.currentQuestion = this.getFirstQuestion();
    if (!this.currentQuestion) {
      this.finishGame();
    } else {
      this.bonus = -this.getElapsedTime(this.currentQuestion.id);
    }
  },

  methods: {
    nextQuestion() {
      const question = this.getNextQuestion(this.currentQuestion.id);
      if (!question) this.finishGame();

      this.currentQuestion = question;
    },

    finishGame() {
      this.$emit('change-page', 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.Wordle {
  height: 100%;
}
</style>

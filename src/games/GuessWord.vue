<template>
  <div class="GuessWord">
    <GameTemplate :loading="isLoadingData">
      <template v-slot:header>
        <Header :title="$t('GUESSWORD.TITLE')">
          <AnswerResultsResponsive
            :answer-results="getAnswersResults()"
            :progress="{ current: currentQuestionIndex, total: questions.length }"
          />
        </Header>
      </template>

      <template v-slot:main>
        <Board
          v-if="currentQuestion"
          :key="currentQuestion.id"
          :init-bonus="bonus"
          :question="currentQuestion"
          :fail="fail"
          @save-answer="checkAnswer"
        />
      </template>
    </GameTemplate>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import GameTemplate from '@/components/common/GameTemplate';
import Header from '@/components/header/Header';
import AnswerResultsResponsive from '@/components/header/AnswerResultsResponsive';
import Board from '@/components/guessword/Board';

const STORE = 'GuessWordModule';

export default {
  components: {
    GameTemplate,
    Header,
    AnswerResultsResponsive,
    Board,
  },

  data() {
    return {
      // answersArray: undefined,
      currentQuestion: undefined,
      bonus: undefined,
      fail: false,
    };
  },

  computed: {
    ...get(STORE, [
      'isLoading',
      'getGlobalTime',
      'getFirstQuestion',
      'getAnswersResults',
      'getNextQuestion',
      'getElapsedTime',
    ]),

    questions: get(`${STORE}/questions`),

    currentQuestionIndex() {
      return this.questions?.findIndex((question) => question.id === this.currentQuestion?.id) ?? 0;
    },

    isLoadingData() {
      return this.isLoading();
    },
  },

  async created() {
    await this.$store.dispatch(`${STORE}/fetchQuestions`);

    this.currentQuestion = this.getFirstQuestion();
    if (!this.currentQuestion) {
      this.finishGame();
    } else {
      this.bonus = -this.getElapsedTime(this.currentQuestion.id);
    }
  },

  methods: {
    async checkAnswer(newAnswer) {
      const answer = {
        ...newAnswer, questionId: this.currentQuestion?.id,
      };
      const correct = await this.$store.dispatch(`${STORE}/checkAnswer`, answer);
      await this.addLettersAnimation(correct);
      if (correct || !newAnswer?.word) {
        this.nextQuestion();
      } else {
        this.fail = true;
        setTimeout(() => { this.fail = false; }, 100);
      }
    },

    addLettersAnimation(correct) {
      const letters = document.querySelectorAll('.Letters .letter');
      const className = correct ? 'correct' : 'shake';
      const animationDuration = 0.7;
      let delay = 0;

      const timeOut = () => (delay + animationDuration) * 1000;

      for (let i = 0; i < letters.length; i += 1) {
        const letter = Array.from(letters)[i];
        letter.classList.add(className);
        letter.style['animation-delay'] = `${delay}s`;
        delay += 0.1;

        setTimeout(() => {
          letter.classList.remove(className);
        }, timeOut());
      }

      return new Promise((resolve) => {
        setTimeout(() => {
          resolve();
        }, timeOut());
      });
    },

    nextQuestion() {
      const question = this.getNextQuestion(this.currentQuestion.id);
      if (!question) this.finishGame();

      this.currentQuestion = question;
    },

    finishGame() {
      this.$emit('change-page', 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.GuessWord {
  height: 100%;
  user-select: none;
}
</style>

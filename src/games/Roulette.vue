<template>
  <div class="Roulette">
    <GameTemplate :loading="false">
      <template v-slot:header>
        <Header :title="$t('ROULETTE.TITLE')">
          <AnswerResultsResponsive :answer-results="getAnswersResults()" :isVisible="false" />
        </Header>
      </template>

      <template v-slot:main>
        <section v-show="!isAnswering">
          <TransitionSlide side="right">
            <wheel
              :sections="sections"
              @finish-spinnig="finishSpinnig"
            />
          </TransitionSlide>
        </section>

        <div
          v-if="isAnswering"
          class="question-view"
        >
          <Question
            v-if="currentQuestion"
            :question="currentQuestion"
            :time="time"
            @finish-question="finishQuestion"
          />
        </div>
      </template>
    </GameTemplate>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import GameTemplate from '@/components/common/GameTemplate';
import Header from '@/components/header/Header';
import AnswerResultsResponsive from '@/components/header/AnswerResultsResponsive';
import Wheel from '@/components/roulette/Wheel';
import Question from '@/components/quiz/Question';

import TransitionSlide from '@/transitions/TransitionSlide';

const STORE = 'RouletteModule';

export default {
  components: {
    GameTemplate,
    Header,
    AnswerResultsResponsive,
    Wheel,
    Question,
    TransitionSlide,
  },

  data() {
    return {
      question: undefined,
      currentSection: undefined,
      answering: false,
    };
  },

  computed: {
    ...get(STORE, ['getSectionStates', 'getAnswersResults', 'getRandomQuestion', 'isCorrect', 'isLastAnswerCorrect']),

    questions: get(`${STORE}/questions`),

    time() {
      return this.question?.time;
    },

    isAnswering() {
      return this.answering;
    },

    sections() {
      return this.getSectionStates();
    },

    currentQuestion() {
      return this.question;
    },
  },

  async created() {
    await this.$store.dispatch(`${STORE}/fetchQuestions`);
  },

  methods: {
    finishSpinnig(sectionIndex) {
      this.currentSection = sectionIndex;
      this.loadQuestion();
    },

    loadQuestion() {
      this.question = this.getRandomQuestion();
      this.answering = true;
    },

    async finishQuestion(answer) {
      await this.saveAnswer(answer);

      await this.addFeedBackAnimation(answer?.id);

      this.nextQuestion();
    },

    async addFeedBackAnimation(answerId) {
      return new Promise((resolve) => {
        if (!answerId) resolve();

        const isCorrect = this.isLastAnswerCorrect();
        const $answer = this.$el.querySelector(`#answer${answerId}.Answer`);
        if (!$answer) resolve();

        const className = isCorrect ? 'correct' : 'incorrect';
        const $question = this.$el.querySelector('.Question');

        $question.classList.add('disabled');
        $answer.classList.remove('selected');
        $answer.classList.add(className);

        setTimeout(() => {
          $answer.classList.remove(className);
          $question.classList.remove('disabled');
          resolve();
        }, 1000);
      });
    },

    async saveAnswer(answer) {
      await this.$store.dispatch(`${STORE}/saveAnswer`, { answer, section: this.currentSection });
    },

    nextQuestion() {
      if (this.areAllSectionsCompleted()) {
        this.finnishRoulette();
      } else {
        this.answering = false;
      }
    },

    areAllSectionsCompleted() {
      return this.sections.every((section) => section);
    },

    finnishRoulette() {
      this.$emit('change-page', 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.Roulette {
  height: 100%;

  section {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    min-height: 400px;
  }
}
</style>

<template>
  <div class="HigherLower">
    <GameTemplate :loading="isLoadingData">
      <template v-slot:header>
        <Header :title="$t('HIGHERLOWER.TITLE')">
          <AnswerResultsResponsive
            :answer-results="getAnswersResults()"
            :progress="{ current: currentQuestionIndex, total: questions.length }"
          />
        </Header>
      </template>

      <template v-slot:main>
        <Board
          v-if="currentQuestion"
          :key="currentQuestion.id"
          :question="currentQuestion"
          :init-bonus="bonus"
          @next-question="nextQuestion"
        />
      </template>
    </GameTemplate>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import GameTemplate from '@/components/common/GameTemplate';
import Header from '@/components/header/Header';
import AnswerResultsResponsive from '@/components/header/AnswerResultsResponsive';
import Board from '@/components/higherLower/Board';

const STORE = 'HigherLowerModule';

export default {
  name: 'Game',
  components: {
    GameTemplate,
    Header,
    AnswerResultsResponsive,
    Board,
  },

  data() {
    return {
      bonus: undefined,
      currentQuestion: undefined,
    };
  },

  computed: {
    ...get(STORE, ['isLoading', 'getAnswersResults', 'getElapsedTime', 'getFirstAvailableQuestion', 'getNextQuestion']),
    firstAvailableQuestion: get(`${STORE}/firstAvailableQuestion`),
    questions: get(`${STORE}/questions`),
    currentQuestionIndex() {
      return this.questions?.findIndex((question) => question.id === this.currentQuestion?.id) ?? 0;
    },
    isLoadingData() {
      return this.isLoading();
    },
  },

  async created() {
    await this.$store.dispatch('HigherLowerModule/fetchQuestions');
    this.currentQuestion = this.getFirstAvailableQuestion();
    this.bonus = -this.getElapsedTime(this.currentQuestion.id);
  },

  methods: {
    nextQuestion() {
      const currentQuestion = this.getNextQuestion();
      this.currentQuestion = currentQuestion;
      this.bonus = 0;
      if (!this.currentQuestion) {
        this.finishGame();
      }
    },

    finishGame() {
      this.$emit('change-page', 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.HigherLower {
  height: 100%;
}
</style>

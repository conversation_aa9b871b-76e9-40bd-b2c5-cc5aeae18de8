<template>
  <div class="LettersWheel">
    <GameTemplate :loading="isLoadingData">
      <template v-slot:header>
        <Header :title="$t('LETTERSWHEEL.TITLE')" />
      </template>

      <template v-slot:main>
        <div class="main-content max-content-width">
          <div class="wheel-content">
            <Wheel
              v-if="questions"
              :questions="questions"
              :current-question="currentQuestion"
              :global-time="globalTime"
              :used-time="usedTime"
              :style="transform"
              :finish="finish"
              :timer-stopped="timerStopped"
              @current-time="currentTimer"
              @finish="finishTimer"
            />
          </div>

          <Panel
            :current-question="currentQuestion"
            :questions="questions"
            @next-question="nextQuestion"
            @save-answer="saveAnswer"
          />
        </div>
      </template>
    </GameTemplate>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import GameTemplate from '@/components/common/GameTemplate';
import Header from '@/components/header/Header';
import Wheel from '@/components/lettersWheel/Wheel';
import Panel from '@/components/lettersWheel/Panel';

const STORE = 'LettersWheelModule';

export default {
  components: {
    GameTemplate,
    Header,
    Wheel,
    Panel,
  },

  data() {
    return {
      currentQuestion: undefined,
      originalChildSize: undefined,
      originalParentSize: undefined,
      transform: undefined,
      time: undefined,
      currentTime: undefined,
      finish: false,
      timerStopped: false,
    };
  },

  computed: {
    ...get(STORE, [
      'isLoading',
      'getState',
      'getGlobalTime',
      'getFirstQuestion',
      'getAnswersResults',
      'getNextQuestion',
      'isLastQuestion',
    ]),

    globalTime: get(`${STORE}/time`),
    questions: get(`${STORE}/getQuestionsWithAnswers`),
    remainingTime: get(`${STORE}/remainingTime`),
    usedTime: get(`${STORE}/usedTime`),

    isLoadingData() {
      return this.isLoading();
    },
  },

  // watch: {
  //   currentQuestion(newValue, oldValue) {
  //     if (!oldValue && newValue) {
  //       const $wheel = this.$el.querySelector('.Wheel');
  //       const $panel = this.$el.querySelector('.Panel');
  //       if ($wheel && $panel) {
  //         const { height, y } = $wheel.getBoundingClientRect();
  //         $panel.style.top = `${height + y}px`;
  //       }
  //     }
  //   },
  // },

  async created() {
    await this.$store.dispatch('LettersWheelModule/fetchQuestions');
    this.currentQuestion = this.getFirstQuestion();
    if (!this.currentQuestion) this.finnishWheel();
  },

  methods: {
    nextQuestion() {
      const questionId = this.currentQuestion.id;
      if (this.isLastQuestion(questionId)) {
        if (this.questions.filter((letter) => letter.correct === undefined).length) return;
        this.finnishWheel();
      } else {
        this.currentQuestion = this.getNextQuestion(questionId);
      }
    },

    currentTimer(value) {
      this.currentTime = value - 1;
    },

    async saveAnswer(value) {
      this.timerStopped = true;

      const time = this.remainingTime - this.currentTime;
      const currentAnswer = {
        questionId: this.currentQuestion.id,
        value,
        time,
      };

      await this.$store.dispatch('LettersWheelModule/saveAnswer', currentAnswer);

      this.timerStopped = false;
      this.nextQuestion();
    },

    async finishTimer() {
      await this.saveAnswer();

      this.finnishWheel();
    },

    finnishWheel() {
      this.finish = true;

      this.$emit('change-page', 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.LettersWheel {
  height: 100%;

  .main-content {
    display: grid;
    grid-template-columns: repeat(2, auto);
    justify-content: center;
    align-items: center;
    gap: $spacing-4xl;
    padding: $spacing-m;
    height: 100%;
    position: relative;

    .wheel-content {
      overflow: visible;
      width: 100%;
    }

    .Wheel {
      justify-self: center;
      transition: all 0.2s ease;
      transform: scale(1);
    }

    @media #{$breakpoint-xl-max} {
      gap: $spacing-xl;

      .Wheel {
        transform: scale(0.8);
      }
    }

    @media #{$breakpoint-lg-max} {
      gap: 0;

      .Wheel {
        transform: scale(0.6);
      }
    }

    @media #{$breakpoint-md-max} {
      grid-template-columns: 100%;
      justify-items: center;

      .wheel-content {
        overflow: hidden;
      }

      .Wheel {
        transform: scale(0.5) translate(0, -30%);
        position: absolute;
        top: 0;
        left: $spacing-xs;
        right: $spacing-xs;
      }
    }

    @media #{$breakpoint-sm-max} {
      .Wheel {
        transform: scale(0.4) translate(0, -50%);
      }

      .Panel {
        position: absolute;
        left: $spacing-m;
        right: $spacing-m;
        bottom: $spacing-xl;
        width: auto;
      }
    }
  }
}
</style>

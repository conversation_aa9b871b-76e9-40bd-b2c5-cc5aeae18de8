<template>
  <div
    v-if="questions"
    class="FillGaps"
  >
    <GameTemplate :loading="isLoadingData || !time">
      <template v-slot:header>
        <Header :title="$t('FILLTHEGAPS.TITLE')">
          <AnswerResultsResponsive
            :answer-results="getAnswersResults()"
            :progress="{ current: currentQuestionIndex, total: questions.length }"
          />
        </Header>

        <Timer
          v-if="time"
          :key="timerKey"
          class="timer"
          :time="time"
          :state="timer.state"
          :bonus="timer.bonus"
          @current-time="saveTime"
          @timer-controller="timerController"
        />
      </template>

      <template v-slot:main>
        <div class="main-container max-content-width">
          <TransitionSlide side="top">
            <div
              v-if="currentQuestion && currentQuestionblocks"
              :key="currentQuestion.id"
              class="text-container"
            >
              <div
                v-for="block in currentQuestionblocks"
                :key="block.id"
                class="text"
              >
                <component
                  :is="getGapType(block.component)"
                  v-if="block.isGapComponent"
                  :key="reset"
                  :options="currentQuestionOptions"
                  :class="['gap', block.state]"
                  @input="saveAnswer({ word: $event, id: block.id })"
                />

                <div
                  v-else
                  class="simple-text"
                  v-html="block.text"
                />
              </div>
            </div>
          </TransitionSlide>

          <TransitionFade>
            <div
              v-if="!isMobile && isDragType"
              class="options-container"
            >
              <PanelOptions
                v-for="(currentQuestionOption, i) in currentQuestionOptions"
                id="Panel"
                :key="i"
                :option="currentQuestionOption"
              />
            </div>
          </TransitionFade>

          <div class="button-container">
            <BaseButton
              variation="secondary"
              @click="resetData"
            >
              <font-awesome-icon icon="fa-solid fa-rotate-right" />
              {{ $t('FILLTHEGAPS.RESETBUTTON') }}
            </BaseButton>

            <TransitionSlide side="bottom">
              <BaseButton
                v-if="isFilled"
                @click="confirm"
              >
                {{ $t('CONFIRM.BUTTON') }}
                <font-awesome-icon icon="fa-solid fa-angle-right" />
              </BaseButton>
            </TransitionSlide>
          </div>
        </div>
      </template>
    </GameTemplate>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import GameTemplate from '@/components/common/GameTemplate';
import Header from '@/components/header/Header';
import AnswerResultsResponsive from '@/components/header/AnswerResultsResponsive';
import Timer from '@/components/common/Timer';
import DeepContent from '@/components/common/DeepContent';
import Select from '@/components/fillGaps/Select';
import PanelOptions from '@/components/fillGaps/PanelOptions';
import Draggable from '@/components/fillGaps/Draggable';
import TransitionFade from '@/transitions/TransitionFade';
import TransitionSlide from '@/transitions/TransitionSlide';

const BLOCK_STATES = {
  INCORRECT: 'incorrect',
  CORRECT: 'correct',
  DEFAULT: 'default',
};

const COMPONETS = {
  drag: 'Draggable',
  select: 'Select',
};

const MOBILE_BREAKPOINT = 800; // 586 left tablets out

export default {
  components: {
    TransitionFade,
    TransitionSlide,
    GameTemplate,
    Header,
    AnswerResultsResponsive,
    Timer,
    DeepContent,
    Select,
    PanelOptions,
    Draggable,
  },

  data() {
    return {
      timer: {
        state: 'stopped',
        bonus: 0,
      },
      currentAttempts: [],
      currentFails: [],
      elapsedTime: 0,
      selected: undefined,
      currentQuestionblocks: [],
      reset: 0,
      isMobile: undefined,
      results: {
        correct: 0,
        incorrect: 0,
      },
    };
  },

  computed: {
    ...get('FillGapsModule', ['isLoading', 'getGlobalTime', 'getAnswersResults', 'getElapsedTime', 'getNextQuestion']),

    currentText: get('FillGapsModule/currentQuestion@text'),
    currentQuestion: get('FillGapsModule/currentQuestion'),
    questions: get('FillGapsModule/questions'),
    currentQuestionIndex: get('FillGapsModule/currentQuestionIndex'),
    fails: get('FillGapsModule/fails'),
    gapsLength: get('FillGapsModule/getQuestionGaps'),

    isLoadingData() {
      return this.isLoading();
    },

    time() {
      return this.getGlobalTime();
    },

    timerKey() {
      return `timer_${this.currentQuestion.id}`;
    },

    currentQuestionOptions() {
      const options = this.currentQuestion?.answers;
      // if (!this.currentQuestion?.random) return options;

      return this.shuffle([...options]);
    },

    // headerResults() {
    //   return Object.entries(this.results).map(([type, value]) => ({ type, value }));
    // },

    currentQuestionGaps() {
      return this.currentQuestionblocks.filter(({ isGapComponent }) => isGapComponent);
    },

    isDragType() {
      // ?
      if (!this.currentQuestionblocks) return undefined;

      const hasGap = (text) => /id\d+-(select|drag)/g.test(text);
      const questions = this.currentQuestionblocks.filter(({ text }) => hasGap(text));
      return questions.every((question) => question.component === 'Draggable');
    },

    isFilled() {
      return this.currentAttempts.length === this.gapsLength;
    },
  },

  watch: {
    elapsedTime(newTime) {
      if (newTime) {
        this.checkAnswers();
      }
    },
  },

  async created() {
    await this.$store.dispatch('FillGapsModule/fetchQuestions');
    this.loadCurrentQuestionblocks();
    this.timer.bonus = -this.getElapsedTime();
    if (!this.currentQuestion) {
      this.finishGame();
    }
  },

  mounted() {
    this.timer.state = 'discounting';

    this.isMobile = this.checkIfIsMobile();
    window.addEventListener('resize', () => {
      this.isMobile = this.checkIfIsMobile();
    });
  },

  methods: {
    checkIfIsMobile() {
      const isLandscape = window.matchMedia('(orientation: landscape)').matches;
      return isLandscape ? window.innerHeight <= MOBILE_BREAKPOINT : window.innerWidth <= MOBILE_BREAKPOINT;
    },

    getGapType(componentType) {
      return !this.isMobile ? componentType : 'Select';
    },

    loadCurrentQuestionblocks() {
      const blocks = this.currentText?.split(/\[|\]/g);

      this.resetFailsAndHits();
      this.currentQuestionblocks = blocks?.map((block) => {
        const isGapComponent = /id\d+-(select|drag)/g.test(block);

        if (!isGapComponent) {
          return { text: block, isGapComponent };
        }

        const id = this.getGapId(block);
        return {
          text: block,
          component: this.getComponent(block),
          id,
          isGapComponent,
        };
      });
      this.timer.state = 'discounting';
    },

    getGapId(block) {
      const [id] = block.match(/\d+/g);
      return parseInt(id, 10);
    },

    getComponent(type) {
      try {
        const currentType = type.match(/select|drag/g);
        return COMPONETS[currentType];
      } catch (e) {
        return undefined;
      }
    },

    getState(gapId, isCorrect = false) {
      const optionFail = this.currentAttempts.find(({ id }) => gapId === id);
      if (!optionFail) return BLOCK_STATES.DEFAULT;

      return isCorrect ? BLOCK_STATES.CORRECT : BLOCK_STATES.INCORRECT;
    },

    shuffle(items) {
      const randomItem = Math.floor(Math.random() * items.length);
      const item = items[randomItem];

      if (items.length === 0) return [];

      items.splice(randomItem, 1);
      return [item, ...this.shuffle(items)];
    },

    timerController(data) {
      if (data.state) {
        this.timer.state = data.state;

        if (this.timer.state === 'stopped') {
          this.finishTime();
        }
      }

      if (data.bonus) {
        this.timer.bonus = data.bonus;
      }
    },

    async finishTime() {
      const answer = {
        questionId: this.currentQuestion?.id,
        attempts: [],
        time: this.time,
      };
      await this.$store.dispatch('FillGapsModule/checkAnswer', answer);
      this.loadNextQuestion();
    },

    saveTime(currentTime) {
      this.elapsedTime = this.time + this.timer.bonus - currentTime;
    },

    finishGame() {
      this.$emit('change-page', 2);
    },

    saveAnswer(answer) {
      const answers = this.currentAttempts.filter(({ id }) => id !== answer.id);
      this.currentAttempts = [...answers, answer];
    },

    resetData() {
      this.reset += 1;
      this.currentAttempts = [];
      this.loadCurrentQuestionblocks();
    },

    confirm() {
      if (!this.isFilled) return;

      this.timer.state = 'paused';
      this.timer.bonus = 0;
    },

    async checkAnswers() {
      const answer = {
        questionId: this.currentQuestion.id,
        attempts: this.currentAttempts,
        time: this.elapsedTime,
      };
      const attemptsState = await this.$store.dispatch('FillGapsModule/checkAnswer', answer);
      attemptsState.sort((a, b) => a.id - b.id);

      this.addHitsAndFailuresAnswer(attemptsState);
      this.loadCurrentQuestionblocks();
      this.renderFailsAndHits(attemptsState);
      const hasFailures = attemptsState.some((attempt) => !attempt.correct);
      this.timer.bonus = 0;
      if (hasFailures) {
        this.timer.state = 'discounting';
        this.timer.bonus = -5;
        return;
      }
      setTimeout(this.loadNextQuestion, 1000);
    },

    resetFailsAndHits() {
      const $elements = this.$el.querySelectorAll('.text :is(.Draggable, .Select)');
      $elements.forEach(($element) => {
        $element.classList.remove(BLOCK_STATES.CORRECT);
        $element.classList.remove(BLOCK_STATES.INCORRECT);
      });
    },

    renderFailsAndHits(attemptsState) {
      const $elements = this.$el.querySelectorAll('.text :is(.Draggable, .Select)');
      $elements.forEach(($element, i) => {
        const state = this.getState(attemptsState[i].id, attemptsState[i]?.correct);
        $element.classList.add(state);
      });
    },

    addHitsAndFailuresAnswer(attemptsState) {
      const hits = attemptsState.filter(({ correct }) => correct)?.length ?? 0;
      const failures = attemptsState.filter(({ correct }) => !correct)?.length ?? 0;
      this.results = {
        correct: this.results.correct + hits,
        incorrect: this.results.incorrect + failures,
      };
    },

    loadNextQuestion() {
      const currentQuestion = this.getNextQuestion();
      if (!currentQuestion) {
        this.finishGame();
      } else {
        this.$store.dispatch('FillGapsModule/setCurrentQuestion', currentQuestion);
        this.currentAttempts = [];
        this.loadCurrentQuestionblocks();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.FillGaps {
  height: 100%;

  .main-container {
    box-sizing: border-box;
    background: #fff;
    display: grid;
    grid-template-rows: 1fr auto auto;
    grid-template-columns: 1fr;
    align-items: flex-start;
    overflow: hidden;

    .text-container {
      padding: $spacing-l;
      overflow: auto;
      height: 100%;
      box-sizing: border-box;
      text-align: justify;
      .text {
        font-family: $font-family-secondary;
        display: inline;
        font-size: clamp($font-size-m, 2vw, $font-size-2xl);
        line-height: 1.8;
        color: $color-neutral-mid-darker;

        .simple-text {
          -webkit-user-select: none; /* Safari */
          -ms-user-select: none; /* IE 10 and IE 11 */
          user-select: none;
        }

        ::v-deep div {
          display: inline;
        }

        .gap {
          &.incorrect {
            &.Select,
            &.Draggable ::v-deep .word {
              box-shadow: none;
              background: adjust-color($color: $color-error, $alpha: -0.5);
              animation: bounce 0.5s;
            }
          }

          &.correct {
            &.Select,
            &.Draggable ::v-deep .word {
              box-shadow: none;
              background: adjust-color($color: $color-success, $alpha: -0.5);
              animation: correct 0.5s;
            }
          }
        }
      }
    }

    .options-container {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-s;
      position: relative;
    }

    .button-container {
      border-top: 1px solid $color-neutral-mid;
      max-width: 800px;
      margin: 0 auto;
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: space-around;
      gap: clamp($spacing-m, 2vw, $spacing-xl);
      position: relative;
    }

    .options-container,
    .button-container {
      padding: clamp($spacing-m, 2vw, $spacing-xl);
      &::after {
        content: '';
        position: absolute;
        bottom: 100%;
        left: 0;
        right: 0;
        height: clamp($font-size-m, 2vw, $font-size-2xl);
        background: linear-gradient(0deg, rgb(255, 255, 255) 0%, rgba(255, 255, 255, 0) 100%);
      }
    }
  }

  @media #{$breakpoint-sm-max} {
    .main-container {
      .text .Select {
        font-size: clamp($font-size-m, 2vw, $font-size-l);
        padding: clamp($spacing-2xs, 2vw, $spacing-xs);
      }
      .button-container {
        gap: $spacing-m;
        padding: $spacing-m;
      }
    }
  }
}
</style>

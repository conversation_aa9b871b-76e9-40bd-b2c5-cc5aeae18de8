<template>
  <div class="Quiz">
    <GameTemplate :loading="isLoadingData">
      <template v-slot:header>
        <Header :title="$t('QUIZ.TITLE')">
          <AnswerResultsResponsive
            :answer-results="getAnswersResults()"
            :progress="{ current: currentQuestionIndex, total: questions.length }"
          />
        </Header>
      </template>

      <template v-slot:main>
        <Question
          v-if="currentQuestion"
          :key="currentQuestion.id"
          :question="currentQuestion"
          :time="time"
          :init-bonus="bonus"
          @finish-question="finishQuestion"
        />
      </template>
    </GameTemplate>

    <BaseModal v-if="feedback">
      <FeedbackPanel :state="feedback.state">
        <template v-slot:content>
          {{ feedback.text }}
        </template>

        <template v-slot:footer>
          <BaseButton @click="closeFeedback">
            {{ $t('CONTINUE.BUTTON') }}
            <font-awesome-icon icon="fa-solid fa-angle-right" />
          </BaseButton>
        </template>
      </FeedbackPanel>
    </BaseModal>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import GameTemplate from '@/components/common/GameTemplate';
import Header from '@/components/header/Header';
import AnswerResultsResponsive from '@/components/header/AnswerResultsResponsive';
import Question from '@/components/quiz/Question';
import FeedbackPanel from '@/components/common/FeedbackPanel';

const STORE = 'QuizModule';
export default {
  name: 'Game',
  components: {
    GameTemplate,
    AnswerResultsResponsive,
    Header,
    Question,
    FeedbackPanel,
  },

  data() {
    return {
      currentQuestion: undefined,
      bonus: undefined,
      feedbackText: undefined,
      feedback: undefined,
    };
  },

  computed: {
    ...get(STORE, [
      'isLoading',
      'getFirstQuestion',
      'getGlobalTime',
      'getAnswersResults',
      'getNextQuestion',
      'isLastQuestion',
      'getElapsedTime',
      'isLastAnswerCorrect',
    ]),

    questions: get(`${STORE}/questions`),

    currentQuestionIndex() {
      return this.questions?.findIndex((question) => question.id === this.currentQuestion?.id) ?? 0;
    },

    isLoadingData() {
      return this.isLoading();
    },

    time() {
      return this.getGlobalTime();
    },
  },

  async created() {
    await this.$store.dispatch('QuizModule/fetchQuestions');

    this.currentQuestion = this.getFirstQuestion();
    if (!this.currentQuestion) this.finishQuiz();

    this.bonus = -this.getElapsedTime();
  },

  methods: {
    async finishQuestion(answer) {
      await this.saveAnswer(answer);

      await this.addFeedBackAnimation(answer?.id);

      this.feedback = this.getCurrentFeedback(this.currentQuestion?.feedback);
      if (!this.feedback) {
        this.nextQuestion();
      }
    },

    closeFeedback() {
      this.feedback = undefined;
      this.nextQuestion();
    },

    getCurrentFeedback(feedback) {
      if (!feedback || !feedback.active) return undefined;

      const isCorrect = this.isLastAnswerCorrect();
      const state = isCorrect ? 'correct' : 'incorrect';
      const text = feedback[state];
      if (!text) return undefined;

      return { text, state };
    },

    async addFeedBackAnimation(answerId) {
      return new Promise((resolve) => {
        if (!answerId) resolve();

        const isCorrect = this.isLastAnswerCorrect();
        const $answer = this.$el.querySelector(`#answer${answerId}.Answer`);
        if (!$answer) resolve();

        const className = isCorrect ? 'correct' : 'incorrect';
        const $question = this.$el.querySelector('.Question');

        $question.classList.add('disabled');
        $answer.classList.remove('selected');
        $answer.classList.add(className);

        setTimeout(() => {
          $answer.classList.remove(className);
          $question.classList.remove('disabled');
          resolve();
        }, 1000);
      });
    },

    async saveAnswer(answer) {
      await this.$store.dispatch('QuizModule/saveAnswer', answer);
    },

    nextQuestion() {
      const elapsedTime = this.getElapsedTime();
      if (this.isLastQuestion() || elapsedTime >= this.time) {
        this.finishQuiz();
      } else {
        this.bonus = -elapsedTime;
        this.currentQuestion = this.getNextQuestion();
      }
    },

    finishQuiz() {
      this.$emit('change-page', 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.Quiz {
  height: 100%;

  .Question.disabled {
    pointer-events: none;
  }
}
</style>

<template>
  <div class="TrueOrFalse">
    <GameTemplate :loading="isLoadingData">
      <template v-slot:header>
        <Header :title="$t(`${gameType.toUpperCase()}.TITLE`)">
          <AnswerResultsResponsive
            :answer-results="getAnswersResults()"
            :progress="{ current: currentQuestionIndex, total: questions.length }"
          />
        </Header>
      </template>

      <template v-slot:main>
        <Question
          v-if="currentQuestion"
          :key="currentQuestion.id"
          :current-question="currentQuestion"
          :init-bonus="bonus"
          @select-answer="finishQuestion"
        />
      </template>
    </GameTemplate>
  </div>
</template>

<script>
import { get } from 'vuex-pathify';
import GameTemplate from '@/components/common/GameTemplate';
import Header from '@/components/header/Header';
import Question from '@/components/trueOrFalse/Question';
import AnswerResultsResponsive from '@/components/header/AnswerResultsResponsive';

const STORE = 'TrueOrFalseModule';
export default {
  components: {
    GameTemplate,
    Header,
    Question,
    AnswerResultsResponsive,
  },

  data() {
    return {
      currentQuestion: undefined,
      bonus: undefined,
      questionNumber: undefined,
    };
  },

  computed: {
    ...get('', ['getChapterGame']),

    ...get(STORE, [
      'isLoading',
      'getFirstQuestion',
      'getGlobalTime',
      'getAnswersResults',
      'getNextQuestion',
      'isLastQuestion',
      'getElapsedTime',
    ]),

    gameType() {
      return this.getChapterGame();
    },

    questions: get(`${STORE}/questions`),

    currentQuestionIndex() {
      return this.questions?.findIndex((question) => question.id === this.currentQuestion?.id) ?? 0;
    },

    isLoadingData() {
      return this.isLoading();
    },

    time() {
      return this.getGlobalTime();
    },

    getQuestion() {
      return this.getAnswersResults();
    },

    // correctNumber() {
    //   const questionArray = this.getQuestion;
    //   const num = questionArray.filter((type) => type === 'correct');
    //   return (num.length).toString();
    // },

    // incorrectNumber() {
    //   const questionArray = this.getQuestion;
    //   const num = questionArray.filter((type) => type === 'incorrect');
    //   return (num.length).toString();
    // },

    // unansweredNumber() {
    //   const questionArray = this.getQuestion;
    //   const num = questionArray.filter((type) => type === 'unanswered');
    //   return (num.length).toString();
    // },

    // headerResults() {
    //   const types = ['correct', 'incorrect'];

    //   return types.map((type) => {
    //     const valueFunction = `${type}Number`;

    //     return { type, value: this.[valueFunction] };
    //   });
    // },
  },

  async created() {
    await this.$store.dispatch(`${STORE}/fetchQuestions`);

    this.currentQuestion = this.getFirstQuestion();
    if (!this.currentQuestion) this.finishTrueOrFalse();

    this.questionNumber = this.getQuestionNumber();
  },

  methods: {
    async finishQuestion(answer) {
      await this.saveAnswer(answer);
      this.nextQuestion();
    },

    async saveAnswer(answer) {
      await this.$store.dispatch(`${STORE}/saveAnswer`, answer);
    },

    nextQuestion() {
      if (this.isLastQuestion()) {
        this.finishTrueOrFalse();
      } else {
        this.currentQuestion = this.getNextQuestion();
      }
    },

    getQuestionNumber() {
      let questionArray = this.getQuestion;
      questionArray = questionArray.length;
      return questionArray;
    },

    finishTrueOrFalse() {
      this.$emit('change-page', 2);
    },
  },
};
</script>

<style lang="scss" scoped>
.TrueOrFalse {
  height: 100%;

  .progress {
    font-family: $font-family-secondary;
    font-size: $font-size-2xl;
    font-weight: 500;
    text-transform: uppercase;
    padding: 10px 1.25rem;
    background: $color-neutral-mid-darker;
    border-radius: $border-radius-xl;
    box-shadow: $shadow-elevation-1;
    color: $color-neutral-light;

    b {
      font-weight: 600;
      color: $color-neutral-darkest;
    }
  }
}
</style>

const { createjs } = window;

module.exports = class Utils {
  // constructor() {}
  static shuffle(pieces) {
    const piecesShuffled = pieces;
    for (let i = pieces.length - 1; i > 0; i -= 1) {
      const j = Math.floor(Math.random() * (i + 1));
      const piece = pieces[i];
      piecesShuffled[i] = pieces[j];
      piecesShuffled[j] = piece;
    }

    return pieces;
  }

  static createBitmap(oSprite, iWidth, iHeight) {
    const oBmp = new createjs.Bitmap(oSprite);
    const hitObject = new createjs.Shape();

    if (iWidth && iHeight) {
      hitObject.graphics.beginFill('#fff').drawRect(0, 0, iWidth, iHeight);
    } else {
      hitObject.graphics.beginFill('#ff0').drawRect(0, 0, oSprite.width, oSprite.height);
    }

    oBmp.hitArea = hitObject;

    return oBmp;
  }

  static createSprite(oSpriteSheet, szState, iRegX, iRegY, iWidth, iHeight) {
    let oRetSprite;
    if (szState !== null) {
      oRetSprite = new createjs.Sprite(oSpriteSheet, szState);
    } else {
      oRetSprite = new createjs.Sprite(oSpriteSheet);
    }

    const hitObject = new createjs.Shape();
    hitObject.graphics.beginFill('#000000').drawRect(-iRegX, -iRegY, iWidth, iHeight);

    oRetSprite.hitArea = hitObject;

    return oRetSprite;
  }

  static distance(v1, v2) {
    return Math.sqrt(((v2.x - v1.x) * (v2.x - v1.x)) + ((v2.y - v1.y) * (v2.y - v1.y)));
  }

  static distance2(v1, v2) {
    return ((v2.x - v1.x) * (v2.x - v1.x)) + ((v2.y - v1.y) * (v2.y - v1.y));
  }
};

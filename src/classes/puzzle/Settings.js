const DIFFICULTY = [16, 36, 64, 100];

module.exports = class Settings {
  constructor(difficulty) {
    this.difficulty = difficulty ?? 0;

    this.aMatSize = [];
    this.aMatSize[`num_${DIFFICULTY[0]}`] = { rows: 4, cols: 4 };
    this.aMatSize[`num_${DIFFICULTY[1]}`] = { rows: 6, cols: 6 };
    this.aMatSize[`num_${DIFFICULTY[2]}`] = { rows: 8, cols: 8 };
    this.aMatSize[`num_${DIFFICULTY[3]}`] = { rows: 10, cols: 10 };

    // SET PIECE SIZE
    this.pieces = [];
    this.pieces[`difficulty_${DIFFICULTY[0]}`] = [];
    this.pieces[`difficulty_${DIFFICULTY[1]}`] = [];
    this.pieces[`difficulty_${DIFFICULTY[2]}`] = [];
    this.pieces[`difficulty_${DIFFICULTY[3]}`] = [];

    this.pieces[`difficulty_${DIFFICULTY[0]}`][0] = [0, 0, 212, 279];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][1] = [146, 0, 334, 212];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][2] = [412, 0, 220, 272];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][3] = [567, 0, 273, 212];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][4] = [0, 205, 277, 216];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][5] = [209, 152, 213, 333];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][6] = [356, 208, 341, 214];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][7] = [628, 144, 212, 341];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][8] = [0, 353, 215, 340];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][9] = [146, 411, 339, 219];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][10] = [412, 347, 220, 346];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][11] = [565, 418, 275, 216];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][12] = [0, 628, 276, 212];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][13] = [209, 570, 215, 270];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][14] = [353, 626, 342, 214];
    this.pieces[`difficulty_${DIFFICULTY[0]}`][15] = [628, 566, 212, 274];

    this.pieces[`difficulty_${DIFFICULTY[1]}`][0] = [0, 0, 143, 187];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][1] = [98, 0, 225, 142];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][2] = [277, 0, 149, 182];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][3] = [383, 0, 234, 142];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][4] = [564, 0, 144, 184];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][5] = [657, 0, 183, 142];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][6] = [0, 137, 187, 145];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][7] = [140, 102, 144, 222];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][8] = [239, 139, 231, 143];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][9] = [420, 96, 145, 228];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][10] = [514, 139, 230, 144];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][11] = [703, 97, 137, 223];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][12] = [0, 236, 145, 227];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][13] = [98, 274, 228, 147];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][14] = [276, 231, 149, 233];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][15] = [380, 279, 237, 145];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][16] = [565, 235, 142, 229];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][17] = [662, 274, 178, 151];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][18] = [0, 419, 186, 142];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][19] = [140, 380, 145, 218];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][20] = [237, 417, 230, 144];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][21] = [420, 377, 146, 234];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][22] = [516, 419, 236, 143];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][23] = [703, 378, 137, 224];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][24] = [0, 506, 143, 241];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][25] = [98, 558, 225, 144];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][26] = [276, 509, 149, 233];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][27] = [381, 559, 240, 142];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][28] = [564, 528, 143, 214];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][29] = [661, 559, 179, 143];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][30] = [0, 697, 187, 143];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][31] = [141, 662, 143, 178];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][32] = [239, 699, 231, 141];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][33] = [420, 656, 143, 184];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][34] = [517, 699, 231, 141];
    this.pieces[`difficulty_${DIFFICULTY[1]}`][35] = [704, 655, 136, 185];

    this.pieces[`difficulty_${DIFFICULTY[2]}`][0] = [0, 0, 107, 141];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][1] = [73, 0, 168, 107];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][2] = [206, 0, 111, 137];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][3] = [284, 0, 175, 106];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][4] = [419, 0, 108, 138];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][5] = [489, 0, 175, 106];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][6] = [628, 0, 108, 137];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][7] = [701, 0, 139, 106];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][8] = [0, 103, 139, 108];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][9] = [104, 76, 108, 166];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][10] = [178, 103, 171, 108];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][11] = [314, 72, 108, 170];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][12] = [382, 104, 173, 108];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][13] = [524, 73, 107, 167];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][14] = [595, 103, 177, 110];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][15] = [734, 67, 106, 173];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][16] = [0, 176, 108, 172];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][17] = [73, 205, 171, 111];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][18] = [206, 173, 111, 174];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][19] = [283, 208, 176, 110];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][20] = [420, 176, 106, 172];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][21] = [493, 206, 171, 112];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][22] = [629, 176, 108, 172];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][23] = [699, 206, 141, 111];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][24] = [0, 314, 139, 108];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][25] = [105, 285, 108, 165];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][26] = [176, 312, 172, 110];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][27] = [314, 283, 108, 176];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][28] = [383, 315, 177, 107];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][29] = [524, 284, 107, 169];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][30] = [607, 314, 161, 109];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][31] = [734, 284, 106, 168];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][32] = [0, 381, 108, 180];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][33] = [74, 420, 166, 107];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][34] = [206, 383, 111, 174];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][35] = [284, 420, 178, 106];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][36] = [419, 398, 108, 159];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][37] = [492, 420, 172, 107];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][38] = [628, 393, 110, 165];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][39] = [696, 420, 144, 107];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][40] = [0, 524, 141, 108];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][41] = [105, 497, 107, 166];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][42] = [178, 524, 171, 108];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][43] = [314, 492, 108, 171];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][44] = [385, 524, 172, 111];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][45] = [524, 492, 108, 171];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][46] = [594, 524, 181, 111];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][47] = [735, 493, 105, 172];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][48] = [0, 598, 108, 171];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][49] = [73, 626, 171, 112];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][50] = [206, 594, 111, 178];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][51] = [283, 629, 174, 108];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][52] = [419, 601, 108, 168];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][53] = [496, 630, 167, 107];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][54] = [626, 598, 112, 168];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][55] = [696, 629, 144, 108];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][56] = [0, 735, 139, 105];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][57] = [105, 703, 108, 137];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][58] = [177, 734, 171, 106];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][59] = [314, 703, 108, 137];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][60] = [385, 734, 175, 106];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][61] = [523, 709, 109, 131];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][62] = [597, 734, 173, 106];
    this.pieces[`difficulty_${DIFFICULTY[2]}`][63] = [735, 698, 105, 142];

    this.pieces[`difficulty_${DIFFICULTY[3]}`][0] = [0, 0, 85, 112];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][1] = [58, 0, 134, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][2] = [165, 0, 88, 109];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][3] = [227, 0, 140, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][4] = [336, 0, 85, 110];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][5] = [391, 0, 140, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][6] = [503, 0, 85, 110];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][7] = [561, 0, 137, 86];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][8] = [671, 0, 87, 110];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][9] = [728, 0, 112, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][10] = [0, 83, 111, 86];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][11] = [84, 62, 85, 132];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][12] = [143, 83, 137, 86];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][13] = [252, 58, 85, 137];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][14] = [306, 84, 137, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][15] = [419, 59, 85, 133];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][16] = [476, 84, 134, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][17] = [587, 59, 88, 135];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][18] = [646, 84, 133, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][19] = [756, 59, 84, 132];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][20] = [0, 142, 86, 136];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][21] = [59, 166, 135, 87];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][22] = [165, 140, 89, 138];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][23] = [227, 168, 140, 86];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][24] = [336, 141, 85, 137];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][25] = [394, 165, 137, 90];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][26] = [503, 142, 86, 136];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][27] = [562, 165, 139, 88];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][28] = [671, 143, 87, 136];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][29] = [731, 165, 109, 88];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][30] = [0, 252, 110, 84];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][31] = [83, 229, 87, 131];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][32] = [142, 251, 136, 87];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][33] = [252, 227, 85, 140];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][34] = [307, 252, 142, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][35] = [419, 227, 85, 134];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][36] = [486, 252, 129, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][37] = [586, 227, 87, 128];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][38] = [647, 252, 135, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][39] = [756, 228, 84, 132];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][40] = [0, 304, 85, 144];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][41] = [58, 336, 134, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][42] = [166, 307, 87, 138];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][43] = [228, 336, 141, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][44] = [336, 318, 85, 128];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][45] = [394, 336, 136, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][46] = [503, 314, 87, 133];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][47] = [563, 336, 128, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][48] = [672, 310, 86, 135];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][49] = [730, 336, 110, 84];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][50] = [0, 419, 111, 86];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][51] = [84, 398, 85, 132];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][52] = [144, 420, 134, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][53] = [252, 394, 85, 137];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][54] = [308, 420, 138, 88];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][55] = [419, 394, 87, 136];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][56] = [475, 420, 139, 88];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][57] = [587, 395, 87, 136];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][58] = [646, 418, 137, 90];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][59] = [757, 395, 83, 136];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][60] = [0, 478, 86, 136];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][61] = [59, 502, 135, 87];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][62] = [165, 476, 89, 138];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][63] = [227, 504, 139, 86];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][64] = [335, 481, 86, 133];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][65] = [397, 504, 133, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][66] = [501, 479, 88, 135];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][67] = [565, 503, 136, 87];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][68] = [673, 481, 85, 133];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][69] = [734, 503, 106, 87];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][70] = [0, 588, 110, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][71] = [83, 565, 87, 135];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][72] = [142, 587, 136, 87];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][73] = [252, 563, 85, 128];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][74] = [306, 588, 143, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][75] = [419, 562, 87, 134];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][76] = [477, 587, 137, 86];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][77] = [588, 563, 86, 136];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][78] = [646, 588, 140, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][79] = [757, 563, 83, 131];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][80] = [0, 645, 85, 139];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][81] = [58, 672, 134, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][82] = [166, 646, 88, 135];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][83] = [228, 673, 139, 84];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][84] = [336, 646, 85, 135];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][85] = [391, 672, 140, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][86] = [503, 646, 85, 137];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][87] = [561, 672, 137, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][88] = [671, 646, 87, 141];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][89] = [728, 672, 112, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][90] = [0, 755, 110, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][91] = [85, 734, 84, 106];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][92] = [142, 756, 136, 84];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][93] = [252, 729, 85, 111];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][94] = [308, 755, 140, 85];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][95] = [419, 729, 86, 111];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][96] = [479, 756, 135, 84];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][97] = [587, 730, 86, 110];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][98] = [645, 756, 140, 84];
    this.pieces[`difficulty_${DIFFICULTY[3]}`][99] = [755, 730, 85, 110];

    // SET PIECE JOINTS
    this.pieceJoint = [];
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`] = [];
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`] = [];
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`] = [];
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`] = [];

    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][0] = {
      up: null, right: [180, 105], down: [87, 242], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][1] = {
      up: null, right: [303, 106], down: [164, 177], left: [32, 106],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][2] = {
      up: null, right: [186, 115], down: [101, 239], left: [35, 103],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][3] = {
      up: null, right: null, down: [190, 176], left: [30, 116],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][4] = {
      up: [86, 41], right: [245, 118], down: [104, 179], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][5] = {
      up: [100, 28], right: [179, 148], down: [94, 300], left: [35, 170],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][6] = {
      up: [156, 35], right: [307, 107], down: [181, 172], left: [34, 91],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][7] = {
      up: [128, 34], right: null, down: [92, 306], left: [32, 170],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][8] = {
      up: [100, 33], right: [179, 170], down: [91, 310], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][9] = {
      up: [160, 39], right: [305, 112], down: [181, 185], left: [34, 112],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][10] = {
      up: [122, 32], right: [186, 187], down: [114, 311], left: [39, 175],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][11] = {
      up: [154, 32], right: null, down: [181, 178], left: [32, 116],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][12] = {
      up: [89, 33], right: [241, 118], down: null, left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][13] = {
      up: [116, 27], right: [179, 150], down: null, left: [33, 173],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][14] = {
      up: [171, 36], right: [309, 107], down: null, left: [36, 97],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[0]}`][15] = {
      up: [118, 33], right: null, down: null, left: [31, 169],
    };

    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][0] = {
      up: null, right: [120, 71], down: [59, 162], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][1] = {
      up: null, right: [203, 71], down: [110, 123], left: [23, 70],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][2] = {
      up: null, right: [128, 78], down: [66, 161], left: [27, 68],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][3] = {
      up: null, right: [209, 62], down: [125, 119], left: [20, 77],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][4] = {
      up: null, right: [120, 57], down: [64, 162], left: [27, 61],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][5] = {
      up: null, right: null, down: [119, 119], left: [25, 57],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][6] = {
      up: [59, 25], right: [165, 79], down: [71, 121], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][7] = {
      up: [68, 20], right: [122, 97], down: [64, 197], left: [24, 114],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][8] = {
      up: [107, 22], right: [207, 71], down: [124, 115], left: [22, 60],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][9] = {
      up: [87, 23], right: [119, 114], down: [63, 204], left: [22, 114],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][10] = {
      up: [115, 22], right: [210, 76], down: [129, 119], left: [25, 70],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][11] = {
      up: [72, 22], right: null, down: [75, 200], left: [22, 121],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][12] = {
      up: [70, 22], right: [124, 114], down: [60, 203], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][13] = {
      up: [110, 25], right: [204, 73], down: [121, 124], left: [23, 74],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][14] = {
      up: [83, 24], right: [128, 126], down: [76, 207], left: [28, 116],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][15] = {
      up: [105, 25], right: [211, 75], down: [125, 120], left: [22, 76],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][16] = {
      up: [78, 22], right: [119, 105], down: [71, 205], left: [28, 121],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][17] = {
      up: [113, 25], right: null, down: [105, 125], left: [23, 66],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][18] = {
      up: [61, 23], right: [163, 79], down: [69, 112], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][19] = {
      up: [75, 22], right: [124, 100], down: [72, 195], left: [22, 116],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][20] = {
      up: [115, 25], right: [208, 72], down: [119, 117], left: [24, 64],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][21] = {
      up: [81, 24], right: [119, 117], down: [76, 206], left: [23, 109],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][22] = {
      up: [120, 24], right: [212, 82], down: [125, 126], left: [25, 73],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][23] = {
      up: [64, 21], right: null, down: [68, 200], left: [24, 120],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][24] = {
      up: [69, 26], right: [121, 120], down: [56, 214], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][25] = {
      up: [112, 24], right: [202, 72], down: [112, 122], left: [22, 71],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][26] = {
      up: [77, 24], right: [128, 127], down: [66, 211], left: [26, 117],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][27] = {
      up: [119, 27], right: [215, 60], down: [129, 115], left: [22, 78],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][28] = {
      up: [74, 18], right: [120, 86], down: [82, 190], left: [31, 92],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][29] = {
      up: [110, 19], right: null, down: [117, 120], left: [24, 55],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][30] = {
      up: [59, 27], right: [164, 80], down: null, left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][31] = {
      up: [70, 20], right: [121, 98], down: null, left: [23, 115],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][32] = {
      up: [106, 22], right: [208, 70], down: null, left: [22, 60],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][33] = {
      up: [87, 22], right: [119, 106], down: null, left: [22, 113],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][34] = {
      up: [127, 22], right: [209, 80], down: null, left: [23, 62],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[1]}`][35] = {
      up: [72, 22], right: null, down: null, left: [22, 121],
    };

    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][0] = {
      up: null, right: [89, 51], down: [45, 121], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][1] = {
      up: null, right: [151, 53], down: [84, 90], left: [19, 53],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][2] = {
      up: null, right: [95, 57], down: [53, 118], left: [18, 53],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][3] = {
      up: null, right: [156, 44], down: [96, 89], left: [16, 58],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][4] = {
      up: null, right: [89, 42], down: [48, 120], left: [21, 44],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][5] = {
      up: null, right: [159, 56], down: [87, 89], left: [21, 41],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][6] = {
      up: null, right: [88, 50], down: [49, 117], left: [18, 57],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][7] = {
      up: null, right: null, down: [81, 84], left: [18, 52],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][8] = {
      up: [44, 20], right: [121, 58], down: [53, 89], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][9] = {
      up: [51, 15], right: [91, 73], down: [49, 147], left: [18, 85],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][10] = {
      up: [80, 18], right: [154, 54], down: [90, 87], left: [17, 45],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][11] = {
      up: [65, 18], right: [85, 84], down: [47, 154], left: [18, 85],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][12] = {
      up: [86, 18], right: [157, 58], down: [97, 89], left: [18, 51],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][13] = {
      up: [53, 16], right: [87, 77], down: [53, 148], left: [16, 90],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][14] = {
      up: [82, 19], right: [158, 48], down: [93, 92], left: [16, 48],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][15] = {
      up: [47, 17], right: null, down: [53, 155], left: [20, 83],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][16] = {
      up: [52, 18], right: [90, 86], down: [44, 153], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][17] = {
      up: [80, 21], right: [153, 56], down: [90, 95], left: [18, 56],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][18] = {
      up: [61, 19], right: [94, 94], down: [57, 156], left: [20, 88],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][19] = {
      up: [78, 17], right: [158, 57], down: [93, 91], left: [18, 59],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][20] = {
      up: [59, 19], right: [90, 80], down: [53, 152], left: [22, 90],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][21] = {
      up: [84, 18], right: [155, 64], down: [79, 94], left: [17, 50],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][22] = {
      up: [59, 19], right: [89, 80], down: [54, 153], left: [17, 93],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][23] = {
      up: [87, 18], right: null, down: [82, 94], left: [20, 50],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][24] = {
      up: [44, 18], right: [123, 60], down: [50, 87], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][25] = {
      up: [58, 15], right: [91, 76], down: [54, 150], left: [16, 87],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][26] = {
      up: [86, 16], right: [156, 55], down: [87, 90], left: [20, 48],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][27] = {
      up: [61, 16], right: [89, 88], down: [58, 155], left: [17, 83],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][28] = {
      up: [90, 18], right: [159, 59], down: [92, 93], left: [17, 56],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][29] = {
      up: [48, 16], right: [96, 89], down: [48, 152], left: [17, 90],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][30] = {
      up: [78, 16], right: [146, 44], down: [76, 92], left: [14, 59],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][31] = {
      up: [50, 16], right: null, down: [55, 150], left: [18, 74],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][32] = {
      up: [54, 17], right: [91, 90], down: [42, 161], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][33] = {
      up: [83, 18], right: [150, 53], down: [83, 90], left: [17, 53],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][34] = {
      up: [58, 17], right: [96, 96], down: [52, 156], left: [18, 88],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][35] = {
      up: [89, 19], right: [158, 45], down: [96, 88], left: [17, 59],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][36] = {
      up: [56, 11], right: [91, 62], down: [62, 142], left: [23, 66],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][37] = {
      up: [82, 18], right: [155, 59], down: [86, 89], left: [19, 41],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][38] = {
      up: [55, 14], right: [87, 78], down: [64, 145], left: [20, 84],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][39] = {
      up: [92, 18], right: null, down: [92, 92], left: [19, 52],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][40] = {
      up: [45, 19], right: [125, 58], down: [52, 91], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][41] = {
      up: [51, 13], right: [90, 70], down: [48, 146], left: [18, 85],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][42] = {
      up: [82, 17], right: [154, 54], down: [89, 86], left: [20, 45],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][43] = {
      up: [66, 18], right: [90, 78], down: [46, 154], left: [17, 85],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][44] = {
      up: [95, 16], right: [155, 58], down: [88, 92], left: [20, 47],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][45] = {
      up: [54, 18], right: [89, 80], down: [45, 153], left: [19, 90],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][46] = {
      up: [96, 16], right: [162, 56], down: [90, 92], left: [18, 47],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][47] = {
      up: [52, 17], right: null, down: [45, 152], left: [22, 86],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][48] = {
      up: [52, 18], right: [91, 85], down: [44, 153], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][49] = {
      up: [80, 22], right: [154, 56], down: [85, 94], left: [18, 56],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][50] = {
      up: [63, 19], right: [96, 94], down: [58, 162], left: [19, 89],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][51] = {
      up: [78, 16], right: [156, 50], down: [87, 88], left: [16, 59],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][52] = {
      up: [54, 17], right: [93, 84], down: [54, 150], left: [20, 78],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][53] = {
      up: [73, 19], right: [150, 58], down: [83, 92], left: [17, 55],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][54] = {
      up: [56, 20], right: [92, 81], down: [63, 150], left: [20, 90],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][55] = {
      up: [87, 21], right: null, down: [96, 85], left: [21, 50],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][56] = {
      up: [45, 16], right: [122, 58], down: null, left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][57] = {
      up: [52, 18], right: [91, 78], down: null, left: [16, 88],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][58] = {
      up: [88, 23], right: [154, 53], down: null, left: [18, 46],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][59] = {
      up: [54, 18], right: [89, 79], down: null, left: [16, 83],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][60] = {
      up: [87, 18], right: [157, 63], down: null, left: [18, 41],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][61] = {
      up: [59, 13], right: [91, 77], down: null, left: [19, 87],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][62] = {
      up: [91, 18], right: [158, 61], down: null, left: [18, 53],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[2]}`][63] = {
      up: [56, 18], right: null, down: null, left: [17, 98],
    };

    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][0] = {
      up: null, right: [72, 41], down: [33, 96], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][1] = {
      up: null, right: [120, 42], down: [66, 72], left: [12, 42],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][2] = {
      up: null, right: [75, 46], down: [39, 96], left: [14, 42],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][3] = {
      up: null, right: [123, 36], down: [75, 70], left: [13, 47],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][4] = {
      up: null, right: [71, 32], down: [38, 95], left: [14, 36],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][5] = {
      up: null, right: [126, 47], down: [70, 69], left: [14, 33],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][6] = {
      up: null, right: [71, 35], down: [37, 95], left: [14, 45],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][7] = {
      up: null, right: [123, 41], down: [68, 71], left: [13, 35],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][8] = {
      up: null, right: [72, 33], down: [38, 95], left: [14, 40],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][9] = {
      up: null, right: null, down: [70, 70], left: [14, 34],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][10] = {
      up: [35, 15], right: [96, 47], down: [42, 72], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][11] = {
      up: [38, 12], right: [72, 57], down: [38, 118], left: [14, 66],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][12] = {
      up: [64, 12], right: [123, 43], down: [72, 70], left: [13, 35],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][13] = {
      up: [51, 14], right: [69, 68], down: [36, 122], left: [13, 68],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][14] = {
      up: [68, 14], right: [125, 44], down: [76, 71], left: [14, 39],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][15] = {
      up: [42, 14], right: [71, 60], down: [42, 119], left: [11, 70],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][16] = {
      up: [65, 12], right: [122, 46], down: [73, 70], left: [13, 35],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][17] = {
      up: [40, 13], right: [72, 63], down: [43, 121], left: [13, 72],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][18] = {
      up: [64, 13], right: [121, 38], down: [73, 71], left: [14, 38],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][19] = {
      up: [42, 13], right: null, down: [41, 119], left: [12, 66],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][20] = {
      up: [41, 14], right: [72, 68], down: [36, 123], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][21] = {
      up: [63, 14], right: [122, 44], down: [72, 72], left: [13, 43],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][22] = {
      up: [50, 13], right: [76, 74], down: [44, 124], left: [15, 69],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][23] = {
      up: [62, 14], right: [125, 47], down: [74, 72], left: [12, 47],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][24] = {
      up: [47, 15], right: [71, 64], down: [42, 122], left: [16, 69],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][25] = {
      up: [68, 14], right: [122, 51], down: [63, 76], left: [15, 39],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][26] = {
      up: [47, 12], right: [73, 68], down: [43, 120], left: [13, 74],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][27] = {
      up: [68, 15], right: [122, 48], down: [63, 74], left: [14, 44],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][28] = {
      up: [50, 13], right: [73, 61], down: [43, 121], left: [16, 69],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][29] = {
      up: [66, 14], right: null, down: [62, 74], left: [12, 39],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][30] = {
      up: [36, 13], right: [96, 45], down: [40, 67], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][31] = {
      up: [47, 11], right: [72, 60], down: [43, 118], left: [14, 70],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][32] = {
      up: [68, 14], right: [123, 43], down: [69, 70], left: [14, 39],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][33] = {
      up: [46, 13], right: [68, 70], down: [46, 122], left: [13, 66],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][34] = {
      up: [71, 12], right: [126, 47], down: [73, 73], left: [15, 44],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][35] = {
      up: [36, 14], right: [75, 69], down: [37, 120], left: [14, 71],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][36] = {
      up: [60, 12], right: [114, 47], down: [60, 73], left: [9, 46],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][37] = {
      up: [38, 14], right: [74, 60], down: [43, 117], left: [15, 73],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][38] = {
      up: [67, 12], right: [122, 50], down: [70, 70], left: [13, 36],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][39] = {
      up: [35, 11], right: null, down: [44, 118], left: [12, 73],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][40] = {
      up: [40, 15], right: [71, 72], down: [33, 128], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][41] = {
      up: [67, 12], right: [120, 42], down: [67, 71], left: [13, 42],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][42] = {
      up: [47, 14], right: [74, 74], down: [39, 124], left: [14, 71],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][43] = {
      up: [69, 15], right: [125, 36], down: [74, 71], left: [14, 47],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][44] = {
      up: [44, 10], right: [72, 50], down: [47, 115], left: [18, 54],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][45] = {
      up: [63, 14], right: [123, 48], down: [68, 70], left: [14, 33],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][46] = {
      up: [41, 12], right: [73, 59], down: [47, 117], left: [14, 71],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][47] = {
      up: [66, 9], right: [118, 37], down: [68, 72], left: [13, 36],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][48] = {
      up: [45, 14], right: [71, 63], down: [48, 120], left: [10, 62],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][49] = {
      up: [70, 13], right: null, down: [68, 69], left: [14, 36],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][50] = {
      up: [34, 16], right: [97, 47], down: [41, 73], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][51] = {
      up: [39, 12], right: [72, 56], down: [37, 118], left: [14, 66],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][52] = {
      up: [62, 14], right: [121, 42], down: [71, 70], left: [13, 35],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][53] = {
      up: [51, 14], right: [69, 63], down: [36, 122], left: [12, 68],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][54] = {
      up: [75, 12], right: [125, 48], down: [70, 73], left: [14, 38],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][55] = {
      up: [43, 12], right: [72, 64], down: [36, 121], left: [13, 73],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][56] = {
      up: [75, 13], right: [125, 42], down: [72, 71], left: [14, 37],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][57] = {
      up: [43, 12], right: [72, 59], down: [40, 120], left: [13, 67],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][58] = {
      up: [75, 14], right: [124, 48], down: [70, 75], left: [14, 38],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][59] = {
      up: [41, 13], right: null, down: [37, 121], left: [13, 70],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][60] = {
      up: [42, 13], right: [72, 68], down: [36, 121], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][61] = {
      up: [64, 13], right: [122, 44], down: [71, 74], left: [13, 44],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][62] = {
      up: [50, 14], right: [76, 73], down: [44, 125], left: [15, 69],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][63] = {
      up: [63, 14], right: [123, 39], down: [73, 72], left: [13, 45],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][64] = {
      up: [43, 12], right: [73, 66], down: [43, 119], left: [16, 62],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][65] = {
      up: [59, 14], right: [119, 47], down: [70, 70], left: [13, 43],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][66] = {
      up: [45, 13], right: [75, 62], down: [46, 120], left: [15, 69],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][67] = {
      up: [61, 15], right: [122, 49], down: [70, 72], left: [12, 39],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][68] = {
      up: [42, 12], right: [72, 60], down: [41, 119], left: [15, 71],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][69] = {
      up: [60, 15], right: null, down: [68, 71], left: [11, 39],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][70] = {
      up: [35, 14], right: [97, 47], down: [46, 69], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][71] = {
      up: [47, 11], right: [72, 59], down: [36, 121], left: [13, 68],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][72] = {
      up: [68, 13], right: [123, 43], down: [75, 73], left: [14, 38],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][73] = {
      up: [48, 14], right: [69, 58], down: [47, 117], left: [13, 67],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][74] = {
      up: [73, 12], right: [129, 49], down: [70, 69], left: [18, 33],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][75] = {
      up: [47, 13], right: [74, 67], down: [48, 122], left: [16, 77],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][76] = {
      up: [68, 14], right: [125, 50], down: [64, 72], left: [14, 43],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][77] = {
      up: [47, 14], right: [70, 62], down: [45, 122], left: [14, 74],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][78] = {
      up: [70, 13], right: [124, 50], down: [64, 70], left: [14, 37],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][79] = {
      up: [46, 13], right: null, down: [44, 120], left: [15, 75],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][80] = {
      up: [47, 15], right: [72, 69], down: [35, 123], left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][81] = {
      up: [62, 16], right: [120, 42], down: [72, 71], left: [14, 42],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][82] = {
      up: [51, 14], right: [75, 72], down: [38, 122], left: [15, 68],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][83] = {
      up: [70, 10], right: [122, 42], down: [71, 69], left: [12, 46],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][84] = {
      up: [38, 14], right: [70, 75], down: [43, 120], left: [16, 69],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][85] = {
      up: [74, 14], right: [126, 37], down: [75, 70], left: [15, 49],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][86] = {
      up: [37, 12], right: [71, 73], down: [47, 123], left: [15, 62],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][87] = {
      up: [70, 15], right: [123, 42], down: [74, 71], left: [14, 47],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][88] = {
      up: [39, 13], right: [71, 75], down: [43, 125], left: [14, 68],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][89] = {
      up: [74, 14], right: null, down: [74, 71], left: [14, 50],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][90] = {
      up: [35, 16], right: [97, 46], down: null, left: null,
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][91] = {
      up: [46, 12], right: [70, 58], down: null, left: [13, 68],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][92] = {
      up: [65, 14], right: [123, 42], down: null, left: [14, 36],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][93] = {
      up: [48, 14], right: [72, 60], down: null, left: [14, 68],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][94] = {
      up: [70, 14], right: [127, 50], down: null, left: [14, 32],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][95] = {
      up: [47, 13], right: [73, 68], down: null, left: [15, 76],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][96] = {
      up: [71, 14], right: [123, 48], down: null, left: [13, 42],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][97] = {
      up: [48, 14], right: [71, 62], down: null, left: [14, 72],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][98] = {
      up: [70, 18], right: [126, 50], down: null, left: [14, 38],
    };
    this.pieceJoint[`difficulty_${DIFFICULTY[3]}`][99] = {
      up: [47, 13], right: null, down: null, left: [16, 75],
    };
  }

  getNumberOfPieces() {
    return DIFFICULTY[this.difficulty];
  }

  getPieces() {
    return this.pieces[`difficulty_${this.getNumberOfPieces()}`];
  }

  getPieceJoints() {
    return this.pieceJoint[`difficulty_${this.getNumberOfPieces()}`];
  }

  getPieceSize() {
    return this.aMatSize[`num_${this.getNumberOfPieces()}`];
  }
};

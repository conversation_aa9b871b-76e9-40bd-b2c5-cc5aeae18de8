/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable class-methods-use-this */
/* eslint-disable no-param-reassign */
const { createjs } = window;
const LOGIC_CIRCLE_RADIUS = [35, 24, 16, 12];

const Piece = require('@/classes/puzzle/Piece');
const Utils = require('@/classes/puzzle/Utils');

module.exports = class Game {
  constructor(settings, difficulty, canvasW, canvasH, questionImage, delayToStart, responsiveMode) {
    this.settings = settings;
    this.difficulty = difficulty;
    this.library = [];
    this.delayToStart = delayToStart;
    this.inResponsiveMode = responsiveMode;

    this.canvas = document.getElementById('canvas');
    this.ctx = this.canvas.getContext('2d');

    this.stage = new createjs.Stage(this.canvas);
    createjs.Ticker.addEventListener('tick', () => this.tick(this));

    this.canvas.width = canvasW; // was a computed
    this.canvas.height = canvasH; // was a computed

    this.preloadResources(questionImage);
    this.complete = false;
    this.isLoaded = false;

    createjs.Touch.enable(this.stage);
  }

  tick(self) {
    self.stage.update();
  }

  preloadResources(questionImage) {
    const manifest = [];
    manifest.push({ id: 'board', src: require('@/assets/images/puzzle/board.png'), type: 'image' });
    const numberOfPieces = this.settings.getNumberOfPieces();
    for (let i = 0; i < numberOfPieces; i += 1) {
      manifest.push({
        id: `piece_${i}`,
        src: require(`@/assets/images/puzzle/pieces/difficulty_${this.difficulty}/piece_${i}.png`),
        type: 'image',
      });
      manifest.push({
        id: `effect_piece_${i}`,
        src: require(`@/assets/images/puzzle/pieces/difficulty_${this.difficulty}/effect_piece_${i}.png`),
        type: 'image',
      });
    }

    const question = questionImage; // ?? require('@/assets/images/puzzle/image_2.jpg');
    manifest.push({ id: 'question', src: question, type: 'image' });

    const loader = new createjs.LoadQueue(false);
    loader.loadManifest(manifest, true);
    loader.addEventListener('fileload', (e) => {
      if (e.item.type === 'image') {
        this.library[e.item.id] = e.result;
      }
    });

    loader.addEventListener('complete', () => {
      this.start();
    });
  }

  start() {
    this.pieces = [];
    this.complete = false;
    this.loadBoardBackground();

    const numberOfPieces = this.settings.getNumberOfPieces();
    const outsidePoints = this.generatePiecesOutsidePoints(numberOfPieces);

    this.pieces = this.attachImages();
    this.initJoints(this.pieces);
    this.isLoaded = true;

    setTimeout(() => {
      this.shuffleBoard(this.pieces, outsidePoints);
    }, this.delayToStart);
  }

  loadBoardBackground() {
    this.board = new createjs.Bitmap(this.library.board);
    const { x } = this.getElementPositionCentered(this.board.image.width, this.board.image.height);
    this.board.x = x;
    this.board.y = 0;
    this.stage.addChild(this.board);
  }

  getElementPositionCentered(elementWidth, elementHeight) {
    return {
      x: (this.canvas.width - elementWidth) / 2,
      y: (this.canvas.height - elementHeight) / 2,
    };
  }

  generatePiecesOutsidePoints(numberOfPieces) {
    const BOARD = {
      X: this.board.x,
      Y: this.board.y,
      WIDTH: this.board.image.width,
      HEIGHT: this.board.image.height,
    };
    const PIECE_RADIUS = 200;
    const clearWidth = (this.canvas.width - BOARD.WIDTH) / 2 - PIECE_RADIUS;
    const clearHeight = this.canvas.height - BOARD.HEIGHT - PIECE_RADIUS;

    const outsideLeftRectangle = new createjs.Rectangle(
      BOARD.X - clearWidth,
      BOARD.Y + PIECE_RADIUS,
      clearWidth,
      BOARD.HEIGHT - PIECE_RADIUS
    );
    // this.#debugPiece(outsideLeftRectangle, '#00F5');

    const outsideRightRectangle = new createjs.Rectangle(
      BOARD.X + BOARD.WIDTH,
      BOARD.Y + PIECE_RADIUS,
      clearWidth,
      BOARD.HEIGHT - PIECE_RADIUS
    );
    // this.#debugPiece(outsideRightRectangle, '#F005');

    const outsideDownRectangle = new createjs.Rectangle(
      BOARD.X - clearWidth,
      BOARD.Y + BOARD.HEIGHT,
      BOARD.WIDTH + clearWidth * 2,
      clearHeight
    );
    // this.#debugPiece(outsideDownRectangle, '#0F05');

    const points = [];
    const shapes = [outsideDownRectangle];
    if (!this.inResponsiveMode) {
      shapes.push(outsideLeftRectangle);
      shapes.push(outsideRightRectangle);
    }

    for (let i = 0; i < numberOfPieces; i += 1) {
      const shape = shapes[i % shapes.length];
      const point = this.loadPointsInsideShape(shape);
      points.push(point);
    }

    return Utils.shuffle(points);
  }

  loadPointsInsideShape(shape) {
    const { x, y, width, height } = shape;
    const randomX = Math.random() * width;
    const randomY = Math.random() * height;
    const point = { x: randomX + x, y: randomY + y };
    // this.#debugPiece({ ...point, width: 5, height: 5 }, '#000');

    return point;
  }

  #debugPiece(piece, color = '#0F0') {
    const p = new createjs.Shape();
    p.graphics.beginFill(color).drawRect(piece.x, piece.y, piece.width, piece.height);
    this.stage.addChild(p);
  }

  attachImages() {
    this.containerPieces = new createjs.Container();
    this.stage.addChild(this.containerPieces);

    const frames = this.settings.getPieces();
    const numberOfPieces = this.settings.getNumberOfPieces();
    const animations = {};
    for (let i = 0; i < numberOfPieces; i += 1) {
      animations[`frame_${i}`] = i;
    }

    const imageQ = this.library.question;
    const questionImage = new createjs.Bitmap(imageQ);

    const { image } = questionImage;

    // image.style.width = '50px'; // todo not works
    // image.style.height = '50px';// todo not works
    const dataSheet = {
      images: [image],
      frames,
      animations,
    };

    const spriteSheet = new createjs.SpriteSheet(dataSheet);
    const joints = this.settings.getPieceJoints();

    const pieces = [];
    const { rows, cols } = this.settings.getPieceSize();
    for (let i = 0; i < rows; i += 1) {
      pieces[i] = [];
      for (let j = 0; j < cols; j += 1) {
        const index = i * cols + j;
        const pieceImage = new createjs.Bitmap(this.library[`piece_${index}`]);
        const pieceEffectImage = new createjs.Bitmap(this.library[`effect_piece_${index}`]);
        const piece = new Piece(
          this.board.x + 6 + frames[index][0],
          this.board.y + 6 + frames[index][1],
          LOGIC_CIRCLE_RADIUS[this.difficulty],
          joints[index],
          index,
          pieceImage.image,
          pieceEffectImage.image,
          spriteSheet,
          this.containerPieces,
          this
        );
        pieces[i][j] = piece;
      }
    }

    return pieces;
  }

  initJoints(pieces) {
    this.activeJoints = [];
    this.jointList = [];
    const { rows, cols } = this.settings.getPieceSize();
    for (let i = 0; i < rows; i += 1) {
      for (let j = 0; j < cols; j += 1) {
        if (j + 1 < cols) {
          // SET PIECE ON THE RIGHT
          const oJoint1 = pieces[i][j].getJoint('right');
          const oJoint2 = pieces[i][j + 1].getJoint('left');
          this.jointList.push({ joint1: oJoint1, joint2: oJoint2 });
        }

        if (i + 1 < rows) {
          // SET PIECE DOWN
          const oJoint1 = pieces[i][j].getJoint('down');
          const oJoint2 = pieces[i + 1][j].getJoint('up');
          this.jointList.push({ joint1: oJoint1, joint2: oJoint2 });
        }
      }
    }
  }

  shuffleBoard(pieces, shufflePoints) {
    const { rows, cols } = this.settings.getPieceSize();
    for (let i = 0; i < rows; i += 1) {
      for (let j = 0; j < cols; j += 1) {
        const index = i * cols + j;
        pieces[i][j].moveTo(shufflePoints[index]);
      }
    }
  }

  draggingPiece(x, y, pieceDragging, jointList) {
    let iCurDepth = this.containerPieces.numChildren - 1;
    pieceDragging.setDepth(iCurDepth);

    for (let i = 0; i < jointList.length; i += 1) {
      if (pieceDragging !== jointList[i]) {
        const diff = {
          x: jointList[i].getStartingX() - pieceDragging.getStartingX(),
          y: jointList[i].getStartingY() - pieceDragging.getStartingY(),
        };
        jointList[i].draggedByOtherPiece(x, y, diff);
        iCurDepth -= 1;
        jointList[i].setDepth(iCurDepth);
      }
    }
  }

  releasePiece(piece, hitAreas) {
    // CHECK IF THERE ARE ANY JOINT
    if (this.checkCollision(piece, hitAreas) === false) {
      // playSound("place",1,false);
    }

    // CHECK IF PUZZLE IS OVER
    let numPieceAttached = 0;
    const { rows, cols } = this.settings.getPieceSize();
    for (let i = 0; i < rows; i += 1) {
      for (let j = 0; j < cols; j += 1) {
        if (this.pieces[i][j].isAttached()) {
          numPieceAttached += 1;
        }
      }
    }

    if (numPieceAttached === this.settings.getNumberOfPieces()) {
      this.finish();
    }
  }

  checkCollision(piece, hitAreas) {
    let found = false;
    const { rows, cols } = this.settings.getPieceSize();
    for (let i = 0; i < rows; i += 1) {
      for (let j = 0; j < cols; j += 1) {
        if (piece !== this.pieces[i][j] && piece.getAbsRotation() === 0 && this.pieces[i][j].getAbsRotation() === 0) {
          const oRet = this.pieces[i][j].checkCollision(hitAreas);

          if (oRet !== null) {
            const oJoint1 = oRet.joint1;
            const oJoint2 = oRet.joint2;
            if (this.findJointInList(oJoint1, oJoint2)) {
              // SNAP PIECE
              const iDiffX = this.pieces[i][j].getDiffX();
              const iDiffY = this.pieces[i][j].getDiffY();
              const aJointList = piece.snap(iDiffX, iDiffY);

              // SNAP OTHER PIECES EVENTUALLY
              for (let k = 0; k < aJointList.length; k += 1) {
                aJointList[k].snap(iDiffX, iDiffY);
              }

              // CREATE PIECE ISLAND EVENTUALLY
              this.updateActiveJoints(piece, this.pieces[i][j]);

              // REMOVE JOINTS INVOLVED IN COLLISION
              this.pieces[i][j].removeJoint(oJoint1);
              piece.removeJoint(oJoint2);

              // playSound('snap', 1, false);

              found = true;
            }
          }
        }
      }
    }

    return found;
  }

  findJointInList(oJoint1, oJoint2) {
    for (let i = 0; i < this.jointList.length; i += 1) {
      const firstCondition = oJoint1 === this.jointList[i].joint1 && oJoint2 === this.jointList[i].joint2;
      const secondCondition = oJoint1 === this.jointList[i].joint2 && oJoint2 === this.jointList[i].joint1;
      if (firstCondition || secondCondition) {
        return true;
      }
    }

    return false;
  }

  updateActiveJoints(oPieceToAttach, oPieceMaster) {
    // CHECK IF THIS PIECE IS ALREADY IN AN ACTIVE LIST
    let aListToEmpty = [oPieceToAttach];
    let found = false;
    for (let i = 0; i < this.activeJoints.length; i += 1) {
      const aList = this.activeJoints[i];
      for (let j = 0; j < aList.length; j += 1) {
        if (aList[j] === oPieceToAttach) {
          aListToEmpty = aList;
          found = true;
          break;
        }
      }

      if (found) {
        this.activeJoints.splice(i, 1);
        break;
      }
    }

    // FILL ACTIVE JOINT LIST
    for (let k = 0; k < aListToEmpty.length; k += 1) {
      found = false;
      for (let i = 0; i < this.activeJoints.length; i += 1) {
        const aList = this.activeJoints[i];
        for (let j = 0; j < aList.length; j += 1) {
          if (aList[j] === oPieceMaster) {
            this.activeJoints[i].push(aListToEmpty[k]);
            aListToEmpty[k].updateJointList(this.activeJoints[i]);
            found = true;
            break;
          }
        }
        if (found) {
          break;
        }
      }

      if (!found) {
        this.activeJoints[this.activeJoints.length] = [];
        this.activeJoints[this.activeJoints.length - 1].push(aListToEmpty[k]);
        this.activeJoints[this.activeJoints.length - 1].push(oPieceMaster);
        aListToEmpty[k].updateJointList(this.activeJoints[this.activeJoints.length - 1]);
        oPieceMaster.updateJointList(this.activeJoints[this.activeJoints.length - 1]);
      }
    }

    oPieceToAttach.setAttached(true);
    oPieceMaster.setAttached(true);
  }

  finish() {
    this.complete = true;
  }

  get isFinish() {
    return this.complete;
  }

  get isloaded() {
    return this.isLoaded;
  }
};

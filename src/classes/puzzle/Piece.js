/* eslint-disable class-methods-use-this */
/* eslint-disable no-param-reassign */
const Utils = require('@/classes/puzzle/Utils');

const { createjs } = window;

let DISTANCE_TO_CHECK;
const DEBUG = false;
const PIECE_ROTATIONS = [0, 90, 180, 270];
const JOINT_UP = 'up';
const JOINT_RIGHT = 'right';
const JOINT_DOWN = 'down';
const JOINT_LEFT = 'left';
const PRIMARY_FONT = 'Arial';
const TIME_FADE_OUT_PIECES = 1500;
module.exports = class Piece {
  constructor(x, y, radius, joints, frameNumber, pieceImage, pieceEffectImage, spriteSheet, piecesContainer, game) {
    this.game = game;
    this.piecesContainer = piecesContainer;
    // this.self = this;

    this.isActive = false;
    this.attached = false;
    this.dragging = false;
    this.index = frameNumber;
    this.radius = radius;
    const iIndexRand = Math.floor((Math.random() * PIECE_ROTATIONS.length));
    this.startingRot = PIECE_ROTATIONS[iIndexRand];
    this.joints = joints;

    this.activeJointList = [];

    this.container = new createjs.Container();
    this.container.x = x + (pieceImage.width / 2);
    this.container.y = y + (pieceImage.height / 2);
    this.container.regX = pieceImage.width / 2;
    this.container.regY = pieceImage.height / 2;
    this.container.cursor = 'pointer';
    this.piecesContainer.addChild(this.container);

    this.startingPos = { x: this.container.x, y: this.container.y };

    const mask = Utils.createBitmap(pieceImage);
    this.image = Utils.createSprite(spriteSheet, `frame_${this.index}`, 0, 0, pieceImage.width, pieceImage.height);
    this.image.filters = [
      new createjs.AlphaMaskFilter(mask.image),
    ];

    this.image.cache(0, 0, pieceImage.width, pieceImage.height);

    this.container.addChild(this.image);

    const stroke = Utils.createBitmap(pieceEffectImage);
    this.container.addChild(stroke);

    if (DEBUG) {
      this.debugText = new createjs.Text(this.index, `50px ${PRIMARY_FONT}`, 'green');
      this.debugText.x = pieceImage.width / 2;
      this.debugText.y = pieceImage.height / 2;
      this.debugText.textAlign = 'center';
      this.debugText.textBaseline = 'alphabetic';
      this.container.addChild(this.debugText);
    }

    this.initHitAreas();

    this.listeners = [];
    this.listeners.push(this.container.on('mousedown', (e) => { this.onPieceDown(e, this); }));
    this.listeners.push(this.container.on('pressmove', (e) => { this.onDragPiece(e, this); }));
    this.listeners.push(this.container.on('pressup', (e) => { this.onReleasePiece(e, this); }));
  }

  onPieceDown(e, self) {
    if (self.isActive) {
      self.mouseOffset = { x: e.stageX - self.container.x, y: e.stageY - self.container.y };
      self.prevCoord = { x: e.stageX, y: e.stageY };
    }
  }

  onDragPiece(e, self) {
    if (!self.isActive) {
      return;
    }

    if (Utils.distance({ x: e.stageX, y: e.stageY }, self.prevCoord) < 10) {
      return;
    }

    self.dragging = true;
    self.container.x = e.stageX - self.mouseOffset.x;
    self.container.y = e.stageY - self.mouseOffset.y;

    this.game.draggingPiece(self.container.x, self.container.y, self, this.activeJointList);
  }

  draggedByOtherPiece(iX, iY, pDiff) {
    this.container.x = iX + pDiff.x;
    this.container.y = iY + pDiff.y;
  }

  onReleasePiece(e, self) {
    if (!self.isActive) {
      return;
    }

    if (this.dragging) {
      this.dragging = false;
      const aHitArea = self.updateHitAreaPosition();

      this.game.releasePiece(self, aHitArea);
    } else if (!self.attached) {
      self.container.rotation += 90;
    }
  }

  unload() {
    this.container.on('mousedown', this.listeners[0]);
    this.container.on('pressmove', this.listeners[1]);
    this.container.on('pressup', this.listeners[2]);
  }

  reset(bRotActive) {
    this.container.alpha = 1;
    this.attached = false;

    if (bRotActive) {
      const iIndexRand = Math.floor((Math.random() * PIECE_ROTATIONS.length));
      this.startingRot = PIECE_ROTATIONS[iIndexRand];
    } else {
      this.startingRot = 0;
    }

    this.initHitAreas();
    this.activeJointList = [];
    this.moveTo(this.posAfterShuffling);
  }

  initHitAreas() {
    DISTANCE_TO_CHECK = this.radius * this.radius;

    this.logicCircleHitArea = [];

    if (this.joints.up !== null) {
      const iX = this.joints.up[0];

      const oRect = new createjs.Shape();
      oRect.graphics.beginFill('rgba(255,0,0,0.3)').drawCircle(iX, this.joints.up[1], this.radius);
      if (DEBUG) {
        this.container.addChild(oRect);
      }

      this.logicCircleHitArea[JOINT_UP] = { x: iX, y: this.joints.up[1], pointer: oRect };
    } else {
      this.logicCircleHitArea[JOINT_UP] = null;
    }

    if (this.joints.right !== null) {
      const iY = this.joints.right[1];

      const oRect = new createjs.Shape();
      oRect.graphics.beginFill('rgba(0,255,0,0.3)').drawCircle(this.joints.right[0], iY, this.radius);
      if (DEBUG) {
        this.container.addChild(oRect);
      }

      this.logicCircleHitArea[JOINT_RIGHT] = { x: this.joints.right[0], y: iY, pointer: oRect };
    } else {
      this.logicCircleHitArea[JOINT_RIGHT] = null;
    }

    if (this.joints.down !== null) {
      const iX = this.joints.down[0];

      const oRect = new createjs.Shape();
      oRect.graphics.beginFill('rgba(0,0,255,0.3)').drawCircle(iX, this.joints.down[1], this.radius);
      if (DEBUG) {
        this.container.addChild(oRect);
      }

      this.logicCircleHitArea[JOINT_DOWN] = { x: iX, y: this.joints.down[1], pointer: oRect };
    } else {
      this.logicCircleHitArea[JOINT_DOWN] = null;
    }

    if (this.joints.left !== null) {
      const iY = this.joints.left[1];

      const oRect = new createjs.Shape();
      oRect.graphics.beginFill('rgba(255,228,0,0.7)').drawCircle(this.joints.left[0], iY, this.radius);
      if (DEBUG) {
        this.container.addChild(oRect);
      }

      this.logicCircleHitArea[JOINT_LEFT] = { x: this.joints.left[0], y: iY, pointer: oRect };
    } else {
      this.logicCircleHitArea[JOINT_LEFT] = null;
    }
  }

  moveTo(oPoint) {
    this.posAfterShuffling = oPoint;
    createjs.Tween.get(this.container).to(
      { x: oPoint.x, y: oPoint.y, rotation: this.startingRot },
      1000,
      createjs.Ease.cubicOut,
    ).call(() => {
      this.isActive = true;
    });
  }

  setStartingPosition() {
    this.container.x = this.startingPos.x;
    this.container.y = this.startingPos.y;
    this.isActive = false;
  }

  setRotation(bRot) {
    if (this.attached) {
      return;
    }

    if (bRot) {
      this.container.rotation = this.startingRot;
    } else {
      this.container.rotation = 0;
    }
  }

  fadeOut() {
    createjs.Tween.get(this.container).to({ alpha: 0 }, TIME_FADE_OUT_PIECES);
  }

  snap(iDiffX, iDiffY) {
    this.container.x = this.startingPos.x + iDiffX;
    this.container.y = this.startingPos.y + iDiffY;

    return this.activeJointList;
  }

  checkCollision(aHitAreaToCheck) {
    const aHitArea = this.updateHitAreaPosition();
    // CALCULATE DISTANCE BETWEEN HIT AREAS
    if (aHitAreaToCheck[JOINT_UP] !== null && aHitArea[JOINT_DOWN] !== null) {
      if (Utils.distance2(aHitAreaToCheck[JOINT_UP], aHitArea[JOINT_DOWN]) < DISTANCE_TO_CHECK) {
        return { joint1: aHitArea[JOINT_DOWN].pointer, joint2: aHitAreaToCheck[JOINT_UP].pointer, dir: JOINT_UP };
      }
    }

    if (aHitAreaToCheck[JOINT_RIGHT] !== null && aHitArea[JOINT_LEFT] !== null) {
      if (Utils.distance2(aHitAreaToCheck[JOINT_RIGHT], aHitArea[JOINT_LEFT]) < DISTANCE_TO_CHECK) {
        return { joint1: aHitArea[JOINT_LEFT].pointer, joint2: aHitAreaToCheck[JOINT_RIGHT].pointer, dir: JOINT_RIGHT };
      }
    }

    if (aHitAreaToCheck[JOINT_DOWN] !== null && aHitArea[JOINT_UP] !== null) {
      if (Utils.distance2(aHitAreaToCheck[JOINT_DOWN], aHitArea[JOINT_UP]) < DISTANCE_TO_CHECK) {
        return { joint1: aHitArea[JOINT_UP].pointer, joint2: aHitAreaToCheck[JOINT_DOWN].pointer, dir: JOINT_DOWN };
      }
    }

    if (aHitAreaToCheck[JOINT_LEFT] !== null && aHitArea[JOINT_RIGHT] !== null) {
      if (Utils.distance2(aHitAreaToCheck[JOINT_LEFT], aHitArea[JOINT_RIGHT]) < DISTANCE_TO_CHECK) {
        return { joint1: aHitArea[JOINT_RIGHT].pointer, joint2: aHitAreaToCheck[JOINT_LEFT].pointer, dir: JOINT_LEFT };
      }
    }

    return null;
  }

  updateHitAreaPosition() {
    const aHitArea = [];
    if (this.logicCircleHitArea[JOINT_UP] !== null) {
      aHitArea[JOINT_UP] = {
        x: this.container.x - this.container.regX + this.logicCircleHitArea[JOINT_UP].x,
        y: this.container.y - this.container.regY + this.logicCircleHitArea[JOINT_UP].y,
        pointer: this.logicCircleHitArea[JOINT_UP].pointer,
      };
    } else {
      aHitArea[JOINT_UP] = null;
    }

    if (this.logicCircleHitArea[JOINT_RIGHT] !== null) {
      aHitArea[JOINT_RIGHT] = {
        x: this.container.x - this.container.regX + this.logicCircleHitArea[JOINT_RIGHT].x,
        y: this.container.y - this.container.regY + this.logicCircleHitArea[JOINT_RIGHT].y,
        pointer: this.logicCircleHitArea[JOINT_RIGHT].pointer,
      };
    } else {
      aHitArea[JOINT_RIGHT] = null;
    }

    if (this.logicCircleHitArea[JOINT_DOWN] !== null) {
      aHitArea[JOINT_DOWN] = {
        x: this.container.x - this.container.regX + this.logicCircleHitArea[JOINT_DOWN].x,
        y: this.container.y - this.container.regY + this.logicCircleHitArea[JOINT_DOWN].y,
        pointer: this.logicCircleHitArea[JOINT_DOWN].pointer,
      };
    } else {
      aHitArea[JOINT_DOWN] = null;
    }

    if (this.logicCircleHitArea[JOINT_LEFT] !== null) {
      aHitArea[JOINT_LEFT] = {
        x: this.container.x - this.container.regX + this.logicCircleHitArea[JOINT_LEFT].x,
        y: this.container.y - this.container.regY + this.logicCircleHitArea[JOINT_LEFT].y,
        pointer: this.logicCircleHitArea[JOINT_LEFT].pointer,
      };
    } else {
      aHitArea[JOINT_LEFT] = null;
    }

    return aHitArea;
  }

  removeJoint(oJointToRemove) {
    // eslint-disable-next-line no-restricted-syntax
    for (const i in this.logicCircleHitArea) {
      if (this.logicCircleHitArea[i] !== null && this.logicCircleHitArea[i].pointer === oJointToRemove) {
        this.container.removeChild(this.logicCircleHitArea[i].pointer);
        this.logicCircleHitArea[i] = null;
        break;
      }
    }
  }

  setAttached(bValue) {
    this.attached = bValue;
  }

  updateJointList(aNewList) {
    this.activeJointList = aNewList;
  }

  setDepth(iDepth) {
    this.piecesContainer.setChildIndex(this.container, iDepth);
  }

  getHitAreas() {
    return this.logicCircleHitArea;
  }

  getJoint(szType) {
    return this.logicCircleHitArea[szType].pointer;
  }

  getX() {
    return this.container.x;
  }

  getY() {
    return this.container.y;
  }

  getDiffX() {
    return this.container.x - this.startingPos.x;
  }

  getDiffY() {
    return this.container.y - this.startingPos.y;
  }

  getStartingX() {
    return this.startingPos.x;
  }

  getStartingY() {
    return this.startingPos.y;
  }

  getStartingPos() {
    return this.startingPos;
  }

  getPos() {
    return { x: this.container.x, y: this.container.y };
  }

  isAttached() {
    return this.attached;
  }

  getIndex() {
    return this.index;
  }

  getAbsRotation() {
    return this.container.rotation % 360 === 0 ? 0 : this.container.rotation;
  }
};

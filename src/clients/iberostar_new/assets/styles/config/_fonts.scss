$fonts-path: '~@/clients/iberostar_new/assets/fonts';

@font-face {
  font-family: 'OpenSans';
  src: url('#{$fonts-path}/openSans/OpenSans-Light.ttf') format('opentype');
  font-weight: #{$weight-light};
  font-style: normal;
}

@font-face {
  font-family: 'OpenSans';
  src: url('#{$fonts-path}/openSans/OpenSans-LightItalic.ttf') format('opentype');
  font-weight: #{$weight-light};
  font-style: italic;
}

// Regular
@font-face {
  font-family: 'OpenSans';
  src: url('#{$fonts-path}/openSans/OpenSans-Regular.ttf') format('opentype');
  font-weight: #{$weight-normal};
  font-style: normal;
}
@font-face {
  font-family: 'OpenSans';
  src: url('#{$fonts-path}/openSans/OpenSans-Italic.ttf') format('opentype');
  font-weight: #{$weight-normal};
  font-style: italic;
}

// SemiBold
@font-face {
  font-family: 'OpenSans';
  src: url('#{$fonts-path}/openSans/OpenSans-SemiBold.ttf') format('opentype');
  font-weight: #{$weight-semi-bold};
  font-style: normal;
}

@font-face {
  font-family: 'OpenSans';
  src: url('#{$fonts-path}/openSans/OpenSans-SemiBoldItalic.ttf') format('opentype');
  font-weight: #{$weight-semi-bold};
  font-style: italic;
}

// Bold
@font-face {
  font-family: 'OpenSans';
  src: url('#{$fonts-path}/openSans/OpenSans-Bold.ttf') format('opentype');
  font-weight: #{$weight-bold};
  font-style: normal;
}

@font-face {
  font-family: 'OpenSans';
  src: url('#{$fonts-path}/openSans/OpenSans-BoldItalic.ttf') format('opentype');
  font-weight: #{$weight-bold};
  font-style: italic;
}


$font-family-primary: 'OpenSans', sans-serif;

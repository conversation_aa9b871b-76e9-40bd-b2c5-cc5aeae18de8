$color-primary: hsl(194, 97%, 48%);
$color-primary-darkest: adjust-color($color-primary, $lightness: -30%);
$color-primary-darker: adjust-color($color-primary, $lightness: -20%);
$color-primary-dark: adjust-color($color-primary, $lightness: -10%);
$color-primary-light: adjust-color($color-primary, $lightness: 15%);
$color-primary-lighter: adjust-color($color-primary, $lightness: 35%);
$color-primary-lightest: adjust-color($color-primary, $lightness: 45%);

import axios from 'axios';
import { getToken, setToken, setRefreshToken, getRefreshToken } from '../utils/token';

const REFRESH_TOKEN_ENDPOINT = '/token/refresh';
axios.defaults.baseURL = `${window.location.origin}/api`;

function manageHeaders(config) {
  const token = getToken();
  const configuration = config;

  if (token) {
    configuration.headers.Authorization = `Bearer ${token}`;
  }

  return configuration;
}

axios.interceptors.request.use(
  (config) => manageHeaders(config),
  (error) => Promise.reject(error)
);

let listeners = [];
let isFetchingRefreshToken = false;

function addListener(callback) {
  listeners.push(callback);
}

function onAccessTokenFetched(accessToken = null) {
  listeners.forEach((callback) => callback(accessToken));
  listeners = [];
}

axios.interceptors.response.use(
  (response) => response.data,
  async (error) => {
    const { config, response } = error;
    if (response && response.status === 401) {
      const originalRequest = config;

      if (originalRequest.url.includes(REFRESH_TOKEN_ENDPOINT)) {
        console.log('DEBUG - response interceptor: Refresh token request itself failed. Aborting.');
        return Promise.reject(error);
      }

      if (!isFetchingRefreshToken) {
        isFetchingRefreshToken = true;
        const refreshToken = getRefreshToken();

        if (!refreshToken) {
          console.log('DEBUG - response interceptor: No refresh token available. Cannot refresh access token.');
          return Promise.reject(error);
        }

        try {
          const tokens = await axios.post(
            REFRESH_TOKEN_ENDPOINT,
            {
              refresh_token: refreshToken,
            },
            {
              ...originalRequest,
              headers: {
                'Content-Type': 'application/json',
              },
            }
          );
          setToken(tokens.token);
          setRefreshToken(tokens.refresh_token);
          isFetchingRefreshToken = false;
          onAccessTokenFetched(tokens.token);
        } catch (err) {
          isFetchingRefreshToken = false;
          console.log('DEBUG - response interceptor: Refresh token request failed.');
          return Promise.reject(error);
        }
      }

      return new Promise((resolve) => {
        addListener((accessToken) => {
          if (accessToken) {
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          } else {
            console.log('DEBUG - Queued request: No access token available for retry.');
          }
          resolve(axios(originalRequest));
        });
      });
    }

    console.log('DEBUG - response interceptor: Response error was not 401, rejecting error.');
    return Promise.reject(error);
  }
);

export default axios;

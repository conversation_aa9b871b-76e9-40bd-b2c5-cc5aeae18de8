{"name": "games", "version": "2.0.1", "private": true, "scripts": {"serve": "yarn locales && vue-cli-service serve", "serve:offline": "cross-env VUE_APP_CLIENT=easylearning yarn serve --mode=offline", "serve:dev": "yarn serve --mode development", "serve:pre": "yarn serve --mode preproduction", "build": "node ./scripts/build", "multi-build": "node ./scripts/multi-build", "test:unit": "yarn vue-cli-service test:unit", "lint": "vue-cli-service lint", "locales": "node ./scripts/download-locales.js", "i18n:report": "vue-cli-service i18n:report --src \"./src/**/*.?(js|vue)\" --locales \"./src/locales/**/*.json\"", "release": "standard-version"}, "engines": {"node": "^16.0.0"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.2.0", "@fortawesome/free-regular-svg-icons": "^6.2.0", "@fortawesome/free-solid-svg-icons": "^6.2.0", "@fortawesome/vue-fontawesome": "^2.0.8", "axios": "^0.20.0", "core-js": "^3.6.5", "createjs": "^1.0.1", "vue": "^2.6.11", "vue-i18n": "^8.24.3", "vue-router": "^3.2.0", "vue-smooth-dnd": "^0.8.1", "vue-vimeo-player": "^0.1.2", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vuex-pathify": "^1.4.1"}, "devDependencies": {"@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@faker-js/faker": "^7.3.0", "@vue/cli-plugin-babel": "~4.2.0", "@vue/cli-plugin-eslint": "~4.2.0", "@vue/cli-plugin-router": "~4.2.0", "@vue/cli-plugin-unit-jest": "~4.2.0", "@vue/cli-plugin-vuex": "~4.2.0", "@vue/cli-service": "~4.2.0", "@vue/eslint-config-airbnb": "^5.1.0", "@vue/test-utils": "1.0.0-beta.31", "babel-eslint": "^10.0.3", "babel-jest": "^25.1.0", "conventional-changelog-cli": "^4.1.0", "cross-env": "^7.0.3", "dotenv": "^16.3.1", "eslint": "^6.7.2", "eslint-plugin-import": "^2.22.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^6.1.2", "husky": "^8.0.3", "inquirer": "^8.0.0", "inquirer-autocomplete-prompt": "^2.0.0", "jest": "^25.1.0", "miragejs": "^0.1.45", "null-loader": "^4.0.1", "prettier": "^2.8.7", "sass": "^1.26.10", "sass-loader": "^8.0.2", "standard-version": "^9.5.0", "vue-cli-plugin-i18n": "~2.1.0", "vue-template-compiler": "^2.6.11"}}
openapi: '3.0.2'
info:
  title: Easylearning Games API
  version: '1.0'
# servers:
#   - url: https://api.server.test/v1
paths:
  /api/chapter/{id}/start:
    get:
      tags:
        - 'Quiz'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'Quiz'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  properties:
                                    id:
                                      type: integer
                                      example: 1
                                    questionId:
                                      type: integer
                                      example: 1
                                    correct:
                                      type: boolean
                                      example: true
                                    time:
                                      type: integer
                                      example: 5

                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/{id}/questions:
    get:
      tags:
        - 'Quiz'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: "Devuelve las **preguntas** con sus respectivas opciones de respuesta.\n\nEl tiempo en la pregunta es opcional, si no hay un tiempo global se usa el de cada pregunta"
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              example: 1
                            question:
                              type: string
                            imageUrl:
                              type: string
                            random:
                              type: boolean
                            time:
                              type: number
                              example: 60
                            answers:
                              type: array
                              items:
                                properties:
                                  id:
                                    type: integer
                                    example: 1
                                  answer:
                                    type: string
                            feedback:
                              properties:
                                active:
                                  type: boolean
                                  example: true
                                correct:
                                  type: string
                                  example: La respuesta es correcta
                                incorrect:
                                  type: string
                                  example: La respuesta es incorrecta
                      time:
                        type: number
                        example: 150

  /api/chapters/answer/{id}/check:
    post:
      tags:
        - 'Quiz'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: 'Respuesta que se quiere comprobar. **En caso de terminarse el tiempo** y no contestar, **el id** (id de la respuesta) **no se enviará**'
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  example: 2
                questionId:
                  type: integer
                  example: 1
                time:
                  type: integer
                  example: 10.4
      responses:
        '200':
          description: 'Devuelve si es correcta o incorrecta'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      correct:
                        type: boolean

  /api/chapter/{id}/update:
    post:
      tags:
        - 'Quiz'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          id:
                            type: integer
                            example: 1
                          questionId:
                            type: integer
                            example: 1
                          time:
                            type: integer
                            example: 3.0005
                          correct:
                            type: boolean
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers: [{ id: 1, questionId: 1, time: 3.0005, correct: true }]
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/2/start:
    get:
      tags:
        - 'Roulette'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'Roulette'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              sections:
                                type: array
                                items:
                                  type: boolean
                                example: [true, true, false]
                              answers:
                                type: array
                                items:
                                  properties:
                                    id:
                                      type: integer
                                      example: 1
                                    questionId:
                                      type: integer
                                      example: 1
                                    correct:
                                      type: boolean
                                      example: true
                                    time:
                                      type: integer
                                      example: 5

                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/2/questions:
    get:
      tags:
        - 'Roulette'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve las **preguntas** con sus respectivas opciones de respuesta.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              example: 1
                            question:
                              type: string
                            imageUrl:
                              type: string
                            random:
                              type: boolean
                            time:
                              type: number
                              example: 60
                            answers:
                              type: array
                              items:
                                properties:
                                  id:
                                    type: integer
                                    example: 1
                                  answer:
                                    type: string
                      time:
                        type: number
                        example: 150

  /api/chapters/answer/2/check:
    post:
      tags:
        - 'Roulette'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: 'Respuesta que se quiere comprobar. **En caso de terminarse el tiempo** y no contestar, **el id** (id de la respuesta) **no se enviará**'
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  example: 2
                questionId:
                  type: integer
                  example: 1
                time:
                  type: integer
                  example: 10.4
      responses:
        '200':
          description: 'Devuelve si es correcta o incorrecta'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      correct:
                        type: boolean

  /api/chapter/2/update:
    post:
      tags:
        - 'Roulette'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    sections:
                      type: array
                      items:
                        type: boolean
                      example: [true, true, false]
                    answers:
                      type: array
                      items:
                        properties:
                          id:
                            type: integer
                            example: 1
                          questionId:
                            type: integer
                            example: 1
                          time:
                            type: integer
                            example: 3.0005
                          correct:
                            type: boolean
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers: [{ id: 1, questionId: 1, time: 3.0005, correct: true }]
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/3/start:
    get:
      tags:
        - 'Puzzle'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'Puzzle'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  properties:
                                    id:
                                      type: integer
                                      example: 1
                                    questionId:
                                      type: integer
                                      example: 1
                                    correct:
                                      type: boolean
                                      example: true
                                    time:
                                      type: integer
                                      example: 5

                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/3/questions:
    get:
      tags:
        - 'Puzzle'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve las **preguntas** con sus respectivas opciones de respuesta.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      image:
                        type: string
                      times:
                        type: array
                        example: [60, 60, 60, 60]
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              example: 1
                            question:
                              type: string
                            imageUrl:
                              type: string
                            random:
                              type: boolean
                            time:
                              type: number
                              example: 60
                            answers:
                              type: array
                              items:
                                properties:
                                  id:
                                    type: integer
                                    example: 1
                                  answer:
                                    type: string
                      time:
                        type: number
                        example: 150

  /api/chapters/answer/3/check:
    post:
      tags:
        - 'Puzzle'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: 'Respuesta que se quiere comprobar. **En caso de terminarse el tiempo** y no contestar, **el id** (id de la respuesta) **no se enviará**'
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  example: 2
                questionId:
                  type: integer
                  example: 1
                time:
                  type: integer
                  example: 10.4
      responses:
        '200':
          description: 'Devuelve si es correcta o incorrecta'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      correct:
                        type: boolean

  /api/chapter/3/update:
    post:
      tags:
        - 'Puzzle'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```\n\n El atributo **remainingTime** hace referencia a la suma de las barras de tiempo restantes sin contar con el tiempo de la pregunta actual ni la barra actual de tiempo."
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          id:
                            type: integer
                          questionId:
                            type: integer
                          time:
                            type: integer
                          correct:
                            type: boolean
                    remainingTime:
                      type: integer
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers: [{ id: 1, questionId: 1, time: 3.0005, correct: true }]
                    remainingTime: 180
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/4/start:
    get:
      tags:
        - 'HiddenWords'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'HiddenWords'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  properties:
                                    questionId:
                                      type: integer
                                      example: 1
                                    correct:
                                      type: boolean
                                      example: true
                                    time:
                                      type: integer
                                      example: 5
                                    attempts:
                                      type: array
                                      items:
                                        properties:
                                          letter:
                                            type: string
                                            example: 'a'
                                          correct:
                                            type: boolean
                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/4/questions:
    get:
      tags:
        - 'HiddenWords'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: "Devuelve las **preguntas** con sus respectivas opciones de respuesta. \n\nEl atributo **_clues_** es un array con las posiciones de las letras visibles a modo de pista"
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              example: 1
                            clues:
                              type: array
                              example: [0, 2]
                            question:
                              type: string
                            imageUrl:
                              type: string
                            time:
                              type: number
                              example: 60
                            answers:
                              type: array
                              items:
                                properties:
                                  id:
                                    type: integer
                                    example: 1
                                  answer:
                                    type: string

  /api/chapters/answer/4/check:
    post:
      tags:
        - 'HiddenWords'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: 'Respuesta que se quiere comprobar.'
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                attempts:
                  type: array
                  items:
                    type: object
                    properties:
                      letter:
                        type: string
                        example: 'a'
                      correct:
                        type: boolean
                questionId:
                  type: integer
                  example: 1
                time:
                  type: integer
                  example: 10.4
      responses:
        '200':
          description: 'Devuelve si es correcta o incorrecta'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      result:
                        type: object
                        properties:
                          corrects:
                            type: integer
                            example: 2
                          incorrects:
                            type: integer
                            example: 1

  /api/chapter/4/update:
    post:
      tags:
        - 'HiddenWords'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          id:
                            type: integer
                          questionId:
                            type: integer
                          time:
                            type: integer
                          correct:
                            type: boolean
                          corrects:
                            type: integer
                          incorrects:
                            type: integer
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers: [{ id: 1, questionId: 1, time: 3.0005, correct: true, corrects: 2, incorrects: 1 }]
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/5/start:
    get:
      tags:
        - 'DoubleOrNothing'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'DoubleOrNothing'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              attempts:
                                type: array
                                items:
                                  type: array
                                  items:
                                    type: object
                                    properties:
                                      id:
                                        type: integer
                                        example: 1
                                      questionId:
                                        type: integer
                                        example: 1
                                      correct:
                                        type: boolean
                                        example: true
                                      time:
                                        type: integer
                                        example: 5
                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/5/questions:
    get:
      tags:
        - 'DoubleOrNothing'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve las **preguntas** con sus respectivas opciones de respuesta.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              example: 1
                            question:
                              type: string
                            imageUrl:
                              type: string
                            random:
                              type: boolean
                            time:
                              type: number
                              example: 60
                            answers:
                              type: array
                              items:
                                properties:
                                  id:
                                    type: integer
                                    example: 1
                                  answer:
                                    type: string

  /api/chapters/answer/5/check:
    post:
      tags:
        - 'DoubleOrNothing'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: 'Respuesta que se quiere comprobar. **En caso de terminarse el tiempo** y no contestar, **el id** (id de la respuesta) **no se enviará**'
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  example: 2
                questionId:
                  type: integer
                  example: 1
                time:
                  type: integer
                  example: 10.4
      responses:
        '200':
          description: 'Devuelve si es correcta o incorrecta'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      correct:
                        type: boolean

  /api/chapter/5/update:
    post:
      tags:
        - 'DoubleOrNothing'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          id:
                            type: integer
                            example: 1
                          questionId:
                            type: integer
                            example: 1
                          time:
                            type: integer
                            example: 3.0005
                          correct:
                            type: boolean
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers: [{ id: 1, questionId: 1, time: 3.0005, correct: true }]
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/6/start:
    get:
      tags:
        - 'LettersWheel'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'LettersWheel'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    value:
                                      type: string
                                    questionId:
                                      type: integer
                                      example: 1
                                    correct:
                                      type: boolean
                                      example: true
                                    time:
                                      type: integer
                                      example: 5
                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/6/questions:
    get:
      tags:
        - 'LettersWheel'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve las **preguntas** con las letras a las que estan vinculadas.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              example: 1
                            question:
                              type: string
                            letter:
                              type: string

  /api/chapters/answer/6/check:
    post:
      tags:
        - 'LettersWheel'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: 'Respuesta que se quiere comprobar.'
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                questionId:
                  type: integer
                  example: 1
                value:
                  type: string
      responses:
        '200':
          description: 'Devuelve si es correcta o incorrecta'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      correct:
                        type: boolean

  /api/chapter/6/update:
    post:
      tags:
        - 'LettersWheel'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          id:
                            type: integer
                            example: 1
                          questionId:
                            type: integer
                            example: 1
                          time:
                            type: integer
                            example: 3.0005
                          correct:
                            type: boolean
                    time:
                      type: integer
                      example: 5
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers: [{ questionId: 1, time: 3.0005, correct: true, value: 'araña' }]
                    time: 5
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/7/start:
    get:
      tags:
        - 'TrueOrFalse'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'TrueOrFalse'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    id:
                                      type: integer
                                      example: 1
                                    questionId:
                                      type: integer
                                      example: 1
                                    correct:
                                      type: boolean
                                      example: true
                                    time:
                                      type: integer
                                      example: 5
                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/7/questions:
    get:
      tags:
        - 'TrueOrFalse'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: "Devuelve las **preguntas** con sus respectivas opciones de respuesta.\n\nComo antes estaban unidos **TrueOrFalse** y **Categorized** el campo **_categorized_** hace referencia a eso: \n\n- Las de **_verdadero o falso_** ```(categorized: false)``` las cuales únicamente tienen dos opciones. En las opciones no es necesario tener una imagen (**imageUrl**)\n\n- Las de **_categorias_** ```(categorized: true)``` las cuales pueden ser más de dos.\n\nEn ambos casos en las opciones de las preguntas el texto y la imagen son opcionales, pero siempre tiene que haber uno de los dos como mínimo"
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              example: 1
                            text:
                              type: string
                            categorized:
                              type: boolean
                              example: false
                            imageUrl:
                              type: string
                            answers:
                              type: array
                              items:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                    example: 1
                                  answer:
                                    type: string
                                  image:
                                    type: string
                      time:
                        type: integer
                        example: 120

  /api/chapters/answer/7/check:
    post:
      tags:
        - 'TrueOrFalse'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: 'Respuesta que se quiere comprobar.'
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  example: 1
                questionId:
                  type: integer
                  example: 1
                time:
                  type: integer
                  example: 10
      responses:
        '200':
          description: 'Devuelve si es correcta o incorrecta'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      correct:
                        type: boolean

  /api/chapter/7/update:
    post:
      tags:
        - 'TrueOrFalse'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          id:
                            type: integer
                            example: 1
                          questionId:
                            type: integer
                            example: 1
                          time:
                            type: integer
                            example: 3.0005
                          correct:
                            type: boolean
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers: [{ id: 1, questionId: 1, time: 3.0005, correct: true }]
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/15/start:
    get:
      tags:
        - 'Categorized'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'TrueOrFalse'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    id:
                                      type: integer
                                      example: 1
                                    questionId:
                                      type: integer
                                      example: 1
                                    correct:
                                      type: boolean
                                      example: true
                                    time:
                                      type: integer
                                      example: 5
                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/15/questions:
    get:
      tags:
        - 'Categorized'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: "Devuelve las **preguntas** con sus respectivas opciones de respuesta.\n\nComo antes estaban unidos **TrueOrFalse** y **Categorized** el campo **_categorized_** hace referencia a eso: \n\n- Las de **_verdadero o falso_** ```(categorized: false)``` las cuales únicamente tienen dos opciones. En las opciones no es necesario tener una imagen (**imageUrl**)\n\n- Las de **_categorias_** ```(categorized: true)``` las cuales pueden ser más de dos.\n\nEn ambos casos en las opciones de las preguntas el texto y la imagen son opcionales, pero siempre tiene que haber uno de los dos como mínimo"
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              example: 1
                            text:
                              type: string
                            categorized:
                              type: boolean
                            imageUrl:
                              type: string
                            answers:
                              type: array
                              items:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                    example: 1
                                  answer:
                                    type: string
                                  image:
                                    type: string
                      time:
                        type: integer
                        example: 120

  /api/chapters/answer/15/check:
    post:
      tags:
        - 'Categorized'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: 'Respuesta que se quiere comprobar.'
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  example: 1
                questionId:
                  type: integer
                  example: 1
                time:
                  type: integer
                  example: 10
      responses:
        '200':
          description: 'Devuelve si es correcta o incorrecta'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      correct:
                        type: boolean

  /api/chapter/15/update:
    post:
      tags:
        - 'Categorized'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          id:
                            type: integer
                            example: 1
                          questionId:
                            type: integer
                            example: 1
                          time:
                            type: integer
                            example: 3.0005
                          correct:
                            type: boolean
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers: [{ id: 1, questionId: 1, time: 3.0005, correct: true }]
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/8/start:
    get:
      tags:
        - 'FillGaps'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'FillGaps'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    questionId:
                                      type: integer
                                      example: 1
                                    attempts:
                                      type: array
                                      items:
                                        properties:
                                          id:
                                            type: number
                                            example: 1
                                          word:
                                            type: string
                                          correct:
                                            type: boolean
                                    time:
                                      type: integer
                                      example: 5
                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/8/questions:
    get:
      tags:
        - 'FillGaps'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve las **preguntas** con sus respectivas opciones de respuesta.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              example: 1
                            text:
                              type: string
                            answers:
                              type: array
                              items:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                    example: 1
                                  answer:
                                    type: string
                      time:
                        type: integer
                        example: 120

  /api/chapters/answer/8/check:
    post:
      tags:
        - 'FillGaps'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: 'Respuestas que se quieren comprobar. En los **attempts** se envía el **_id_** del hueco que se ha rellenado con la respuesta **_word_**'
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                questionId:
                  type: integer
                  example: 1
                attempts:
                  type: array
                  items:
                    properties:
                      id:
                        type: integer
                        example: 1
                      word:
                        type: string
                time:
                  type: integer
                  example: 10
      responses:
        '200':
          description: 'Devuelve si cada palabra es correcta o incorrecta'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      attempts:
                        type: array
                        items:
                          properties:
                            id:
                              type: number
                              example: 1
                            correct:
                              type: boolean

  /api/chapter/8/update:
    post:
      tags:
        - 'FillGaps'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          questionId:
                            type: integer
                            example: 1
                          attempts:
                            type: array
                            items:
                              properties:
                                id:
                                  type: number
                                  example: 1
                                word:
                                  type: string
                                correct:
                                  type: boolean
                          time:
                            type: integer
                            example: 3.0005
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers:
                      [{ id: 1, questionId: 1, attempts: [{ id: 1, word: 'gato', correct: false }], time: 3.0005 }]
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/9/start:
    get:
      tags:
        - 'HiddenPic'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'HiddenPic'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    questionId:
                                      type: integer
                                      example: 1
                                    value:
                                      type: string
                                    correct:
                                      type: boolean
                                    time:
                                      type: integer
                                      example: 5
                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/9/questions:
    get:
      tags:
        - 'HiddenPic'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve las **preguntas** con sus respectivas opciones de respuesta.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              example: 1
                            title:
                              type: string
                            image:
                              type: string
                            clue:
                              type: string
                            words:
                              type: string
                              example: 'Torre Eiffel'
                            time:
                              type: number
                              example: 10

  /api/chapters/answer/9/check:
    post:
      tags:
        - 'HiddenPic'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: 'Respuestas que se quieren comprobar.'
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                questionId:
                  type: integer
                  example: 1
                value:
                  type: string
                  example: 'torre eiffel'
                time:
                  type: integer
                  example: 5
      responses:
        '200':
          description: 'Devuelve si cada palabra es correcta o incorrecta'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      correct:
                        type: boolean

  /api/chapter/9/update:
    post:
      tags:
        - 'HiddenPic'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          questionId:
                            type: number
                            example: 1
                          value:
                            type: string
                          correct:
                            type: boolean
                          time:
                            type: integer
                            example: 3.0005
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers:
                      questionId: 1
                      value: 'torre eiffel'
                      correct: true
                      time: 5
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/10/start:
    get:
      tags:
        - 'MemoryMatch'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'MemoryMatch'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    cards:
                                      type: array
                                      example: [1, 2]
                                    time:
                                      type: integer
                                      example: 5
                                    correct:
                                      type: boolean
                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/10/questions:
    get:
      tags:
        - 'MemoryMatch'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve las **cartas** con sus propiedades.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              example: 1
                            image:
                              type: string
                            text:
                              type: string

  /api/chapters/answer/10/check:
    post:
      tags:
        - 'MemoryMatch'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: '**Ids** de cards que se quieren comprobar.'
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                cards:
                  type: array
                  items:
                    example: [1, 2]
                time:
                  type: integer
                  example: 5
      responses:
        '200':
          description: 'Devuelve si son iguales las parejas'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      correct:
                        type: boolean

  /api/chapter/10/update:
    post:
      tags:
        - 'MemoryMatch'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          cards:
                            type: array
                            example: [1, 2]
                          correct:
                            type: boolean
                          time:
                            type: integer
                            example: 3.0005
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers: [{ cards: [1, 2], correct: true, time: 5 }]
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/11/start:
    get:
      tags:
        - 'HigherLower'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'HigherLower'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    ids:
                                      type: array
                                      items:
                                        properties:
                                          id:
                                            type: number
                                            example: 1
                                          correct:
                                            type: boolean
                                    time:
                                      type: integer
                                      example: 5
                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/11/questions:
    get:
      tags:
        - 'HigherLower'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve las **questions** con sus propiedades.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      title:
                        type: string
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: number
                              example: 1
                            correct:
                              type: boolean
                      time:
                        type: number
                        example: 10

  /api/chapters/answer/11/check:
    post:
      tags:
        - 'HigherLower'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: '**Ids** de las palabras que se quieren comprobar en orden.'
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                attempt:
                  type: array
                  items:
                    properties:
                      ids:
                        type: array
                        example: [1, 2, 3, 4, 5, 6]
      responses:
        '200':
          description: 'Devuelve si son iguales las parejas'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      attempt:
                        type: array
                        items:
                          properties:
                            id:
                              type: number
                              example: 1
                            correct:
                              type: boolean
                      time:
                        type: number

  /api/chapter/11/update:
    post:
      tags:
        - 'HigherLower'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          attempt:
                            type: array
                            items:
                              properties:
                                id:
                                  type: number
                                  example: 1
                                correct:
                                  type: boolean
                          time:
                            type: integer
                            example: 3.0005
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers: [{ attempt: [{ id: 1, correct: false }], time: 5 }]
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/12/start:
    get:
      tags:
        - 'GuessWord'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'GuessWord'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    questionId:
                                      type: number
                                      example: 1
                                    attempts:
                                      type: array
                                      items:
                                        properties:
                                          word:
                                            type: string
                                          time:
                                            type: number
                                            example: 2
                                          correct:
                                            type: boolean
                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/12/questions:
    get:
      tags:
        - 'GuessWord'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve las **questions** con sus propiedades. Las palabras vendran desordenadas desde el backoffice'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: number
                              example: 1
                            question:
                              type: string
                            time:
                              type: number
                              example: 5
                            word:
                              type: string

  /api/chapters/answer/12/check:
    post:
      tags:
        - 'GuessWord'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: 'Palabra que se quiere comprobar. En caso de que se haya finalizado el tiempo, se enviara sin el campo **word**'
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                questionId:
                  type: number
                  example: 1
                word:
                  type: string
                time:
                  type: number
                  example: 2
      responses:
        '200':
          description: 'Devuelve si es correcta la palabra enviada'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      correct:
                        type: boolean

  /api/chapter/12/update:
    post:
      tags:
        - 'GuessWord'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          questionId:
                            type: integer
                            example: 1
                          attempts:
                            type: array
                            items:
                              properties:
                                word:
                                  type: string
                                time:
                                  type: number
                                  example: 2
                                correct:
                                  type: boolean
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers: [{ questionId: 1, attempts: [{ word: 'perro', time: 2, correct: true }] }]
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/13/start:
    get:
      tags:
        - 'Wordle'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'Wordle'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    questionId:
                                      type: number
                                      example: 1
                                    attempts:
                                      type: array
                                      items:
                                        properties:
                                          word:
                                            type: string
                                          time:
                                            type: number
                                            example: 2
                                          correct:
                                            type: boolean
                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/13/questions:
    get:
      tags:
        - 'Wordle'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve las **questions** con sus propiedades. EL campo **letters** hace referencia a la cantidad de letras que tiene la palabra que se tiene que advinar'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: number
                              example: 1
                            question:
                              type: string
                            letters:
                              type: number
                              example: 5
                            time:
                              type: number
                              example: 5

  /api/chapters/answer/13/check:
    post:
      tags:
        - 'Wordle'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Palabra que se quiere comprobar. En caso de que se haya finalizado el tiempo, se enviara sin el campo **word**.\n\nEn la respuesta, el campo **correct** será _true_ cuando sea la misma letra en esa posición. Por otro lado el campo **present** será _true_ cuando exista la letra pero no en esa posición"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                questionId:
                  type: number
                  example: 1
                word:
                  type: string
                time:
                  type: number
                  example: 2
      responses:
        '200':
          description: 'Devuelve si es correcta la palabra enviada'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  error:
                    type: number
                  data:
                    type: object
                    properties:
                      letters:
                        type: array
                        items:
                          properties:
                            letter:
                              type: string
                            correct:
                              type: boolean
                            present:
                              type: boolean

  /api/chapter/13/update:
    post:
      tags:
        - 'Wordle'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          questionId:
                            type: integer
                            example: 1
                          attempts:
                            type: array
                            items:
                              properties:
                                letters:
                                  type: array
                                  items:
                                    properties:
                                      letter:
                                        type: string
                                      correct:
                                        type: boolean
                                      present:
                                        type: boolean
                                time:
                                  type: number
                                  example: 7
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers:
                      [
                        {
                          questionId: 1,
                          attempts: [{ letters: [{ letter: 'a', correct: false, present: true }], time: 2 }],
                        },
                      ]
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/14/start:
    get:
      tags:
        - 'SearchWord'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'SearchWord'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    questionId:
                                      type: number
                                      example: 1
                                    attempts:
                                      type: array
                                      items:
                                        properties:
                                          word:
                                            type: string
                                          time:
                                            type: number
                                            example: 2
                                          correct:
                                            type: boolean
                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/14/questions:
    get:
      tags:
        - 'SearchWord'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve las **questions** con sus propiedades.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: number
                              example: 1
                            question:
                              type: string
                            words:
                              type: array
                              example: ['guitarra', 'bajo']
                            time:
                              type: number
                              example: 50

  /api/chapter/14/update:
    post:
      tags:
        - 'SearchWord'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        type: object
                        properties:
                          questionId:
                            type: number
                            example: 1
                          attempts:
                            type: array
                            items:
                              properties:
                                word:
                                  type: string
                                time:
                                  type: number
                                  example: 2
                                correct:
                                  type: boolean
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers: [{ questionId: 1, attempts: [{ word: 'guitarra', time: 5, correct: false }] }]
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

  /api/chapter/16/start:
    get:
      tags:
        - 'VideoQuiz'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: 'Devuelve los datos necesarios para comenzar el juego. Dentro del capitulo, en el campo **data** devuelve los datos de partidas anteriores en caso de que existan.'
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      chapter:
                        type: object
                        properties:
                          chapter:
                            type: object
                            properties:
                              type:
                                type: object
                                properties:
                                  normalizedName:
                                    type: string
                                    example: 'VideoQuiz'
                          finishedAt:
                            type: string
                            example: null
                          id:
                            type: integer
                            example: 1
                          maxPoints:
                            type: integer
                            example: 250
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          updatedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                          data:
                            type: object
                            properties:
                              answers:
                                type: array
                                items:
                                  properties:
                                    id:
                                      type: integer
                                      example: 1
                                    questionId:
                                      type: integer
                                      example: 1
                                    correct:
                                      type: boolean
                                      example: true
                                    time:
                                      type: integer
                                      example: 5
                      course:
                        type: object
                        properties:
                          announcement:
                            type: string
                            example: null
                          finishedAt:
                            type: string
                            example: null
                          startedAt:
                            type: string
                            example: '28-01-2021 15:42:39 UTC'
                      locale:
                        type: string
                        example: 'es'

  /api/chapter/16/questions:
    get:
      tags:
        - 'VideoQuiz'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true
      responses:
        '200':
          description: "Devuelve las **preguntas** con sus respectivas opciones de respuesta.\n\nEl tiempo en la pregunta es opcional, si no hay un tiempo global se usa el de cada pregunta"
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  error:
                    type: boolean
                    example: false
                  data:
                    type: object
                    properties:
                      questions:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              example: 1
                            question:
                              type: string
                            imageUrl:
                              type: string
                            random:
                              type: boolean
                            time:
                              type: number
                              example: 60
                            triggerTime:
                              type: number
                              example: 20
                            answers:
                              type: array
                              items:
                                properties:
                                  id:
                                    type: integer
                                    example: 1
                                  answer:
                                    type: string
                      videoDuration:
                        type: number
                        example: 150
                      videoUrl:
                        type: string

  /api/chapter/16/update:
    post:
      tags:
        - 'VideoQuiz'
      parameters:
        - name: 'id'
          in: 'path'
          schema:
            type: integer
          description: 'ID del user_chapter'
          required: true

      requestBody:
        description: "Datos / progresos del juego que se quieren guardar. Ademas la llamada se ejecuta **cada 10 segundos** para enviar el **tiempo que lleva el usuario en el juego**. \n\nCuando termina el juego, dentro de data se envía: ```{finished: true}```"
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    answers:
                      type: array
                      items:
                        properties:
                          id:
                            type: integer
                            example: 1
                          questionId:
                            type: integer
                            example: 1
                          time:
                            type: integer
                            example: 3.0005
                          correct:
                            type: boolean
            examples:
              prueba1:
                summary: Al completar una acción
                value:
                  data:
                    answers: [{ id: 1, questionId: 1, time: 3.0005, correct: true }]
              prueba2:
                summary: Cada 10 segundos
                value:
                  time: 25
              prueba3:
                summary: Al terminar la prueba
                value:
                  finished: true
      responses:
        '200':
          description: "No devuelve nada excepto cuando se termina el juego y se le envia el ```{finished: true}```. \n\nEn este caso devuelve el **_mesanje_** que se muestra al usuario, los **_puntos_** obtenidos y si se muestra el **_tick de juego completado_** correctamente"
          content:
            application/json:
              examples:
                prueba1:
                  summary: 'Al terminar el juego'
                  value:
                    status: 200
                    error: false
                    data:
                      description: '<p><b>¡Enhorabuena!</b></p><p>Has superado el desafío.</p>'
                      score: 450
                      isCheck: false
                prueba2:
                  summary: 'Al completar una acción'
                  value:
                    status: 200
                    error: false
                    data: null
                prueba3:
                  summary: 'Cada 10 segundos'
                  value:
                    status: 200
                    error: false
                    data: null

module.exports = {
  root: true,
  env: { node: true },
  extends: ['plugin:vue/recommended', '@vue/airbnb'],
  plugins: ['prettier'],
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'max-len': 0,
    'import/extensions': ['warn', { js: 'never' }],
    'vue/no-v-html': 'off',
    'vue/html-self-closing': ['error', { html: { void: 'always' } }],
    'object-curly-newline': ['error', { consistent: true }],
    "linebreak-style": 0,
    // 'object-curly-newline': [
    //   'error',
    //   {
    //     ObjectExpression: { consistent: true },
    //     ObjectPattern: { consistent: true },
    //     ImportDeclaration: { consistent: true },
    //     ExportDeclaration: { multiline: true, minProperties: 3 },
    //   },
    // ],
    'vue/singleline-html-element-content-newline': 'off',
    'comma-dangle': ['error', 'only-multiline'],
  },
  parserOptions: { parser: 'babel-eslint' },
};
